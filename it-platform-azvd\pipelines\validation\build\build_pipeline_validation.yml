name: '🏗 Automated PR Build Validation it-platform-azvd ~ $(Date:yyyy-MM-dd HH-mm) UTC'


pr: none
trigger: none


resources:
  repositories:
  - repository: pipelines
    type      : git
    name      : it/it-devops-pipelines
    ref       : refs/heads/main


variables:
  - template: /params/config.yml

  
stages:
- template: /pipelines/01_stage/bicep_build.yml@pipelines
  parameters:
    bicepFolderList: ["workspace", "personal", "pooled"]
    bicepFolderLoop: true

- template: /pipelines/01_stage/bicep_test.yml@pipelines
  parameters:
    bicepFolderList: ["workspace", "personal", "pooled"]
    bicepFolderLoop: true


  
