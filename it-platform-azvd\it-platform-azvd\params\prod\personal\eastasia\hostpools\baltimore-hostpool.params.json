{"p_appGroups": {"value": [{"applicationGroupType": "Desktop", "description": "Azure Virtual Desktop Application Group for Baltimore - Personal.", "friendlyName": "JERAGM Application Group - Baltimore (Personal)", "kind": "Desktop", "hostpoolName": "baltimore-personal", "appgroupName": "baltimore-personal", "aadUserGroup": "AZVD Users - Baltimore"}]}, "p_hostPools": {"value": [{"customRdpProperty": "drivestoredirect:s:;audiomode:i:0;videoplaybackmode:i:1;redirectclipboard:i:0;redirectprinters:i:0;devicestoredirect:s:;redirectcomports:i:0;redirectsmartcards:i:0;usbdevicestoredirect:s:;enablecredsspsupport:i:1;use multimon:i:1;autoreconnection enabled:i:1;bandwidthautodetect:i:1;networkautodetect:i:1;compression:i:1;audiocapturemode:i:1;encode redirected video capture:i:1;redirected video capture encoding quality:i:2;camerastoredirect:s:*;selectedmonitors:s:;maximizetocurrentdisplays:i:1;singlemoninwindowedmode:i:1;screen mode id:i:1;smart sizing:i:1;dynamic resolution:i:1;desktopscalefactor:i:100;targetisaadjoined:i:1;", "description": "Azure Virtual Desktop Host Pool for Baltimore - Personal.", "friendlyName": "JERAGM Host Pool - Baltimore (Personal)", "hostPoolType": "Personal", "loadBalancerType": "Persistent", "maxSessionLimit": 1, "personalDesktopAssignmentType": "Direct", "preferredAppGroupType": "Desktop", "startVMOnConnect": true, "hostpoolName": "baltimore-personal", "hostpoolNameShort": "baltivd", "validationEnvironment": false, "aadUserGroup": "AZVD Users - Baltimore"}]}}