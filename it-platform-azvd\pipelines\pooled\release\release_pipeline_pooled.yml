name: '🚀 Bicep Release ${{ parameters.environment }} pooled ~ $(Date:yyyy-MM-dd HH-mm) UTC'


pr: none
trigger: none


resources:
  repositories:
  - repository: pipelines
    type      : git
    name      : it/it-devops-pipelines
    ref       : refs/heads/main


parameters:
- name: hostCount
  displayName: Enter the number of hosts to deploy, does not support decreasing existing number of hosts
  default: "1"
  type: string

- name: hostPool 
  displayName: Host Pool
  default: "rietlanden-pooled"  
  values:
  - "rietlanden-pooled"  
  - "rdp-pooled"
  - "lbdevsp-pooled"
  - "zema-testing-pooled"

- name: environment # The configuration files in the folder with the same name under ./params will be used
  displayName: Please select your environment
  default: "prod"
  values:
  - "prod"
  - "nonprod"

- name: location
  displayName: Please select your location
  default: "uksouth"
  values:
  - "uksouth"
  - "ukwest"
  - "eastasia"
  - "southeastasia"


variables:
  - template: /params/config.yml


stages:
- template: /pipelines/01_stage/bicep_build.yml@pipelines
  parameters:
    bicepFolder: "pooled"

- ${{ if eq(parameters.environment, 'prod') }}:
  - template: /pipelines/01_stage/bicep_test.yml@pipelines
    parameters:
      bicepFolder: "pooled"

- template: /pipelines/01_stage/bicep_release.yml@pipelines
  parameters:
    bicepFolder    : "pooled"
    # environment lifecycles
    environmentList: ["${{ parameters.environment }}"]
    inputParams    : "@{p_hostPoolName='${{ parameters.hostPool }}'},@{p_hostCount='${{ parameters.hostCount }}'},@{p_primaryRegion='${{ parameters.location }}'}"
    locationLoop   : true
    locationList   : 
      - ${{ if or(eq(parameters.location, 'uksouth'), eq(parameters.location, 'ukwest')) }}:
        - uksouth
        - ukwest
      - ${{ if or(eq(parameters.location, 'southeastasia'), eq(parameters.location, 'eastasia')) }}:
        - southeastasia
        - eastasia

- template: /pipelines/01_stage/post_config.yml
  parameters:
    environment   : ${{ parameters.environment }}
    primaryRegion : ${{ parameters.location }}
    locationList   : 
      - ${{ if or(eq(parameters.location, 'uksouth'), eq(parameters.location, 'ukwest')) }}:
        - uksouth
        - ukwest
      - ${{ if or(eq(parameters.location, 'southeastasia'), eq(parameters.location, 'eastasia')) }}:
        - southeastasia
        - eastasia

- ${{ if ne(parameters.environment, 'prod') }}:
  - template: /pipelines/01_stage/bicep_test.yml@pipelines
    parameters:
      bicepFolder: "pooled"
