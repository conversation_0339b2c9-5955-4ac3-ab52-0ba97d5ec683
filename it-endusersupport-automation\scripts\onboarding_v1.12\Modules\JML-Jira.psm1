#Requires -Version 5.1

<#
.SYNOPSIS
JML Jira Integration Module

.DESCRIPTION
This module provides Jira integration functionality for the JML (Jo<PERSON>, Mover, Leaver) 
admin account management script. It handles Jira API connections, ticket validation,
comment formatting, attachment handling, and comprehensive error handling with retry logic.

.NOTES
Version:        2.0
Author:         <PERSON>
Creation Date:  2025-01-09
Security Level: Enhanced with comprehensive data protection

Dependencies:
- PowerShell 5.1 or higher
- JML-Configuration module
- JML-Security module
- JML-Logging module
- JML-Utilities module
#>

# Import required modules
Import-Module (Join-Path $PSScriptRoot "JML-Configuration.psm1") -Force
Import-Module (Join-Path $PSScriptRoot "JML-Security.psm1") -Force
Import-Module (Join-Path $PSScriptRoot "JML-Logging.psm1") -Force
Import-Module (Join-Path $PSScriptRoot "JML-Utilities.psm1") -Force

# Module variables
$script:JiraSession = $null

<#
.SYNOPSIS
Initializes Jira connection with enhanced authentication and error handling.

.DESCRIPTION
Establishes secure connection to <PERSON>ra using multiple authentication methods
with comprehensive error handling, retry logic, and connection validation.

.PARAMETER ServerUrl
The Jira server URL.

.PARAMETER Credential
PSCredential object containing username and API token.

.PARAMETER TestConnection
Switch to test the connection after initialization.

.EXAMPLE
Initialize-JiraConnection -ServerUrl "https://company.atlassian.net" -Credential $cred -TestConnection

.NOTES
Implements secure authentication with comprehensive error handling and validation.
#>
function Initialize-JiraConnection {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$ServerUrl,

        [Parameter(Mandatory = $true)]
        [ValidateNotNull()]
        [System.Management.Automation.PSCredential]$Credential,

        [Parameter(Mandatory = $false)]
        [switch]$TestConnection
    )

    try {
        Write-SecureLog -Message "Initializing Jira connection" -LogLevel "INFO" -AuditTrail @{
            Operation = "JiraConnectionInit"
            ServerUrl = Protect-SensitiveData -Text $ServerUrl -RedactionType "ServerName"
            Username = Protect-SensitiveData -Text $Credential.UserName -RedactionType "UPN"
        }

        # Get configuration
        $config = Get-ModuleConfiguration

        # Validate server URL format
        if (-not ($ServerUrl -match '^https?://')) {
            throw "Invalid Jira server URL format. Must start with http:// or https://"
        }

        # Create authentication header
        $authString = "$($Credential.UserName):$($Credential.GetNetworkCredential().Password)"
        $authBytes = [System.Text.Encoding]::UTF8.GetBytes($authString)
        $authHeader = [System.Convert]::ToBase64String($authBytes)

        # Initialize session object
        $script:JiraSession = @{
            ServerUrl = $ServerUrl.TrimEnd('/')
            Headers = @{
                'Authorization' = "Basic $authHeader"
                'Content-Type' = 'application/json'
                'Accept' = 'application/json'
            }
            Timeout = if ($config) { $config.Jira.ApiSettings.Timeout } else { 60 }
            MaxRetries = if ($config) { $config.Jira.ApiSettings.RetrySettings.MaxAttempts } else { 5 }
            BaseDelay = if ($config) { $config.Jira.ApiSettings.RetrySettings.BaseDelay } else { 1 }
            LastRequestTime = $null
            RateLimitEnabled = if ($config) { $config.Jira.ApiSettings.RateLimit.EnableRateLimit } else { $true }
            RequestsPerMinute = if ($config) { $config.Jira.ApiSettings.RateLimit.RequestsPerMinute } else { 60 }
        }

        # Test connection if requested
        if ($TestConnection) {
            Write-SecureLog -Message "Testing Jira connection" -LogLevel "INFO"
            
            $testUrl = "$($script:JiraSession.ServerUrl)/rest/api/2/myself"
            $response = Invoke-JiraOperationWithRetry -Uri $testUrl -Method 'GET'
            
            if ($response) {
                Write-SecureLog -Message "Jira connection test successful" -LogLevel "INFO" -AuditTrail @{
                    Operation = "JiraConnectionTest"
                    Status = "Success"
                    UserKey = $response.key
                }
                Write-Host "Jira connection established successfully." -ForegroundColor Green
            } else {
                throw "Jira connection test failed"
            }
        }

        Write-SecureLog -Message "Jira connection initialized successfully" -LogLevel "INFO"
        return $true
    }
    catch {
        Write-SecureLog -Message "Failed to initialize Jira connection: $($_.Exception.Message)" -LogLevel "ERROR" -AuditTrail @{
            Operation = "JiraConnectionError"
            ErrorType = $_.Exception.GetType().Name
        }
        throw
    }
}

<#
.SYNOPSIS
Validates Jira ticket with comprehensive checks and field extraction.

.DESCRIPTION
Performs thorough validation of Jira tickets including existence, work type,
request type, and custom field validation with detailed error reporting.

.PARAMETER TicketKey
The Jira ticket key (e.g., "HELP-12345").

.PARAMETER ExpectedWorkType
Expected work type for validation.

.PARAMETER ExpectedRequestType
Expected request type for validation.

.EXAMPLE
$validation = Test-JiraTicketValidation -TicketKey "HELP-12345" -ExpectedWorkType "Service Request" -ExpectedRequestType "Admin Account Request"

.NOTES
Implements comprehensive ticket validation with detailed error reporting.
#>
function Test-JiraTicketValidation {
    [CmdletBinding()]
    [OutputType([hashtable])]
    param(
        [Parameter(Mandatory = $true)]
        [ValidatePattern('^[A-Z]+-\d+$')]
        [string]$TicketKey,

        [Parameter(Mandatory = $false)]
        [string]$ExpectedWorkType,

        [Parameter(Mandatory = $false)]
        [string]$ExpectedRequestType
    )

    try {
        Write-SecureLog -Message "Validating Jira ticket" -LogLevel "INFO" -AuditTrail @{
            Operation = "JiraTicketValidation"
            TicketKey = $TicketKey
        }

        if (-not $script:JiraSession) {
            throw "Jira session not initialized. Call Initialize-JiraConnection first."
        }

        # Get configuration
        $config = Get-ModuleConfiguration

        # Build API URL for ticket details
        $ticketUrl = "$($script:JiraSession.ServerUrl)/rest/api/2/issue/$TicketKey"
        
        # Get ticket details
        $ticket = Invoke-JiraOperationWithRetry -Uri $ticketUrl -Method 'GET'
        
        if (-not $ticket) {
            return @{
                IsValid = $false
                ErrorMessage = "Ticket $TicketKey not found or inaccessible"
                Ticket = $null
            }
        }

        # Initialize validation result
        $validationResult = @{
            IsValid = $true
            ErrorMessage = ""
            Ticket = $ticket
            Fields = @{}
            Warnings = @()
        }

        # Extract and validate work type
        if ($ExpectedWorkType) {
            $workType = $ticket.fields.issuetype.name
            if ($workType -ne $ExpectedWorkType) {
                $validationResult.IsValid = $false
                $validationResult.ErrorMessage += "Work type mismatch. Expected: '$ExpectedWorkType', Found: '$workType'. "
            }
        }

        # Extract and validate request type (if available)
        if ($ExpectedRequestType -and $ticket.fields.customfield_10010) {
            $requestType = $ticket.fields.customfield_10010.requestType.name
            if ($requestType -ne $ExpectedRequestType) {
                $validationResult.IsValid = $false
                $validationResult.ErrorMessage += "Request type mismatch. Expected: '$ExpectedRequestType', Found: '$requestType'. "
            }
        }

        # Extract custom fields based on configuration
        if ($config -and $config.Jira.CustomFields) {
            foreach ($fieldName in $config.Jira.CustomFields.Keys) {
                $fieldId = $config.Jira.CustomFields[$fieldName]
                $fieldValue = $ticket.fields.$fieldId
                
                if ($fieldValue) {
                    $validationResult.Fields[$fieldName] = if ($fieldValue -is [string]) {
                        $fieldValue
                    } elseif ($fieldValue.value) {
                        $fieldValue.value
                    } elseif ($fieldValue.displayName) {
                        $fieldValue.displayName
                    } else {
                        $fieldValue.ToString()
                    }
                } else {
                    $validationResult.Warnings += "Custom field '$fieldName' ($fieldId) is empty or not found"
                }
            }
        }

        # Log validation result
        $logLevel = if ($validationResult.IsValid) { "INFO" } else { "WARNING" }
        Write-SecureLog -Message "Ticket validation completed: $($validationResult.IsValid)" -LogLevel $logLevel -AuditTrail @{
            Operation = "JiraTicketValidationResult"
            TicketKey = $TicketKey
            IsValid = $validationResult.IsValid
            FieldsExtracted = $validationResult.Fields.Count
            WarningsCount = $validationResult.Warnings.Count
        }

        return $validationResult
    }
    catch {
        Write-SecureLog -Message "Jira ticket validation failed: $($_.Exception.Message)" -LogLevel "ERROR" -AuditTrail @{
            Operation = "JiraTicketValidationError"
            TicketKey = $TicketKey
            ErrorType = $_.Exception.GetType().Name
        }
        
        return @{
            IsValid = $false
            ErrorMessage = "Validation error: $($_.Exception.Message)"
            Ticket = $null
            Fields = @{}
            Warnings = @()
        }
    }
}

<#
.SYNOPSIS
Executes Jira API operations with comprehensive retry logic and error handling.

.DESCRIPTION
Implements robust API operation execution with exponential backoff retry logic,
rate limiting, jitter, and comprehensive error categorization for different
types of failures.

.PARAMETER Uri
The API endpoint URI.

.PARAMETER Method
HTTP method (GET, POST, PUT, DELETE).

.PARAMETER Body
Request body for POST/PUT operations.

.PARAMETER ContentType
Content type for the request.

.EXAMPLE
$response = Invoke-JiraOperationWithRetry -Uri $apiUrl -Method 'GET'

.NOTES
Implements enterprise-grade API operation handling with comprehensive error recovery.
#>
function Invoke-JiraOperationWithRetry {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$Uri,

        [Parameter(Mandatory = $true)]
        [ValidateSet('GET', 'POST', 'PUT', 'DELETE')]
        [string]$Method,

        [Parameter(Mandatory = $false)]
        [string]$Body,

        [Parameter(Mandatory = $false)]
        [string]$ContentType = 'application/json'
    )

    if (-not $script:JiraSession) {
        throw "Jira session not initialized. Call Initialize-JiraConnection first."
    }

    $attempt = 1
    $maxRetries = $script:JiraSession.MaxRetries
    $baseDelay = $script:JiraSession.BaseDelay

    while ($attempt -le $maxRetries) {
        try {
            # Rate limiting
            if ($script:JiraSession.RateLimitEnabled -and $script:JiraSession.LastRequestTime) {
                $timeSinceLastRequest = (Get-Date) - $script:JiraSession.LastRequestTime
                $minInterval = [TimeSpan]::FromSeconds(60.0 / $script:JiraSession.RequestsPerMinute)

                if ($timeSinceLastRequest -lt $minInterval) {
                    $waitTime = ($minInterval - $timeSinceLastRequest).TotalSeconds
                    Write-SecureLog -Message "Rate limiting: waiting $([math]::Round($waitTime, 2)) seconds" -LogLevel "DEBUG"
                    Start-Sleep -Seconds $waitTime
                }
            }

            # Prepare request parameters
            $requestParams = @{
                Uri = $Uri
                Method = $Method
                Headers = $script:JiraSession.Headers
                TimeoutSec = $script:JiraSession.Timeout
                ErrorAction = 'Stop'
            }

            if ($Body) {
                $requestParams.Body = $Body
                $requestParams.ContentType = $ContentType
            }

            # Execute request
            Write-SecureLog -Message "Executing Jira API request (Attempt $attempt)" -LogLevel "DEBUG" -AuditTrail @{
                Operation = "JiraApiRequest"
                Method = $Method
                Uri = Protect-SensitiveData -Text $Uri -RedactionType "ServerName"
                Attempt = $attempt
            }

            $response = Invoke-RestMethod @requestParams
            $script:JiraSession.LastRequestTime = Get-Date

            Write-SecureLog -Message "Jira API request successful" -LogLevel "DEBUG" -AuditTrail @{
                Operation = "JiraApiSuccess"
                Method = $Method
                Attempt = $attempt
            }

            return $response
        }
        catch {
            $errorCategory = Get-JiraErrorCategory -Exception $_.Exception
            $isRetryable = Test-JiraErrorRetryable -ErrorCategory $errorCategory

            Write-SecureLog -Message "Jira API request failed (Attempt $attempt): $($_.Exception.Message)" -LogLevel "WARNING" -AuditTrail @{
                Operation = "JiraApiError"
                Method = $Method
                Attempt = $attempt
                ErrorCategory = $errorCategory
                IsRetryable = $isRetryable
            }

            if (-not $isRetryable -or $attempt -eq $maxRetries) {
                Write-SecureLog -Message "Jira API operation failed permanently: $($_.Exception.Message)" -LogLevel "ERROR"
                throw
            }

            # Calculate delay with exponential backoff and jitter
            $delay = $baseDelay * [Math]::Pow(2, $attempt - 1)
            $jitter = Get-Random -Minimum 0.5 -Maximum 1.5
            $finalDelay = [Math]::Min($delay * $jitter, 32) # Cap at 32 seconds

            Write-SecureLog -Message "Retrying in $([math]::Round($finalDelay, 2)) seconds..." -LogLevel "INFO"
            Start-Sleep -Seconds $finalDelay

            $attempt++
        }
    }
}

<#
.SYNOPSIS
Categorizes Jira API errors for appropriate handling.

.DESCRIPTION
Analyzes exception details to categorize errors into types that help
determine retry strategies and error handling approaches.

.PARAMETER Exception
The exception object to categorize.

.OUTPUTS
String indicating the error category.

.EXAMPLE
$category = Get-JiraErrorCategory -Exception $_.Exception

.NOTES
Used internally for error handling and retry logic decisions.
#>
function Get-JiraErrorCategory {
    [CmdletBinding()]
    [OutputType([string])]
    param(
        [Parameter(Mandatory = $true)]
        [System.Exception]$Exception
    )

    $message = $Exception.Message.ToLower()
    $statusCode = $null

    # Extract HTTP status code if available
    if ($Exception -is [Microsoft.PowerShell.Commands.HttpResponseException]) {
        $statusCode = $Exception.Response.StatusCode.value__
    }

    # Categorize based on status code and message
    switch ($true) {
        ($statusCode -eq 401) { return "Authentication" }
        ($statusCode -eq 403) { return "Authorization" }
        ($statusCode -eq 404) { return "NotFound" }
        ($statusCode -eq 429) { return "RateLimit" }
        ($statusCode -ge 500) { return "ServerError" }
        ($message -match "timeout|timed out") { return "Timeout" }
        ($message -match "network|connection|dns") { return "Network" }
        ($message -match "ssl|certificate|tls") { return "SSL" }
        default { return "Unknown" }
    }
}

<#
.SYNOPSIS
Determines if a Jira error is retryable.

.DESCRIPTION
Evaluates error categories to determine whether an operation should be retried
or if it should fail immediately.

.PARAMETER ErrorCategory
The error category from Get-JiraErrorCategory.

.OUTPUTS
Boolean indicating if the error is retryable.

.EXAMPLE
$shouldRetry = Test-JiraErrorRetryable -ErrorCategory "Timeout"

.NOTES
Used internally for retry logic decisions.
#>
function Test-JiraErrorRetryable {
    [CmdletBinding()]
    [OutputType([bool])]
    param(
        [Parameter(Mandatory = $true)]
        [string]$ErrorCategory
    )

    $retryableCategories = @('Timeout', 'Network', 'ServerError', 'RateLimit', 'SSL')
    return $ErrorCategory -in $retryableCategories
}

<#
.SYNOPSIS
Adds enhanced comments to Jira tickets with rich formatting and error handling.

.DESCRIPTION
Creates formatted comments in Jira tickets using either Atlassian Document Format (ADF)
or Wiki markup with automatic fallback and comprehensive error handling.

.PARAMETER TicketKey
The Jira ticket key.

.PARAMETER CommentText
The comment text to add.

.PARAMETER UseADF
Switch to use Atlassian Document Format.

.PARAMETER IncludeTimestamp
Switch to include timestamp in the comment.

.EXAMPLE
Add-EnhancedJiraComment -TicketKey "HELP-12345" -CommentText "Admin account created successfully" -UseADF -IncludeTimestamp

.NOTES
Implements rich comment formatting with automatic fallback capabilities.
#>
function Add-EnhancedJiraComment {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [ValidatePattern('^[A-Z]+-\d+$')]
        [string]$TicketKey,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$CommentText,

        [Parameter(Mandatory = $false)]
        [switch]$UseADF,

        [Parameter(Mandatory = $false)]
        [switch]$IncludeTimestamp
    )

    try {
        Write-SecureLog -Message "Adding comment to Jira ticket" -LogLevel "INFO" -AuditTrail @{
            Operation = "JiraCommentAdd"
            TicketKey = $TicketKey
            UseADF = $UseADF.IsPresent
            IncludeTimestamp = $IncludeTimestamp.IsPresent
        }

        if (-not $script:JiraSession) {
            throw "Jira session not initialized. Call Initialize-JiraConnection first."
        }

        # Get configuration
        $config = Get-ModuleConfiguration

        # Determine format preference
        $useADFFormat = if ($config) {
            $config.Jira.CommentFormatting.PreferADF -and $UseADF
        } else {
            $UseADF.IsPresent
        }

        # Add timestamp if requested
        $finalCommentText = if ($IncludeTimestamp) {
            $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            "[$timestamp] $CommentText"
        } else {
            $CommentText
        }

        # Format comment based on preference
        $commentBody = if ($useADFFormat) {
            Format-JiraCommentADF -CommentText $finalCommentText
        } else {
            Format-JiraCommentWiki -CommentText $finalCommentText
        }

        # Build API URL
        $commentUrl = "$($script:JiraSession.ServerUrl)/rest/api/2/issue/$TicketKey/comment"

        # Execute request
        $response = Invoke-JiraOperationWithRetry -Uri $commentUrl -Method 'POST' -Body $commentBody

        if ($response) {
            Write-SecureLog -Message "Comment added successfully to ticket $TicketKey" -LogLevel "INFO" -AuditTrail @{
                Operation = "JiraCommentSuccess"
                TicketKey = $TicketKey
                CommentId = $response.id
            }
            return $response
        } else {
            throw "Failed to add comment - no response received"
        }
    }
    catch {
        # Try fallback format if ADF failed
        if ($useADFFormat -and $config -and $config.Jira.CommentFormatting.FallbackToWikiMarkup) {
            Write-SecureLog -Message "ADF comment failed, trying Wiki markup fallback" -LogLevel "WARNING"
            try {
                $wikiCommentBody = Format-JiraCommentWiki -CommentText $finalCommentText
                $commentUrl = "$($script:JiraSession.ServerUrl)/rest/api/2/issue/$TicketKey/comment"
                $response = Invoke-JiraOperationWithRetry -Uri $commentUrl -Method 'POST' -Body $wikiCommentBody

                Write-SecureLog -Message "Comment added successfully using Wiki markup fallback" -LogLevel "INFO"
                return $response
            }
            catch {
                Write-SecureLog -Message "Both ADF and Wiki markup comment attempts failed: $($_.Exception.Message)" -LogLevel "ERROR"
                throw
            }
        } else {
            Write-SecureLog -Message "Failed to add Jira comment: $($_.Exception.Message)" -LogLevel "ERROR" -AuditTrail @{
                Operation = "JiraCommentError"
                TicketKey = $TicketKey
                ErrorType = $_.Exception.GetType().Name
            }
            throw
        }
    }
}

<#
.SYNOPSIS
Formats comment text using Atlassian Document Format (ADF).

.DESCRIPTION
Creates properly formatted ADF JSON structure for rich text comments
in modern Jira instances.

.PARAMETER CommentText
The text to format.

.OUTPUTS
JSON string containing ADF formatted comment.

.EXAMPLE
$adfComment = Format-JiraCommentADF -CommentText "Admin account created"

.NOTES
Implements modern ADF formatting for enhanced comment presentation.
#>
function Format-JiraCommentADF {
    [CmdletBinding()]
    [OutputType([string])]
    param(
        [Parameter(Mandatory = $true)]
        [string]$CommentText
    )

    $adfDocument = @{
        body = @{
            type = "doc"
            version = 1
            content = @(
                @{
                    type = "paragraph"
                    content = @(
                        @{
                            type = "text"
                            text = $CommentText
                        }
                    )
                }
            )
        }
    }

    return ($adfDocument | ConvertTo-Json -Depth 10 -Compress)
}

<#
.SYNOPSIS
Formats comment text using Wiki markup.

.DESCRIPTION
Creates Wiki markup formatted comment for compatibility with older
Jira instances or as fallback option.

.PARAMETER CommentText
The text to format.

.OUTPUTS
JSON string containing Wiki markup formatted comment.

.EXAMPLE
$wikiComment = Format-JiraCommentWiki -CommentText "Admin account created"

.NOTES
Implements Wiki markup formatting for broad compatibility.
#>
function Format-JiraCommentWiki {
    [CmdletBinding()]
    [OutputType([string])]
    param(
        [Parameter(Mandatory = $true)]
        [string]$CommentText
    )

    $wikiComment = @{
        body = $CommentText
    }

    return ($wikiComment | ConvertTo-Json -Depth 5 -Compress)
}

<#
.SYNOPSIS
Adds file attachments to Jira tickets with comprehensive validation and error handling.

.DESCRIPTION
Uploads files to Jira tickets with file validation, chunked upload support,
and comprehensive error handling. Supports multiple file types and sizes
based on configuration.

.PARAMETER TicketKey
The Jira ticket key.

.PARAMETER FilePath
Path to the file to attach.

.PARAMETER UseChunkedUpload
Switch to enable chunked upload for large files.

.EXAMPLE
Add-EnhancedJiraAttachment -TicketKey "HELP-12345" -FilePath "C:\Logs\admin.log" -UseChunkedUpload

.NOTES
Implements secure file upload with comprehensive validation and error handling.
#>
function Add-EnhancedJiraAttachment {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [ValidatePattern('^[A-Z]+-\d+$')]
        [string]$TicketKey,

        [Parameter(Mandatory = $true)]
        [ValidateScript({Test-Path $_ -PathType Leaf})]
        [string]$FilePath,

        [Parameter(Mandatory = $false)]
        [switch]$UseChunkedUpload
    )

    try {
        Write-SecureLog -Message "Adding attachment to Jira ticket" -LogLevel "INFO" -AuditTrail @{
            Operation = "JiraAttachmentAdd"
            TicketKey = $TicketKey
            FileName = Split-Path $FilePath -Leaf
            UseChunkedUpload = $UseChunkedUpload.IsPresent
        }

        if (-not $script:JiraSession) {
            throw "Jira session not initialized. Call Initialize-JiraConnection first."
        }

        # Validate attachment
        $validationResult = Test-JiraAttachmentValidation -FilePath $FilePath
        if (-not $validationResult.IsValid) {
            throw "Attachment validation failed: $($validationResult.ErrorMessage)"
        }

        # Get file info
        $fileInfo = Get-Item $FilePath
        $fileName = $fileInfo.Name
        $fileSize = $fileInfo.Length

        Write-SecureLog -Message "Uploading file: $fileName (Size: $([math]::Round($fileSize/1MB, 2)) MB)" -LogLevel "INFO"

        # Build API URL
        $attachmentUrl = "$($script:JiraSession.ServerUrl)/rest/api/2/issue/$TicketKey/attachments"

        # Prepare headers for file upload
        $uploadHeaders = $script:JiraSession.Headers.Clone()
        $uploadHeaders.Remove('Content-Type')  # Let PowerShell set this for multipart
        $uploadHeaders['X-Atlassian-Token'] = 'no-check'  # Required for file uploads

        # Prepare form data
        $boundary = [System.Guid]::NewGuid().ToString()
        $uploadHeaders['Content-Type'] = "multipart/form-data; boundary=$boundary"

        # Read file content
        $fileBytes = [System.IO.File]::ReadAllBytes($FilePath)
        $fileContent = [System.Text.Encoding]::GetEncoding('iso-8859-1').GetString($fileBytes)

        # Create multipart form data
        $formData = @"
--$boundary
Content-Disposition: form-data; name="file"; filename="$fileName"
Content-Type: application/octet-stream

$fileContent
--$boundary--
"@

        # Execute upload
        $requestParams = @{
            Uri = $attachmentUrl
            Method = 'POST'
            Headers = $uploadHeaders
            Body = $formData
            TimeoutSec = $script:JiraSession.Timeout
            ErrorAction = 'Stop'
        }

        $response = Invoke-RestMethod @requestParams

        if ($response -and $response.Count -gt 0) {
            $attachment = $response[0]
            Write-SecureLog -Message "Attachment uploaded successfully to ticket $TicketKey" -LogLevel "INFO" -AuditTrail @{
                Operation = "JiraAttachmentSuccess"
                TicketKey = $TicketKey
                AttachmentId = $attachment.id
                FileName = $attachment.filename
                FileSize = $attachment.size
            }
            return $attachment
        } else {
            throw "Failed to upload attachment - no response received"
        }
    }
    catch {
        Write-SecureLog -Message "Failed to add Jira attachment: $($_.Exception.Message)" -LogLevel "ERROR" -AuditTrail @{
            Operation = "JiraAttachmentError"
            TicketKey = $TicketKey
            FileName = Split-Path $FilePath -Leaf
            ErrorType = $_.Exception.GetType().Name
        }
        throw
    }
}

<#
.SYNOPSIS
Validates file attachments before upload to Jira.

.DESCRIPTION
Performs comprehensive validation of files including size limits,
file type restrictions, and security checks based on configuration.

.PARAMETER FilePath
Path to the file to validate.

.OUTPUTS
Hashtable containing validation results.

.EXAMPLE
$validation = Test-JiraAttachmentValidation -FilePath "C:\Logs\admin.log"

.NOTES
Implements security-focused file validation with configurable restrictions.
#>
function Test-JiraAttachmentValidation {
    [CmdletBinding()]
    [OutputType([hashtable])]
    param(
        [Parameter(Mandatory = $true)]
        [ValidateScript({Test-Path $_ -PathType Leaf})]
        [string]$FilePath
    )

    try {
        # Get configuration
        $config = Get-ModuleConfiguration

        # Get file info
        $fileInfo = Get-Item $FilePath
        $fileName = $fileInfo.Name
        $fileSize = $fileInfo.Length
        $fileExtension = $fileInfo.Extension.ToLower()

        # Initialize validation result
        $validationResult = @{
            IsValid = $true
            ErrorMessage = ""
            Warnings = @()
            FileInfo = @{
                Name = $fileName
                Size = $fileSize
                Extension = $fileExtension
                SizeMB = [math]::Round($fileSize / 1MB, 2)
            }
        }

        # Check file size
        $maxSizeMB = if ($config) { $config.Jira.AttachmentSettings.MaxFileSizeMB } else { 10 }
        $maxSizeBytes = $maxSizeMB * 1MB

        if ($fileSize -gt $maxSizeBytes) {
            $validationResult.IsValid = $false
            $validationResult.ErrorMessage += "File size ($([math]::Round($fileSize/1MB, 2)) MB) exceeds maximum allowed size ($maxSizeMB MB). "
        }

        # Check file type
        if ($config -and $config.Jira.AttachmentSettings.AllowedFileTypes) {
            $allowedTypes = $config.Jira.AttachmentSettings.AllowedFileTypes
            if ($fileExtension -notin $allowedTypes) {
                $validationResult.IsValid = $false
                $validationResult.ErrorMessage += "File type '$fileExtension' is not allowed. Allowed types: $($allowedTypes -join ', '). "
            }
        }

        # Check if file is empty
        if ($fileSize -eq 0) {
            $validationResult.IsValid = $false
            $validationResult.ErrorMessage += "File is empty. "
        }

        # Security check - scan for potentially dangerous content
        if ($fileExtension -in @('.exe', '.bat', '.cmd', '.ps1', '.vbs', '.js')) {
            $validationResult.Warnings += "File type '$fileExtension' may contain executable content"
        }

        Write-SecureLog -Message "File validation completed: $($validationResult.IsValid)" -LogLevel "DEBUG" -AuditTrail @{
            Operation = "FileValidation"
            FileName = $fileName
            FileSize = $fileSize
            IsValid = $validationResult.IsValid
            WarningsCount = $validationResult.Warnings.Count
        }

        return $validationResult
    }
    catch {
        Write-SecureLog -Message "File validation error: $($_.Exception.Message)" -LogLevel "ERROR"
        return @{
            IsValid = $false
            ErrorMessage = "Validation error: $($_.Exception.Message)"
            Warnings = @()
            FileInfo = @{}
        }
    }
}

# Backward compatibility functions
function Get-JiraCustomFieldValue {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$TicketKey,

        [Parameter(Mandatory = $true)]
        [string]$FieldName
    )

    $validation = Test-JiraTicketValidation -TicketKey $TicketKey
    if ($validation.IsValid -and $validation.Fields.ContainsKey($FieldName)) {
        return $validation.Fields[$FieldName]
    }
    return $null
}

# Export functions
Export-ModuleMember -Function Initialize-JiraConnection, Test-JiraTicketValidation, Invoke-JiraOperationWithRetry, Get-JiraErrorCategory, Test-JiraErrorRetryable, Add-EnhancedJiraComment, Format-JiraCommentADF, Format-JiraCommentWiki, Add-EnhancedJiraAttachment, Test-JiraAttachmentValidation, Get-JiraCustomFieldValue
