parameters:
  appName       : ""
  appVersion    : ""
  svcConnection : ""

steps:

- task: AzureCLI@2
  displayName: "Create Intune package"
  timeoutInMinutes: 30
  inputs:
    addSpnToEnvironment            : true
    azureSubscription              : ${{ parameters.svcConnection }}
    powerShellErrorActionPreference: stop
    scriptLocation                 : scriptPath
    scriptPath                     : $(System.DefaultWorkingDirectory)/scripts/buildApp.ps1
    scriptType                     : ps
    workingDirectory               : $(System.DefaultWorkingDirectory)
    arguments: 
      -appName                     "${{ parameters.appName }}"
      -appVersion                  "${{ parameters.appVersion }}"
      -storageAccount              $(storageAccount)
      -storageSAS                  $(storageSAS)

- task: CopyFiles@2
  displayName: 'Copy intunewin file'
  inputs:
    contents    : '$(System.DefaultWorkingDirectory)/applications/output/**'
    targetFolder: '$(Build.ArtifactStagingDirectory)'

- publish: '$(Build.ArtifactStagingDirectory)/applications/output'
  displayName: 'Publish .intunewin package'
  artifact   : intunewin

- task: AzureCLI@2
  displayName: "Upload App"
  timeoutInMinutes: 30
  inputs:
    addSpnToEnvironment            : true
    azureSubscription              : ${{ parameters.svcConnection }}
    powerShellErrorActionPreference: stop
    scriptLocation                 : scriptPath
    scriptPath                     : $(System.DefaultWorkingDirectory)/scripts/deployApp.ps1
    scriptType                     : ps
    workingDirectory               : $(System.DefaultWorkingDirectory)
    arguments: 
      -appName                     "${{ parameters.appName }}"
      -storageAccount              $(storageAccount)
      -storageSAS                  $(storageSAS)

- task: AzureCLI@2
  displayName: "Dependency App"
  timeoutInMinutes: 30
  inputs:
    addSpnToEnvironment            : true
    azureSubscription              : ${{ parameters.svcConnection }}
    powerShellErrorActionPreference: stop
    scriptLocation                 : scriptPath
    scriptPath                     : $(System.DefaultWorkingDirectory)/scripts/dependencyApp.ps1
    scriptType                     : ps
    workingDirectory               : $(System.DefaultWorkingDirectory)
    arguments: 
      -appName                     "${{ parameters.appName }}"
      -appVersion                  "${{ parameters.appVersion }}"

- task: AzureCLI@2
  displayName: "Supersede App"
  timeoutInMinutes: 30
  inputs:
    addSpnToEnvironment            : true
    azureSubscription              : ${{ parameters.svcConnection }}
    powerShellErrorActionPreference: stop
    scriptLocation                 : scriptPath
    scriptPath                     : $(System.DefaultWorkingDirectory)/scripts/supersedeApp.ps1
    scriptType                     : ps
    workingDirectory               : $(System.DefaultWorkingDirectory)
    arguments: 
      -appName                     "${{ parameters.appName }}"
      -appVersion                  "${{ parameters.appVersion }}"

- task: AzureCLI@2
  displayName: "Assign App"
  timeoutInMinutes: 30
  inputs:
    addSpnToEnvironment            : true
    azureSubscription              : ${{ parameters.svcConnection }}
    powerShellErrorActionPreference: stop
    scriptLocation                 : scriptPath
    scriptPath                     : $(System.DefaultWorkingDirectory)/scripts/assignApp.ps1
    scriptType                     : ps
    workingDirectory               : $(System.DefaultWorkingDirectory)
    arguments: 
      -appName                      "${{ parameters.appName }}"
      -appVersion                   "${{ parameters.appVersion }}"