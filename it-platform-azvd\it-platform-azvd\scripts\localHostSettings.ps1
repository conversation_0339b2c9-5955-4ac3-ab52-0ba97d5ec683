# [CmdletBinding()]
param (
    [Parameter(Mandatory=$false)]
    [ValidateNotNullOrEmpty()]
    [string]$primaryStorageAccount,

    [Parameter(Mandatory=$false)]
    [ValidateNotNullOrEmpty()]
    [string]$secondaryStorageAccount,

    [Parameter(Mandatory=$false)]
    [ValidateNotNullOrEmpty()]
    [string]$containerName,

    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$fslogixEnabled
)

# Wait for the VM to be ready
Start-Sleep -Seconds "300"
Start-Transcript -Path "C:\transcripts\localHostSettings.txt" 

$fslogixPath = "HKLM:\Software\FSLogix\Profiles"
# Check the FSLogix path contains an entry named 'Enabled'
if (((Get-ItemProperty $fslogixPath).Property -Contains 'Enabled') -or ($fslogixEnabled -eq $false)) {
    Write-Output "FS Logix Profiles already enabled, nothing to do 🎉"
}
else {
    Write-Output "Creating FS Logix Profiles... ✨"
    New-Item -Path $fslogixPath -Force
    # Set the FSLogix registry keys based on best practices:
    # https://learn.microsoft.com/en-us/fslogix/tutorial-cloud-cache-containers#profile-container-configuration-with-cloud-cache
    New-ItemProperty -Path $fslogixPath -Name Enabled -Value 1 -PropertyType DWORD -Force
    New-ItemProperty -path $fslogixPath -Name CCDLocations -Value "type=smb,connectionString=\\$primaryStorageAccount.file.core.windows.net\$containerName;type=smb,connectionString=\\$secondaryStorageAccount.file.core.windows.net\$containerName" -PropertyType MultiString -Force
    New-ItemProperty -Path $fslogixPath -Name DeleteLocalProfileWhenVHDShouldApply -Value 1 -PropertyType DWORD -Force
    New-ItemProperty -Path $fslogixPath -Name ClearCacheOnLogoff -Value 1 -PropertyType DWORD -Force
    New-ItemProperty -Path $fslogixPath -Name FlipFlopProfileDirectoryName -Value 1 -PropertyType DWORD -Force
    New-ItemProperty -Path $fslogixPath -Name HealthyProvidersRequiredForRegister -Value 1 -PropertyType DWORD -Force
    New-ItemProperty -Path $fslogixPath -Name LockedRetryCount -Value 3 -PropertyType DWORD -Force
    New-ItemProperty -Path $fslogixPath -Name LockedRetryInterval -Value 15 -PropertyType DWORD -Force
    New-ItemProperty -Path $fslogixPath -Name ProfileType -Value 0 -PropertyType DWORD -Force
    New-ItemProperty -Path $fslogixPath -Name ReAttachIntervalSeconds -Value 15 -PropertyType DWORD -Force
    New-ItemProperty -Path $fslogixPath -Name ReAttachRetryCount -Value 3 -PropertyType DWORD -Force
    New-ItemProperty -path $fslogixPath -Name VolumeType -Value "VHDX" -PropertyType String -Force

    # Enable Kerberos Cloud Tickets
    New-ItemProperty -Path HKLM:\SYSTEM\CurrentControlSet\Control\Lsa\Kerberos\Parameters -Name CloudKerberosTicketRetrievalEnabled -PropertyType DWORD -Value 1 -Force
    if(Test-Path -Path "HKLM:\Software\Policies\Microsoft\AzureADAccount") {
        Write-Output "AzureADAccount path found 🎉"
        New-ItemProperty -Path HKLM:\Software\Policies\Microsoft\AzureADAccount -Name LoadCredKeyFromProfile -PropertyType DWORD -Value 1 -Force # Store Credkey in the FSLogix profile
    }
    else {
        Write-Output "Creating AzureADAccount... ✨"
        New-Item         -Path HKLM:\Software\Policies\Microsoft\AzureADAccount
        New-ItemProperty -Path HKLM:\Software\Policies\Microsoft\AzureADAccount -Name LoadCredKeyFromProfile -PropertyType DWORD -Value 1 -Force # Store Credkey in the FSLogix profile
    }
}

$hostname = hostname

if ($hostname -like "*uk*") {
    #sets local time to GMT
    Set-TimeZone -Name 'GMT Standard Time' -PassThru
} else {
    Write-Host "Not UK, so not setting timezone"
}