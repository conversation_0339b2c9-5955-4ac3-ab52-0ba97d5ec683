parameters:
  environment   : ""
  primaryRegion : ""
  locationList  : []

# Loop
stages:
- stage: PostDeploy
  displayName: Post Deploy
  #dependsOn: ${{ parameters.environment }}_Release_${{ location }}
  jobs:
  - ${{ each location in parameters.locationList }}:
    - template: /pipelines/02_job/post_config.yml
      parameters:
        location      : ${{ location }}
        environment   : ${{ parameters.environment }}
        primaryRegion : ${{ parameters.primaryRegion }}
