{"p_tags": {"value": {"Billing Owner": "<PERSON><PERSON>", "Business Unit": "IT Operations", "Environment": "Prod", "Location": "Southeast Asia"}}, "p_location": {"value": "southeastasia"}, "p_environment": {"value": "prod"}, "p_serviceCode": {"value": "ssv"}, "p_workspace": {"value": {"description": "Azure Virtual Desktop Workspace for Southeast Asia", "friendlyName": "JERAGM Workspace - Southeast Asia"}}, "p_storageAccount": {"value": {"name": "azvdfslogix", "kind": "StorageV2", "sku": "Standard_LRS", "allowBlobPublicAccess": true, "minimumTlsVersion": "TLS1_2", "supportsHttpsTrafficOnly": true, "allowSharedKeyAccess": true, "defaultAction": "<PERSON><PERSON>", "kerberosSettings": {"directoryServiceOptions": "AADKERB", "activeDirectoryProperties": {"domainName": "JERAGM.COM", "netBiosDomainName": "JERAGM.COM", "forestName": "JERAGM.COM", "domainGuid": "d395ab08-1197-4c2e-a562-080bfa876bdd", "domainSid": "S-1-5-21-*********-**********-**********", "azureStorageSid": "S-1-5-21-*********-**********-**********-*********"}}, "virtualNetworkRules": [{"id": "/subscriptions/ae5f66bb-32d2-4ad2-b092-da9ee89dfc08/resourceGroups/sea-prd-ssv-networking-rg/providers/Microsoft.Network/virtualNetworks/sea-prd-ssv-spoke-vnet/subnets/azure-virtual-desktop-snet", "action": "Allow"}, {"id": "/subscriptions/866d0d3c-3374-4261-bc24-2d0bdc2ca1a1/resourceGroups/eaa-prd-ssv-networking-rg/providers/Microsoft.Network/virtualNetworks/eaa-prd-ssv-spoke-vnet/subnets/azure-virtual-desktop-snet", "action": "Allow"}], "ipRules": [{"value": "***************", "action": "Allow"}, {"value": "*************", "action": "Allow"}, {"value": "**************", "action": "Allow"}, {"value": "*************", "action": "Allow"}, {"value": "*************", "action": "Allow"}]}}}