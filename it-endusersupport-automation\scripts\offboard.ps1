﻿# Import required modules
Import-Module ActiveDirectory

# Sends an email notification with details about the admin account creation
function Send-EmailNotification {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$LogPath,

        [Parameter(Mandatory = $true)]
        [string]$AdminUserUPN,

        [Parameter(Mandatory = $true)]
        [string]$Subject,

        [Parameter(Mandatory = $false)]
        [bool]$IsAccountDisabled = $false,

        [Parameter(Mandatory = $false)]
        [bool]$IsExpirySet = $false,

        [string]$EmailFrom = "<EMAIL>",
        [string]$EmailTo = "<EMAIL>",
        [string]$SmtpServer = "smtp.jeragm.com"
    )

    try {
        $actionDetails = @()
        if ($IsAccountDisabled) { $actionDetails += "disabled" }
        if ($IsExpirySet) { $actionDetails += "expiry set" }
        $actions = $actionDetails -join " and "

        $body = "An admin account ($AdminUserUPN) has been $actions by $env:USERNAME. Please find the attached log for more details."
        $attachment = $LogPath
        $bodyAsHtml = $true

        Send-MailMessage -From $EmailFrom -To $EmailTo -Subject $Subject -Body $body -SmtpServer $SmtpServer -Attachments $attachment -BodyAsHtml $bodyAsHtml
        Write-Output "Email notification sent by $env:USERNAME."
    } catch {
        Write-Log -Message "Error sending email: $_" -Level "ERROR"
    }
}

function Send-DisabledAccountNotification {
    param (
        [Parameter(Mandatory = $true)]
        [string]$LogPath,

        [Parameter(Mandatory = $true)]
        [string]$AdminUserUPN,

        [Parameter(Mandatory = $true)]
        [string]$Subject,

        [Parameter(Mandatory = $true)]
        [string]$EmailFrom,

        [Parameter(Mandatory = $true)]
        [string]$EmailTo,

        [Parameter(Mandatory = $true)]
        [string]$SmtpServer
    )

    $body = "An admin account ($AdminUserUPN) has been disabled by $env:USERNAME. Please find the attached log for more details."
    $attachment = $LogPath
    $bodyAsHtml = $true

    Send-MailMessage -From $EmailFrom -To $EmailTo -Subject $Subject -Body $body -SmtpServer $SmtpServer -Attachments $attachment -BodyAsHtml $bodyAsHtml
}

function Send-ExpirySetNotification {
    param (
        [Parameter(Mandatory = $true)]
        [string]$LogPath,

        [Parameter(Mandatory = $true)]
        [string]$AdminUserUPN,

        [Parameter(Mandatory = $true)]
        [string]$Subject,

        [Parameter(Mandatory = $true)]
        [string]$EmailFrom,

        [Parameter(Mandatory = $true)]
        [string]$EmailTo,

        [Parameter(Mandatory = $true)]
        [string]$SmtpServer
    )

    $body = "An admin account ($AdminUserUPN) has had its expiry set by $env:USERNAME. Please find the attached log for more details."
    $attachment = $LogPath
    $bodyAsHtml = $true

    Send-MailMessage -From $EmailFrom -To $EmailTo -Subject $Subject -Body $body -SmtpServer $SmtpServer -Attachments $attachment -BodyAsHtml $bodyAsHtml
}

$expiryConfirmation = Get-Credential -Message "Set account expiry for $sam?"
if ($expiryConfirmation.GetNetworkCredential().Password -eq 'yes') {
    $expiryDate = Read-Host "Enter expiry date (MM/dd/yyyy)"
    Set-ADAccountExpiration -Identity $user -DateTime (Get-Date $expiryDate)
    $expirySet += $upn
}

$continue = 'yes'
while ($continue -eq 'yes') {
    $continue = Read-Host "Process another user? (yes/no)"
}

$body = "An admin account ($AdminUserUPN) has been $actions by $env:USERNAME. Please find the attached log for more details."
$attachment = $LogPath
$bodyAsHtml = $true

Send-MailMessage -From $EmailFrom -To $EmailTo -Subject $Subject -Body $body -SmtpServer $SmtpServer -Attachments $attachment -BodyAsHtml $bodyAsHtml
Write-Output "Email notification sent by $env:USERNAME."

try {
    $actionDetails = @()
    if ($IsAccountDisabled) { $actionDetails += "disabled" }
    if ($IsExpirySet) { $actionDetails += "expiry set" }
    $actions = $actionDetails -join " and "

    $body = "An admin account ($AdminUserUPN) has been $actions by $env:USERNAME. Please find the attached log for more details."
    $attachment = $LogPath
    $bodyAsHtml = $true

    try {
        Send-MailMessage -From $EmailFrom -To $EmailTo -Subject $Subject -Body $body -SmtpServer $SmtpServer -Attachments $attachment -BodyAsHtml $bodyAsHtml
        Write-Output "Email notification sent by $env:USERNAME."
    } catch {
        Write-Log -Message "Error sending email: $_" -Level "ERROR"
    }
} catch {
    Write-Log -Message "Error generating email content: $_" -Level "ERROR"
}

# Writes a log message with a timestamp and the current user's name
function Write-Log {
    param (
        [Parameter(Mandatory = $true)]
        [string]$Message,

        [Parameter(Mandatory = $false)]
        [ValidateSet("DEBUG", "INFO", "WARNING", "ERROR", "VERBOSE")]
        [string]$Level = "INFO",

        [string]$Path = "C:\Temp\Scripts\Desktop Support\Logs\AdminAccountCreation.log"
    )

    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] [$env:USERNAME] $Message"

    # Only log DEBUG or VERBOSE messages if a certain condition is met (e.g., a debug flag is set)
    $shouldLog = $true
    if ($Level -eq "DEBUG" -or $Level -eq "VERBOSE") {
        # Example condition: check for a global debug flag
        # This part is for illustration; implement the condition based on your needs
        $shouldLog = $global:DebugEnabled
    }

    if ($shouldLog) {
        Add-Content -Path $Path -Value $logMessage
    }

    Write-Host $logMessage
}

function Offboard-ADUser {
    <#
    .SYNOPSIS
    Offboards Active Directory users by setting their account expiry and disabling their accounts.

    .DESCRIPTION
    This function prompts the user to confirm the actions for each user and sends email notifications based on the actions taken. It sets the account expiry and disables the account for each user.

    .EXAMPLE
    Offboard-ADUser
    This command will start the offboarding process for Active Directory users. It will prompt the user to set the account expiry and disable the account for each user. Email notifications will be sent based on the actions taken.

    .NOTES
    Author: Your Name
    Date: Today's Date
    Version: 1.0
    #>

    param()

    Import-Module ActiveDirectory

    $expirySet = @()
    $accountDisabled = @()
    $logPath = "PathToLog"

    $users = Get-ADUser -Filter * -Properties SamAccountName, UserPrincipalName

    foreach ($user in $users) {
        $upn = $user.UserPrincipalName
        $sam = $user.SamAccountName

        $expiryConfirmation = Get-Credential -Message "Set account expiry for $sam?"
        if ($expiryConfirmation.GetNetworkCredential().Password -eq 'yes') {
            $expiryDate = Read-Host "Enter expiry date (MM/dd/yyyy)"
            Set-ADAccountExpiration -Identity $user -DateTime (Get-Date $expiryDate)
            $expirySet = $expirySet + $upn
        }

        $disableConfirmation = Read-Host "Disable user account for $sam? (yes/no)"
        if ($disableConfirmation -eq 'yes') {
            Disable-ADAccount -Identity $user
            Move-ADObject -Identity $user -TargetPath "OU=Terminations,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com"
            $accountDisabled = $accountDisabled + $upn
        }
    }

    if ($expirySet) {
        Send-EmailNotification -LogPath $logPath -AdminUserUPN ($expirySet -join ", ") -Subject "Account Expiry Set"
    }

    if ($accountDisabled) {
        Send-EmailNotification -LogPath $logPath -AdminUserUPN ($accountDisabled -join ", ") -Subject "Account Disabled"
    }

    if ($expirySet -and $accountDisabled) {
        Send-EmailNotification -LogPath $logPath -AdminUserUPN (($expirySet + $accountDisabled) -join ", ") -Subject "Account Disabled and Expiry Set"
    }

    try {
        Log-Action -Message "Offboarding process completed for $($users.Count) users" -Details $details
    } catch {
        Write-Error $_
    }

    Send-MailMessage @mailParams

    $continue = 'yes'
    while ($continue -eq 'yes') {
        $continue = Read-Host "Process another user? (yes/no)"
    }
}

Offboard-ADUser