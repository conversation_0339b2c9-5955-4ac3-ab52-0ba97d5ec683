{"p_appGroups": {"value": [{"applicationGroupType": "Desktop", "description": "Desktop Session for ZEMA Testing - Pooled.", "friendlyName": "Desktop Session for ZEMA Testing - Testing (Pooled)", "appgroupName": "zema-testing-desktop", "aadUserGroup": "AZVD Users - ZEMA Testing - Pooled", "hostpoolName": "zema-testing-pooled"}, {"applicationGroupType": "RemoteApp", "description": "RemoteApps for ZEMA Testing - Pooled.", "friendlyName": "RemoteApps for ZEMA Testing - ZEMA Testing (Pooled)", "appgroupName": "zema-testing-remoteapp", "aadUserGroup": "AZVD Users - ZEMA Testing - Pooled", "hostpoolName": "zema-testing-pooled"}]}, "p_remoteApps": {"value": [{"applicationName": "MS Excel 365", "description": "Microsoft Excel 365", "friendlyName": "Microsoft Excel 365", "filePath": "C:\\Program Files\\Microsoft Office\\root\\Office16\\EXCEL.EXE", "hostpoolName": "zema-testing-pooled"}]}, "p_hostPools": {"value": [{"customRdpProperty": "drivestoredirect:s:;audiomode:i:0;videoplaybackmode:i:1;redirectclipboard:i:0;redirectprinters:i:0;devicestoredirect:s:;redirectcomports:i:0;redirectsmartcards:i:0;usbdevicestoredirect:s:;enablecredsspsupport:i:1;use multimon:i:1;autoreconnection enabled:i:1;bandwidthautodetect:i:1;networkautodetect:i:1;compression:i:1;audiocapturemode:i:1;encode redirected video capture:i:1;redirected video capture encoding quality:i:2;camerastoredirect:s:*;selectedmonitors:s:;maximizetocurrentdisplays:i:1;singlemoninwindowedmode:i:1;screen mode id:i:1;smart sizing:i:1;dynamic resolution:i:1;desktopscalefactor:i:100;targetisaadjoined:i:1;enablerdsaadauth:i:1;redirectwebauthn:i:1;", "description": "Azure Virtual Desktop Host Pool for ZEMA Testing - Pooled.", "friendlyName": "JERAGM Host Pool - ZEMA Testing (Pooled)", "hostPoolType": "<PERSON>d", "loadBalancerType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maxSessionLimit": 4, "personalDesktopAssignmentType": "Direct", "preferredAppGroupType": "Desktop", "startVMOnConnect": false, "hostpoolName": "zema-testing-pooled", "hostpoolNameShort": "avdze", "validationEnvironment": false, "aadUserGroup": "AZVD Users - ZEMA Testing - Pooled", "fslogixEnabled": true}]}, "p_scalingPlans": {"value": [{"enabled": false, "hostpoolName": "zema-testing-pooled"}]}}