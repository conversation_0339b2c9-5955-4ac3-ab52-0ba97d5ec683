function Get-JiraAdminAccountData {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        $Issue
    )

    Write-Log -Message "Extracting admin account data from <PERSON>ra ticket $($Issue.Key)" -LogLevel "DEBUG"

    # --- Extract required data from <PERSON>ra Issue ---
    # Define custom field IDs (these should ideally be configurable, not hardcoded)
    $cfFirstNameId = 'customfield_10304' # First Name
    $cfLastNameId = 'customfield_10305'  # Last Name
    $cfOfficeLocationId = 'customfield_10115' # Office Location
    $cfITAdminAccountId = 'customfield_10453' # IT Admin Account?
    # Optional fields for logging/info
    $cfDepartmentId = 'customfield_10120' # Department
    $cfJobTitleId = 'customfield_10238' # Job Title
    $cfModelAccountId = 'customfield_10343' # Model Account

    $firstName = Get-JiraCustomFieldValue -Issue $Issue -CustomFieldId $cfFirstNameId -FieldName "First Name"
    $lastName = Get-JiraCustomFieldValue -Issue $Issue -CustomFieldId $cfLastNameId -FieldName "Last Name"
    $officeLocation = Get-JiraCustomFieldValue -Issue $Issue -CustomFieldId $cfOfficeLocationId -FieldName "Office Location"
    $itAdminAccount = Get-JiraCustomFieldValue -Issue $Issue -CustomFieldId $cfITAdminAccountId -FieldName "IT Admin Account?"
    $department = Get-JiraCustomFieldValue -Issue $Issue -CustomFieldId $cfDepartmentId -FieldName "Department"
    $jobTitle = Get-JiraCustomFieldValue -Issue $Issue -CustomFieldId $cfJobTitleId -FieldName "Job Title"
    $modelAccount = Get-JiraCustomFieldValue -Issue $Issue -CustomFieldId $cfModelAccountId -FieldName "Model Account"

    # Validate required fields
    if ([string]::IsNullOrWhiteSpace($firstName) -or [string]::IsNullOrWhiteSpace($lastName)) {
        throw "Required fields (First Name, Last Name) not found or empty in Jira ticket $($Issue.Key)."
    }
    if ($itAdminAccount -ne 'Yes') {
         throw "'IT Admin Account?' field is not 'Yes' on Jira ticket $($Issue.Key). Aborting."
    }

    # Return the extracted data as a hashtable
    return @{
        FirstName = $firstName
        LastName = $lastName
        OfficeLocation = $officeLocation
        ITAdminAccount = $itAdminAccount
        Department = $department
        JobTitle = $jobTitle
        ModelAccount = $modelAccount
    }
}