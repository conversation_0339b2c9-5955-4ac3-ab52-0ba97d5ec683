## source: https://call4cloud.nl/2021/05/cloudy-with-a-chance-of-winget/

#WebClient
$dc = New-Object net.webclient
$dc.UseDefaultCredentials = $true
$dc.Headers.Add("user-agent", "Inter Explorer")
$dc.Headers.Add("X-FORMS_BASED_AUTH_ACCEPTED", "f")

#temp folder
$InstallerFolder = $(Join-Path $env:ProgramData CustomScripts)
if (!(Test-Path $InstallerFolder))
{
    New-Item -Path $InstallerFolder -ItemType Directory -Force -Confirm:$false
}

#Check Winget Install
Write-Host "Checking if Winget is installed" -ForegroundColor Yellow
$TestWinget = Get-AppxProvisionedPackage -Online | Where-Object {$_.DisplayName -eq "Microsoft.DesktopAppInstaller"}
If ([Version]$TestWinGet. Version -gt "2022.506.16.0") 
{
    Write-Host "WinGet is Installed" -ForegroundColor Green
}
Else 
{
    #Download WinGet MSIXBundle
    Write-Host "Not installed. Downloading WinGet..." 
    $WinGetURL = "https://aka.ms/getwinget"
    $dc.DownloadFile($WinGetURL, "$InstallerFolder\Microsoft.DesktopAppInstaller_8wekyb3d8bbwe.msixbundle")
    
    #Install WinGet MSIXBundle 
    Try 	{
        Write-Host "Installing MSIXBundle for App Installer..." 
        Add-AppxProvisionedPackage -Online -PackagePath "$InstallerFolder\Microsoft.DesktopAppInstaller_8wekyb3d8bbwe.msixbundle" -SkipLicense 
        Write-Host "Installed MSIXBundle for App Installer" -ForegroundColor Green
        }
    Catch {
        Write-Host "Failed to install MSIXBundle for App Installer..." -ForegroundColor Red
        } 

    #Remove WinGet MSIXBundle 
    #Remove-Item -Path "$InstallerFolder\Microsoft.DesktopAppInstaller_8wekyb3d8bbwe.msixbundle" -Force -ErrorAction Continue
}