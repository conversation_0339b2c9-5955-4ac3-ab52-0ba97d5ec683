name         : "Azure Storage Explorer"
publisher    : "Microsoft"
appVersion   : "1.25.1"
description  : "Tool to conveniently manage your Azure cloud storage resources from your desktop."
installExp   : "system"
featured     : false
supersede    : "none"
assignments  : 
  Intune-Users-Developers:
  - intent   : "available"
dependencies : "none"
commandLine  :
- install    : '"Azure Storage Explorer.exe" /VERYSILENT /NORESTART /ALLUSERS'
- uninstall  : '"%ProgramFiles(x86)%\Microsoft Azure Storage Explorer\unins000.exe" /VERYSILENT /NORESTART'