parameters:
  subscriptions : ""
  svcConnection : ""
  location      : ""

steps:
- task: AzurePowerShell@5
  displayName: Config Host ${{ parameters.location }}
  inputs:
    azurePowerShellVersion: latestVersion
    azureSubscription     : ${{ parameters.svcConnection }}
    pwsh                  : true
    scriptPath            : $(System.DefaultWorkingDirectory)/scripts/config_hosts.ps1
    scriptType            : filePath
    workingDirectory      : $(System.DefaultWorkingDirectory)/scripts/
    ScriptArguments       :
      -location      "${{ parameters.location }}"
      -subscriptions "${{ parameters.subscriptions }}"
      -deploymentName "deployment-$(Build.BuildId)"