//SCOPE

targetScope = 'subscription'

// PARAMETERS

param p_utcNow	   string = utcNow()
@description('Host parameters stored in ./param/$subscriptionName/$teamName.json')
param p_hosts	   array = []
@description('Tags stored in ./param/$subscriptionName/$teamName.json')
param p_tags	   object

@description('Location of Host Pool, Application Group and Workspace')
@allowed([
	'uksouth'
	'ukwest'
	'eastasia'
	'southeastasia'
])
param p_location string

@description('Azure Environment')
@allowed([
	'prod'
	'nonprod'
])
param p_environment string

param p_storageAccountName string
param p_serviceCode        string
param p_appGroups          array
param p_remoteApps         array
param p_hostPools          array
param p_scalingPlans			 array


// VARIABLES

@description('Abbreviation of location name for other resource naming')
var v_locationSwitch = {
	uksouth       : 'uks'
	ukwest        : 'ukw'
	eastasia      : 'eaa'
	southeastasia : 'sea'
}

@description('Selecting another region if an Asian region was selected, as Azure Virtual Desktop is not available in Asia yet')
var v_avdLocationSwitch = {
	uksouth 	    : 'uksouth'
	ukwest		    : 'ukwest'
	southeastasia	: 'uksouth'
	eastasia	    : 'ukwest'
}

@description('Abbreviation of subscription name for resource naming')
var v_environmentSwitch = {
	prod    : 'prd'
	nonprod : 'npr'
}

//RUNTIME PARAM

param p_hostPoolName  string
param p_primaryRegion string
param p_hostCount     string

// VARIABLES
// If the location is the primary region, then the host count is the host count parameter. Otherwise, it is 0.
var v_hostCount = p_location == p_primaryRegion ? p_hostCount : '0'

// RESOURCES
resource rg_hosts 'Microsoft.Resources/resourceGroups@2022-09-01' = {
    name    : '${v_locationSwitch[p_location]}-${v_environmentSwitch[p_environment]}-ssv-azvd-${p_hostPoolName}-rg' // Example: uks-npr-ssv-azvd-tt-rg
    location: p_location
    tags    : p_tags
}

resource workspace 'Microsoft.DesktopVirtualization/workspaces@2022-09-09' existing = {
  	name: '${v_locationSwitch[p_location]}-${v_environmentSwitch[p_environment]}-${p_serviceCode}-azvd-vdws'
  	scope: resourceGroup('${v_locationSwitch[p_location]}-${v_environmentSwitch[p_environment]}-${p_serviceCode}-azvd-rg')
}

// MODULES
module m_azvd 'azvd.bicep' = {
	name: 'm_azvd_${p_utcNow}'
	scope: rg_hosts
	params: {
		p_appGroups       : filter(p_appGroups, appgroup => appgroup.hostpoolName == p_hostPoolName)
		p_remoteApp       : filter(p_remoteApps, remoteapp => remoteapp.hostpoolName == p_hostPoolName)
		p_hostPool        : first(filter(p_hostPools, hostpool => hostpool.hostpoolName == p_hostPoolName))
		p_scalingPlan 		: first(filter(p_scalingPlans, scalingplan => scalingplan.hostpoolName == p_hostPoolName))
		p_azvdLocation    : v_avdLocationSwitch[p_location]
		p_environmentShort: v_environmentSwitch[p_environment]
		p_locationShort   : v_locationSwitch[p_location]
		p_tags            : p_tags
		p_location        : p_location
		p_serviceCode     : p_serviceCode
		p_primaryRegion   : p_primaryRegion
	}
}

module m_hosts 'hosts.bicep' = [for i in range(1, int(v_hostCount)): if (p_location == p_primaryRegion) {
	name: 'host${i}_${p_utcNow}'
    scope: rg_hosts
    params: {
		p_availabilityZone  : string(i % 3 + 1)
		p_hostNumber        : '${i}'
		p_host              : first(filter(p_hosts, host => (host.hostpoolName == p_hostPoolName)))
		p_hostPoolName      : m_azvd.outputs.o_hostPoolName
		p_hostPoolNameShort : m_azvd.outputs.o_hostPoolNameShort
		p_hostPoolRegKey    : m_azvd.outputs.o_hostPoolRegKey
		p_location          : p_location
		p_locationShort     : v_locationSwitch[p_location]
		p_tags              : p_tags
		p_environmentShort  : v_environmentSwitch[p_environment]
		p_storageAccountName: p_storageAccountName
	}
}]



module m_storage 'storage.bicep' = {
	name: 'm_storage_${p_utcNow}'
	scope: resourceGroup('${v_locationSwitch[p_location]}-${v_environmentSwitch[p_environment]}-${p_serviceCode}-azvd-rg')
	params: {
		p_environmentShort  : v_environmentSwitch[p_environment]
		p_hostPool          : first(filter(p_hostPools, hostpool => hostpool.hostpoolName == p_hostPoolName))
		p_storageAccountName: p_storageAccountName
		p_locationShort     : v_locationSwitch[p_location]
	}
}

//OUTPUTS
// Output information to the pipeline in Azure DevOps.

output o_resourceGroupName    string  = rg_hosts.name
output o_workspaceName        string  = workspace.name
output o_applicationGroupName array   = m_azvd.outputs.o_applicationGroupName
output o_sharedAzvdRgName     string  = '${v_locationSwitch[p_location]}-${v_environmentSwitch[p_environment]}-${p_serviceCode}-azvd-rg'
output o_applicationGroupTags array   = m_azvd.outputs.o_applicationGroupTags
output o_fileShareId          string  = m_storage.outputs.o_fileShareId
output o_fslogixEnabled       bool    = m_storage.outputs.o_fslogixEnabled
output o_containerName        string  = m_storage.outputs.o_containerName
output o_storageAccountName   string  = m_storage.outputs.o_storageAccountName
output o_hostPoolName         string  = m_azvd.outputs.o_hostPoolName

// https://learn.microsoft.com/en-us/azure/azure-resource-manager/bicep/outputs?tabs=azure-powershell#conditional-output
output o_hostnames array = [for i in range(0, int(v_hostCount)): {
  	name: m_hosts[i].outputs.o_hostname
}]

