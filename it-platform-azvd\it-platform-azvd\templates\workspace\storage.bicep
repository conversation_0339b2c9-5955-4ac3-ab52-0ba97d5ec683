// PARAMETERS

param p_location         string
param p_locationShort    string
param p_environmentShort string
param p_tags             object
param p_storageAccount   object

//VARIABLES

// RESOURCES

resource storageAccount 'Microsoft.Storage/storageAccounts@2023-01-01' = {
    name      : toLower('jgm${p_locationShort}${p_environmentShort}${p_storageAccount.name}st') // Example: jgmuksprdazvdfslogixst
    kind      : p_storageAccount.kind
    location  : p_location
    tags      : p_tags
    sku: {
        name: p_storageAccount.sku
    }
    properties: {
        azureFilesIdentityBasedAuthentication: p_storageAccount.kerberosSettings
        allowBlobPublicAccess   : p_storageAccount.allowBlobPublicAccess
        minimumTlsVersion       : p_storageAccount.minimumTlsVersion
        supportsHttpsTrafficOnly: p_storageAccount.supportsHttpsTrafficOnly
        allowSharedKeyAccess    : p_storageAccount.allowSharedKeyAccess
        networkAcls: {
            bypass: 'AzureServices'
            virtualNetworkRules: !(empty(p_storageAccount.virtualNetworkRules)) == true ? p_storageAccount.virtualNetworkRules : null
            ipRules: !(empty(p_storageAccount.ipRules)) == true ? p_storageAccount.ipRules : null
            defaultAction: 'Deny'
        }
    }
}

// Outputs the storage account name to main.bicep
output o_storageAccountName string = storageAccount.name
