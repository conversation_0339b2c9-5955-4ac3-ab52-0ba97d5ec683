#requires -Module ActiveDirectory

<#
.SYNOPSIS
    Reads user details from a CSV file and creates AD user accounts with unique, randomly generated passwords.
    Provides an interactive step-by-step approach, requiring user confirmation at each stage.

.DESCRIPTION
    This PowerShell script automates bulk onboarding of AD users from a CSV file, with added
    interactivity to confirm each step before proceeding. It enhances logging, handles
    domain/OU checks, CSV validation, duplicate checks, and final creation.

    Existing Functions:
      1) Initialize-Logging       - Prepares the log file.
      2) Write-Log               - Logs messages with various severity levels.
      3) Test-ADSettings         - Validates the domain and OU in Active Directory.
      4) Test-UserRecord         - Checks each CSV record for required fields.
      5) Get-UserLogonName       - Returns the UPN (from CSV).
      6) Get-SamAccountName      - Builds a unique sAMAccountName from first & last name.
      7) New-SecurePassword      - Generates a random, secure password.
      8) Get-EmailAddress        - Constructs the user's email address.
      9) New-ADUserFromRecord    - Creates an AD user from a valid record.

    New Function:
      10) Test-ADDuplicates      - Checks for existing AD users with the same name or UPN.

    Main Flow (Invoke-OnboardUsers):
      1) Asks for permission to begin.
      2) Checks/logs file paths (log file, CSV) and calls Test-ADSettings.
      3) Validates CSV records, displaying valid (green) vs. invalid (red).
      4) Checks duplicates in AD and skips duplicate entries, displaying them in red.
      5) Previews final set of valid, non-duplicate users in yellow.
      6) Asks for final confirmation before creating users.
      7) Creates each user and logs progress.
      8) Confirms completion.

.PARAMETER CsvPath
    The path to the CSV file.

.PARAMETER Domain
    The AD domain name (e.g., "jeragm.com").

.PARAMETER OUPath
    The DN path for the target OU (e.g., "OU=Tokyo,OU=Users,OU=Accounts,OU=JeraGM,DC=JERAGM,DC=COM").

.PARAMETER LogFilePath
    The (optional) path to the log file. If not provided, a unique name is generated.

.EXAMPLE
    .\interactive-bulk-import-onboard-users.ps1 -CsvPath "C:\Scripts\users.csv" -Domain "jeragm.com" `
        -OUPath "OU=Tokyo,OU=Users,OU=Accounts,OU=JeraGM,DC=JERAGM,DC=COM"

.NOTES
    - Requires the ActiveDirectory module.
    - The script is interactive. It waits for user confirmations to proceed.
    - Make sure to run PowerShell in a context that can create AD user accounts.
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory)]
    [string]$CsvPath,

    [Parameter(Mandatory)]
    [AllowEmptyString()]
    [string]$Domain = "jeragm.com",

    [Parameter(Mandatory)]
    [AllowEmptyString()]
    [string]$OUPath = "OU=Tokyo,OU=Users,OU=Accounts,OU=JeraGM,DC=JERAGM,DC=COM",

    [Parameter()]
    [string]$LogFilePath
)

###############################################################################
#                          GLOBAL CONFIGURATIONS                              #
###############################################################################
if (-not $LogFilePath) {
    $dateStamp   = (Get-Date -Format "yyyyMMdd-HHmmss")
    $currentUser = $env:USERNAME
    $LogFilePath = "C:\Temp\Scripts\Desktop Support\Logs\OnboardingLog-$dateStamp-$currentUser.txt"
}

if ([string]::IsNullOrWhiteSpace($Domain)) {
    $Domain = "jeragm.com"
}
if ([string]::IsNullOrWhiteSpace($OUPath)) {
    $OUPath = "OU=Tokyo,OU=Users,OU=Accounts,OU=JeraGM,DC=JERAGM,DC=COM"
}

$script:DomainName = $Domain
$script:OUPath     = $OUPath
$script:CsvPath    = $CsvPath

###############################################################################
#                           LOGGING FUNCTIONS                                 #
###############################################################################
Function Initialize-Logging {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$LogFile
    )
    try {
        New-Item -ItemType File -Path $LogFile -Force | Out-Null
    }
    catch {
        Write-Host "Could not initialize log file at ${LogFile}: $($_.Exception.Message)" -ForegroundColor Red
        throw
    }
}

Function Write-Log {
    [CmdletBinding()]
    param(
        [string]$Message,
        [ValidateSet("INFO","WARN","ERROR","DEBUG")]
        [string]$Level = "INFO",
        [Parameter(Mandatory)]
        [string]$LogFile,
        [bool]$WriteToHost = $true
    )
    $timeStamp = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
    $logEntry  = "[$timeStamp] [$Level] $Message"
    try {
        Add-Content -Path $LogFile -Value $logEntry -Encoding UTF8
        if ($WriteToHost) {
            switch ($Level) {
                "INFO"  { Write-Host $logEntry -ForegroundColor White }
                "WARN"  { Write-Host $logEntry -ForegroundColor Yellow }
                "ERROR" { Write-Host $logEntry -ForegroundColor Red }
                "DEBUG" { Write-Host $logEntry -ForegroundColor Cyan }
            }
        }
    }
    catch {
        Write-Host "Could not write to log file at ${LogFile}: $($_.Exception.Message)" -ForegroundColor Red
    }
}

###############################################################################
#                          VALIDATION FUNCTIONS                               #
###############################################################################
Function Test-ADSettings {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$Domain,
        [Parameter(Mandatory)]
        [string]$OUPath,
        [Parameter(Mandatory)]
        [string]$LogFile
    )
    try {
        $null = Get-ADDomain -Identity $Domain -ErrorAction Stop
        Write-Log -Message "Verified domain '$Domain'." -Level INFO -LogFile $LogFile
    }
    catch {
        Write-Log -Message "Domain check failed for '$Domain': $($_.Exception.Message)" -Level ERROR -LogFile $LogFile
        throw
    }

    try {
        $null = Get-ADOrganizationalUnit -Identity $OUPath -ErrorAction Stop
        Write-Log -Message "Verified organizational unit '$OUPath'." -Level INFO -LogFile $LogFile
    }
    catch {
        Write-Log -Message "OU check failed for '$OUPath': $($_.Exception.Message)" -Level ERROR -LogFile $LogFile
        throw
    }
}

Function Test-UserRecord {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        $User,
        [Parameter(Mandatory)]
        [string]$LogFile,
        [Parameter(Mandatory)]
        [int]$RowNumber
    )
    # Adjust required fields as needed
    $requiredFields = @("FirstNameRomaji", "LastNameRomaji", "displayName", "upn", "OtherMailbox")
    foreach ($field in $requiredFields) {
        if ([string]::IsNullOrWhiteSpace($User.$field)) {
            Write-Log -Message "Row #$($RowNumber): Missing field '$field'. Record: $($User | Out-String)" -Level "WARN" -LogFile $LogFile
            return $false
        }
    }
    return $true
}

###############################################################################
#                          HELPER / GENERATION FUNCTIONS                      #
###############################################################################
Function Get-UserLogonName {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$Upn
    )
    if (-not $Upn) { throw "UPN value is required for Get-UserLogonName" }
    return $Upn.ToLower()
}

Function Get-SamAccountName {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$FirstNameRomaji,
        [Parameter(Mandatory)]
        [string]$LastNameRomaji
    )
    
# Get first 5 letters of last name (or less if name is shorter)
$lastPart = $LastNameRomaji.Substring(0, [Math]::Min(5, $LastNameRomaji.Length))

# Start with just first letter of first name
$firstNameIndex = 1
$isUnique = $false
$minLengthSatisfied = $false

Write-Log -Message "Generating SAM account name for $FirstNameRomaji $LastNameRomaji" -Level Info -LogFile $LogFile

while (-not $isUnique -and $firstNameIndex -le $FirstNameRomaji.Length) {
    # Get increasing portions of the first name
    $firstPart = $FirstNameRomaji.Substring(0, $firstNameIndex)
    $proposedSam = ("{0}{1}" -f $lastPart, $firstPart).ToLower()
    
    # Check if the proposed SAM meets the minimum length requirement of 6 characters
    if ($proposedSam.Length -lt 6) {
        # If the proposed SAM is too short, add more characters from the first name if possible
        if ($firstNameIndex -lt $FirstNameRomaji.Length) {
            $firstNameIndex++
            Write-Log -Message "SAM account name '$proposedSam' is less than 6 characters, adding more letters from first name" -Level Warning -LogFile $LogFile
            continue
        }
        else {
            # If we've used all first name characters and still under 6, flag it but continue
            Write-Log -Message "Warning: SAM account name '$proposedSam' is less than 6 characters but using all available name parts" -Level Warning -LogFile $LogFile
            $minLengthSatisfied = $false
        }
    }
    else {
        $minLengthSatisfied = $true
    }
    
    try {
        Write-Log -Message "Checking if SAM account name '$proposedSam' exists" -Level Debug -LogFile $LogFile
        
        # Check if this SAM account exists but has a different UPN
        $existingSam = Get-ADUser -Filter "SamAccountName -eq '$proposedSam'" -Properties UserPrincipalName -ErrorAction Stop
        
        if (-not $existingSam) {
            # No user found with this SAM - we can use it
            $isUnique = $true
            
            if (-not $minLengthSatisfied) {
                Write-Log -Message "Generated unique SAM account name: $proposedSam (Warning: less than 6 characters)" -Level Warning -LogFile $LogFile
            }
            else {
                Write-Log -Message "Generated unique SAM account name: $proposedSam" -Level Info -LogFile $LogFile
            }
        }
        else {
            # SAM exists - try next letter of first name
            $firstNameIndex++
            Write-Log -Message "SAM account name '$proposedSam' already exists, trying with more letters from first name" -Level Warning -LogFile $LogFile
        }
    }
    catch {
        # If there's an error checking AD, log it and throw it up
        Write-Log -Message "Error checking SAM account existence: $($_.Exception.Message)" -Level Error -LogFile $LogFile
        throw "Error checking SAM account existence: $($_.Exception.Message)"
    }
}

if (-not $isUnique) {
    $errorMsg = "Could not generate unique SAM account name for $FirstNameRomaji $LastNameRomaji after trying all combinations"
    Write-Log -Message $errorMsg -Level Error -LogFile $LogFile
    throw $errorMsg
}

return $proposedSam
}

Function New-SecurePassword {
    [CmdletBinding()]
    param(
        [Parameter()]
        [int]$Length = 24
    )
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()-_=+[]{};:,.<>?'
    $pw    = -join (1..$Length | ForEach-Object { $chars[(Get-Random -Minimum 0 -Maximum $chars.Length)] })
    return $pw | ConvertTo-SecureString -AsPlainText -Force
}

Function Get-EmailAddress {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$Upn
    )
    if (-not $Upn) {
        throw "UPN value is required for Get-EmailAddress"
    }
    return ("{0}@{1}" -f $Upn.ToLower(), $script:DomainName).ToLower()
}

###############################################################################
#                          DUPLICATE CHECK FUNCTION                           #
###############################################################################
Function Test-ADDuplicates {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        $User,
        [Parameter(Mandatory)]
        [string]$LogFile,
        [Parameter(Mandatory)]
        [int]$RowNumber
    )

    $fn = $User.FirstNameRomaji
    $ln = $User.LastNameRomaji
    
    # Use existing functions to generate UPN and SAM
    $proposedSam = Get-SamAccountName -FirstNameRomaji $fn -LastNameRomaji $ln
    $userLogonName = Get-UserLogonName -Upn $User.upn
    $fullEmail = Get-EmailAddress -Upn $User.upn

    try {
        # Check each condition separately to avoid filter syntax issues
        $dupByName = Get-ADUser -Filter "GivenName -eq '$($fn.Replace("'","''"))' -and Surname -eq '$($ln.Replace("'","''"))'" -Properties DisplayName,GivenName,Surname,UserPrincipalName,SamAccountName -ErrorAction Stop
        $dupByUPN = Get-ADUser -Filter "UserPrincipalName -eq '$($fullEmail.Replace("'","''"))'" -Properties DisplayName,GivenName,Surname,UserPrincipalName,SamAccountName -ErrorAction Stop
        $dupBySAM = Get-ADUser -Filter "SamAccountName -eq '$($proposedSam.Replace("'","''"))'" -Properties DisplayName,GivenName,Surname,UserPrincipalName,SamAccountName -ErrorAction Stop

        $existingUsers = @()
        if ($dupByName) { $existingUsers += @($dupByName) }
        if ($dupByUPN) { $existingUsers += @($dupByUPN) }
        if ($dupBySAM) { $existingUsers += @($dupBySAM) }
        
        # Remove duplicates if same user matched multiple criteria
        $existingUsers = $existingUsers | Sort-Object ObjectGUID -Unique

        if ($existingUsers) {
            foreach ($existing in $existingUsers) {
                $matches = @()
                if ($existing.GivenName -eq $fn -and $existing.Surname -eq $ln) {
                    $matches += "Name (${fn} ${ln})"
                }
                if ($existing.UserPrincipalName -eq $fullEmail) {
                    $matches += "UPN ($fullEmail)"
                }
                if ($existing.SamAccountName -eq $proposedSam) {
                    $matches += "SAM ($proposedSam)"
                }

                $matchTypes = $matches -join ", "
                Write-Log -Message "Row #${RowNumber}: Duplicate found matching $matchTypes. Existing user details:" -Level "WARN" -LogFile $LogFile
                Write-Log -Message "    Display Name: $($existing.DisplayName)" -Level "WARN" -LogFile $LogFile
                Write-Log -Message "    Given Name: $($existing.GivenName)" -Level "WARN" -LogFile $LogFile
                Write-Log -Message "    Surname: $($existing.Surname)" -Level "WARN" -LogFile $LogFile
                Write-Log -Message "    UPN: $($existing.UserPrincipalName)" -Level "WARN" -LogFile $LogFile
                Write-Log -Message "    SAM: $($existing.SamAccountName)" -Level "WARN" -LogFile $LogFile
            }
            return $true
        } 
        else {
            Write-Log -Message "Row #${RowNumber}: No duplicates found for $fn $ln" -Level "INFO" -LogFile $LogFile
            return $false
        }
    }
    catch {
        Write-Log -Message "Row #${RowNumber}: Error checking duplicates: $($_.Exception.Message)" -Level "ERROR" -LogFile $LogFile
        return $true
    }
}

###############################################################################
#                          CREATION FUNCTION                                  #
###############################################################################
Function New-ADUserFromRecord {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        $User,
        [Parameter(Mandatory)]
        [string]$UserLogonName,
        [Parameter(Mandatory)]
        [string]$SamAccountName,
        [Parameter(Mandatory)]
        $SecurePwd,
        [Parameter(Mandatory)]
        [string]$LogFile,
        [Parameter(Mandatory)]
        [int]$RowNumber
    )
    try {
        # Check for existing UPN
        $existingUpn = Get-ADUser -Filter "UserPrincipalName -eq '$($UserLogonName.Replace("'","''"))'" -Properties DisplayName -ErrorAction SilentlyContinue
        
        # Check for existing SAM
        $existingSam = Get-ADUser -Filter "SamAccountName -eq '$($SamAccountName.Replace("'","''"))'" -Properties DisplayName -ErrorAction SilentlyContinue
        
        if ($existingUpn -and $existingSam) {
            Write-Log -Message "Row #${RowNumber}: Both UPN '$UserLogonName' and SAM Account '$SamAccountName' are already in use. UPN by: $($existingUpn.DisplayName), SAM by: $($existingSam.DisplayName). Skipping." -Level "WARN" -LogFile $LogFile
            return
        }
        elseif ($existingUpn) {
            Write-Log -Message "Row #${RowNumber}: UPN '$UserLogonName' is already in use by user: $($existingUpn.DisplayName). Skipping." -Level "WARN" -LogFile $LogFile
            return
        }
        elseif ($existingSam) {
            Write-Log -Message "Row #${RowNumber}: SAM Account '$SamAccountName' is already in use by user: $($existingSam.DisplayName). Skipping." -Level "WARN" -LogFile $LogFile
            return
        }

        $fullEmail = Get-EmailAddress -Upn $User.upn
        
        # Intelligently truncate display name if it's too long
        $displayName = $User.displayName
        if ($displayName.Length -gt 63) {
            # Split the display name into parts (assuming space-separated)
            $parts = $displayName -split '\s+'
            
            # Keep truncating parts from longest to shortest until we're under the limit
            while ($displayName.Length -gt 63 -and $parts.Count -gt 0) {
                # Find the longest part
                $longestIndex = 0
                $maxLength = $parts[0].Length
                for ($i = 1; $i -lt $parts.Count; $i++) {
                    if ($parts[$i].Length -gt $maxLength) {
                        $longestIndex = $i
                        $maxLength = $parts[$i].Length
                    }
                }
                
                # Truncate the longest part
                if ($parts[$longestIndex].Length -gt 3) {
                    $parts[$longestIndex] = $parts[$longestIndex].Substring(0, $parts[$longestIndex].Length - 1)
                    $displayName = $parts -join ' '
                } else {
                    # If we can't truncate anymore, remove the part
                    $parts = $parts[0..($longestIndex-1)] + $parts[($longestIndex+1)..($parts.Count-1)]
                    $displayName = $parts -join ' '
                }
            }
            
            Write-Log -Message "Row #${RowNumber}: Display name truncated to: $displayName" -Level "WARN" -LogFile $LogFile
        }

        # Clean and validate names
        $givenName = $User.FirstNameRomaji -replace '[^\p{L}\p{N}\s\-_\.]', ''
        $surname = $User.LastNameRomaji -replace '[^\p{L}\p{N}\s\-_\.]', ''
        
        # Validate lengths
        if ($givenName.Length -gt 64) {
            $givenName = $givenName.Substring(0, 64)
            Write-Log -Message "Row #${RowNumber}: Given name truncated to 64 characters" -Level "WARN" -LogFile $LogFile
        }
        if ($surname.Length -gt 64) {
            $surname = $surname.Substring(0, 64)
            Write-Log -Message "Row #${RowNumber}: Surname truncated to 64 characters" -Level "WARN" -LogFile $LogFile
        }

        # Create hashtable for optional attributes
        $otherAttributes = @{}
        if ($User.OtherMailbox) {
            $otherAttributes['otherMailbox'] = $User.OtherMailbox
        }

        # Create the user with cleaned values
        New-ADUser `
            -Name $displayName `
            -GivenName $givenName `
            -Surname $surname `
            -DisplayName $displayName `
            -EmailAddress $fullEmail `
            -UserPrincipalName $UserLogonName `
            -SamAccountName $SamAccountName `
            -OtherAttributes $otherAttributes `
            -Path $script:OUPath `
            -Enabled $true `
            -AccountPassword $SecurePwd `
            -ChangePasswordAtLogon $false `
            -ErrorAction Stop

        # Verify creation using SamAccountName
        Start-Sleep -Seconds 2  # Give AD time to replicate
        $verifyBySam = Get-ADUser -Filter "SamAccountName -eq '$($SamAccountName.Replace("'","''"))'" -Properties DistinguishedName,Enabled -ErrorAction SilentlyContinue
        
        if ($verifyBySam) {
            Write-Log -Message "Row #${RowNumber}: Creation confirmed for user '$displayName' (SAM: $SamAccountName, DN: $($verifyBySam.DistinguishedName), Enabled: $($verifyBySam.Enabled))." -Level "INFO" -LogFile $LogFile
            
            # If user was created but not enabled, try to enable them
            if (-not $verifyBySam.Enabled) {
                Write-Log -Message "Row #${RowNumber}: User account was created disabled. Attempting to enable..." -Level "WARN" -LogFile $LogFile
                try {
                    Enable-ADAccount -Identity $verifyBySam.DistinguishedName -ErrorAction Stop
                    Write-Log -Message "Row #${RowNumber}: Successfully enabled user account." -Level "INFO" -LogFile $LogFile
                }
                catch {
                    Write-Log -Message "Row #${RowNumber}: Failed to enable user account: $($_.Exception.Message)" -Level "ERROR" -LogFile $LogFile
                }
            }
        }
        else {
            Write-Log -Message "Row #${RowNumber}: Creation not verified for user '$displayName'." -Level "ERROR" -LogFile $LogFile
        }
    }
    catch {
        Write-Log -Message "Row #${RowNumber}: Error creating user '$($User.displayName)': $($_.Exception.Message)" -Level "ERROR" -LogFile $LogFile
        Write-Log -Message "Row #${RowNumber}: Full error details: $($_)" -Level "ERROR" -LogFile $LogFile
    }
}


###############################################################################
#                         MAIN PROCESS FUNCTION                               #
###############################################################################
Function Invoke-OnboardUsers {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$CsvPath,
        [Parameter(Mandatory)]
        [string]$Domain,
        [Parameter(Mandatory)]
        [string]$OUPath,
        [Parameter(Mandatory)]
        [string]$LogFile
    )

    $startChoice = Read-Host "Do you want to begin the AD Onboarding process? (Y/N)"
    if ($startChoice -notin @("Y","y","Yes","yes")) {
        Write-Host "Process canceled by user." -ForegroundColor Red
        return
    }

    if (-not (Test-Path (Split-Path $LogFile -Parent))) {
        Write-Host "Log file directory does not exist. Creating directory..." -ForegroundColor Yellow
        New-Item -ItemType Directory -Path (Split-Path $LogFile -Parent) -Force | Out-Null
    }
    Initialize-Logging -LogFile $LogFile
    Write-Log -Message "Script started. Log file: $LogFile" -Level INFO -LogFile $LogFile
    Write-Log -Message "Begin interactive onboarding." -Level INFO -LogFile $LogFile

    Write-Log -Message "Validating AD domain/OU." -Level INFO -LogFile $LogFile
    Test-ADSettings -Domain $Domain -OUPath $OUPath -LogFile $LogFile

    if (-not (Test-Path $CsvPath)) {
        Write-Log -Message "CSV file not found: $CsvPath" -Level ERROR -LogFile $LogFile
        Write-Host "CSV file not found: $CsvPath" -ForegroundColor Red
        return
    }
    Write-Log -Message "CSV file found at $CsvPath" -Level INFO -LogFile $LogFile

    $importChoice = Read-Host "Press Enter to import data (or type 'N' to cancel)."
    if ($importChoice -eq "N") {
        Write-Host "User canceled at data import stage." -ForegroundColor Red
        return
    }
    try {
        if ($CsvPath -match "\.xlsx$") {
            if (-not (Get-Module -ListAvailable -Name ImportExcel | Where-Object { $_.Version -eq [version]'7.8.6' })) {
                Write-Host "ImportExcel module version 7.8.6 not detected. Installing now..." -ForegroundColor Yellow
                try {
                    Install-Module -Name ImportExcel -RequiredVersion 7.8.6 -Scope CurrentUser -Force -AllowClobber
                    Write-Host "ImportExcel module installed successfully." -ForegroundColor Green
                }
                catch {
                    Write-Log -Message "Failed to install ImportExcel module: $($_.Exception.Message)" -Level ERROR -LogFile $LogFile
                    Write-Host "Failed to install ImportExcel module. Error: $($_.Exception.Message)" -ForegroundColor Red
                    return
                }
            }
            try {
                Import-Module ImportExcel -ErrorAction Stop
                Write-Host "ImportExcel module imported." -ForegroundColor Green
            }
            catch {
                Write-Log -Message "Error importing the ImportExcel module: $($_.Exception.Message)" -Level ERROR -LogFile $LogFile
                Write-Host "Error importing the ImportExcel module: $($_.Exception.Message)" -ForegroundColor Red
                return
            }
            $users = Import-Excel -Path $CsvPath -ErrorAction Stop
            Write-Log -Message "XLSX import successful. Total records: $($users.Count)" -Level INFO -LogFile $LogFile
        }
        else {
            $users = Import-Csv -Path $CsvPath -Encoding UTF8 -ErrorAction Stop
            Write-Log -Message "CSV import successful. Total records: $($users.Count)" -Level INFO -LogFile $LogFile
        }
    }
    catch {
        Write-Log -Message "Error importing data file: $($_.Exception.Message)" -Level ERROR -LogFile $LogFile
        Write-Host "Error importing data file: $($_.Exception.Message)" -ForegroundColor Red
        return
    }

    $validRecords = @()
    $recordCount = 0
    Write-Host "Validating records..." -ForegroundColor Cyan

    foreach ($user in $users) {
        $recordCount++
        
        if ((-not $user.FirstNameRomaji) -or (-not $user.LastNameRomaji) -or (-not $user.upn)) {
            Write-Log -Message "Row #$($recordCount): Missing required fields. Skipping row." -Level "WARN" -LogFile $LogFile
            Write-Host "Row #$($recordCount): Missing required fields. Skipping." -ForegroundColor Yellow
            continue
        }

        try {
            $isValid = Test-UserRecord -User $user -LogFile $LogFile -RowNumber $recordCount
            $summary = "Row #$($recordCount): Name: $($user.FirstNameRomaji) $($user.LastNameRomaji); displayName: $($user.displayName); upn: $($user.upn)"
            if ($isValid) {
                Write-Host $summary -ForegroundColor Green
                Write-Log -Message "Row #$($recordCount) validated successfully: $summary" -Level "INFO" -LogFile $LogFile
                $validRecords += $user
            }
            else {
                Write-Host $summary -ForegroundColor Red
                Write-Log -Message "Row #$($recordCount) failed validation: $summary" -Level "ERROR" -LogFile $LogFile
            }
        }
        catch {
            Write-Log -Message "Row #$($recordCount): Error processing row: $($_.Exception.Message)" -Level "ERROR" -LogFile $LogFile
            Write-Host "An error occurred processing row #$($recordCount). See log for details." -ForegroundColor Red
        }
    }

    Write-Log -Message "Validation complete. Valid records count: $($validRecords.Count)" -Level "INFO" -LogFile $LogFile

    Read-Host "Press Enter to check for duplicates in AD"
    $finalList = @()
    $duplicateRow = 0

    foreach ($user in $validRecords) {
        $duplicateRow++
        $duplicateFound = Test-ADDuplicates -User $user -LogFile $LogFile -RowNumber $duplicateRow
        if ($duplicateFound) {
            Write-Host "$($user.FirstNameRomaji) $($user.LastNameRomaji) - Duplicate Found!" -ForegroundColor Red
            Write-Log -Message "Duplicate found for $($user.displayName). Skipping." -Level WARN -LogFile $LogFile
        }
        else {
            Write-Host "$($user.FirstNameRomaji) $($user.LastNameRomaji) - No duplicate found." -ForegroundColor Green
            $finalList += $user
        }
    }

    Read-Host "Press Enter to preview the final valid, non-duplicate records"
    Write-Host "Preview of final valid records:" -ForegroundColor Cyan
    foreach ($user in $finalList) {
        $sam  = Get-SamAccountName -FirstNameRomaji $user.FirstNameRomaji -LastNameRomaji $user.LastNameRomaji
        $upn  = Get-UserLogonName -Upn $user.upn
        $mail = Get-EmailAddress -Upn $user.upn
        Write-Host "Name: $($user.FirstNameRomaji) $($user.LastNameRomaji), displayName: $($user.displayName), UPN: $mail, SAM: $sam" -ForegroundColor Yellow
    }

    $confirmCreation = Read-Host "Are you happy to proceed with creating these users? (Y/N)"
    if ($confirmCreation -notin @("Y","y","Yes","yes")) {
        Write-Log -Message "Creation canceled by user." -Level WARN -LogFile $LogFile
        Write-Host "Operation canceled." -ForegroundColor Red
        return
    }

    Write-Log -Message "Beginning user creation..." -Level INFO -LogFile $LogFile
    $creationRow = 0
    foreach ($user in $finalList) {
        $creationRow++
        if (Test-ADDuplicates -User $user -LogFile $LogFile -RowNumber $creationRow) {
            Write-Host "Row #$($creationRow): Duplicate found for $($user.FirstNameRomaji) $($user.LastNameRomaji). Skipping." -ForegroundColor Red
            continue
        }

        $displayName = $user.displayName
        Write-Host "Creating user: $displayName" -ForegroundColor Green
        Write-Log -Message "Creating user: $displayName" -Level INFO -LogFile $LogFile

        $securePassword    = New-SecurePassword
        $userSam           = Get-SamAccountName -FirstNameRomaji $user.FirstNameRomaji -LastNameRomaji $user.LastNameRomaji
        $finalUserLogon    = Get-UserLogonName -Upn $user.upn
        $finalUserLogonUPN = Get-EmailAddress -Upn $user.upn

        New-ADUserFromRecord -User $user `
                             -UserLogonName $finalUserLogonUPN `
                             -SamAccountName $userSam `
                             -SecurePwd $securePassword `
                             -LogFile $LogFile `
                             -RowNumber $creationRow
    }

    Write-Host "`nAll valid users processed. Script finished." -ForegroundColor Cyan
    Write-Log -Message "Script execution complete." -Level INFO -LogFile $LogFile
}

###############################################################################
#                         MAIN SCRIPT EXECUTION                               #
###############################################################################
try {
    Invoke-OnboardUsers -CsvPath $CsvPath -Domain $Domain -OUPath $OUPath -LogFile $LogFilePath
}
catch {
    Write-Host "An error occurred: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}


