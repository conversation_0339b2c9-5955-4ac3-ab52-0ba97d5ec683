name         : "Company Portal"
publisher    : "Microsoft"
appVersion   : "1.0"
description  : "Microsoft Intune helps organizations manage access to their internal apps, data, and resources. Intune Company Portal is the app that lets you securely access those resources."
installExp   : "user"
featured     : false
supersede    : "none"
assignments  : 
  Intune-Users-Developers:
  - intent   : "available"
dependencies :
  Windows Package Manager :
  - version  : "1.3.2691"
  - type     : "AutoInstall"
commandLine  :
- install    : 'Company Portal.exe'
- uninstall  : 'PowerShell.exe -Command "winget uninstall 9WZDNCRFJ3PZ"'