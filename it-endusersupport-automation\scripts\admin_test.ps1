﻿#Requires -Version 5.1
#Requires -Modules ActiveDirectory
#Requires -RunAsAdministrator

<#
.SYNOPSIS
Creates an admin account based on a standard user's details.

.DESCRIPTION
This script automates the process of creating an admin account in Active Directory 
based on the details of a standard user. It includes validation of user inputs, 
logging actions, generating passwords, and sending email notifications.

.NOTES
Version:        1.1
Author:         <PERSON>
Creation Date:  2025-01-09
#>

#region Initialization
# Import required modules
Import-Module ActiveDirectory
Add-Type -AssemblyName System.Web

# Default parameter values
$script:LogDirectory = "C:\Temp\Scripts\Desktop Support\Logs"
$script:DefaultEmailFrom = "<EMAIL>"
$script:DefaultSmtpServer = "smtp.jeragm.com"
$script:DefaultDomain = "jeragm.com"
#endregion

#region Helper Functions
function Initialize-Logging {
    [CmdletBinding()]
    param (
        [ValidateNotNullOrEmpty()]
        [string]$StandardUserUPN
    )

    try {
        # Create log directory if it doesn't exist
        if (-not (Test-Path -Path $script:LogDirectory)) {
            New-Item -ItemType Directory -Path $script:LogDirectory -Force | Out-Null
        }

        # Generate unique log file name with timestamp and user
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $username = $StandardUserUPN.Split('@')[0]
        $script:CurrentLogPath = Join-Path $script:LogDirectory "Admin Account Creation For_${username}_${timestamp}.log"

        # Create new log file with header
        $separator = "=" * 100
        $header = @"
$separator
Log Started: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
Script Version: 1.1
Standard User: $StandardUserUPN
Created By: $env:USERNAME
Computer Name: $env:COMPUTERNAME
$separator
"@

        Set-Content -Path $script:CurrentLogPath -Value $header -Force
        Write-Log -Message "Logging initialized for user: $StandardUserUPN" -LogLevel "INFO"

        # Clean up old log files (keep last 30 days)
        $oldLogs = Get-ChildItem -Path $script:LogDirectory -Filter "AdminCreation_*.log" |
                   Where-Object { $_.LastWriteTime -lt (Get-Date).AddDays(-30) }
        
        foreach ($log in $oldLogs) {
            try {
                Remove-Item $log.FullName -Force
                Write-Log -Message "Removed old log file: $($log.Name)" -LogLevel "DEBUG"
            }
            catch {
                Write-Log -Message "Failed to remove old log file $($log.Name): $_" -LogLevel "WARNING"
            }
        }

        return $script:CurrentLogPath
    }
    catch {
        Write-Error "Failed to initialize logging: $_"
        throw
    }
}

function Send-EmailWithRetry {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$From,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$To,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$Subject,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$Body,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$SmtpServer,

        [string[]]$Attachments,

        [switch]$UseSSL,

        [int]$Port = 25
    )
    
    $maxRetries = 3
    $retryDelay = 2
    
    for ($i = 1; $i -le $maxRetries; $i++) {
        try {
            $params = @{
                From       = $From
                To         = $To
                Subject    = $Subject
                Body      = $Body
                SmtpServer = $SmtpServer
                Port      = $Port
                UseSSL    = $UseSSL.IsPresent
            }
            
            if ($Attachments) { 
                $params.Attachments = $Attachments 
            }
            
            # Add error handling for email parameters
            Write-Log -Message "Attempting to send email (Attempt $i of $maxRetries)" -LogLevel "DEBUG"
            Write-Log -Message "Email parameters: To=$To, From=$From, Subject=$Subject, SmtpServer=$SmtpServer" -LogLevel "DEBUG"
            
            Send-MailMessage @params -ErrorAction Stop
            
            Write-Log -Message "Email sent successfully to $To" -LogLevel "INFO"
            return $true
        }
        catch {
            $errorMessage = "Email attempt $i failed: $($_.Exception.Message)"
            Write-Log -Message $errorMessage -LogLevel "WARNING"
            
            if ($_.Exception.Message -match "secure connections") {
                Write-Log -Message "SSL connection failed, attempting without SSL..." -LogLevel "WARNING"
                $params.UseSSL = $false
                try {
                    Send-MailMessage @params -ErrorAction Stop
                    Write-Log -Message "Email sent successfully without SSL to $To" -LogLevel "INFO"
                    return $true
                }
                catch {
                    Write-Log -Message "Failed to send email without SSL: $($_.Exception.Message)" -LogLevel "ERROR"
                }
            }
            
            if ($i -lt $maxRetries) {
                $waitTime = $retryDelay * [Math]::Pow(2, $i-1)
                Write-Log -Message "Waiting $waitTime seconds before retry..." -LogLevel "INFO"
                Start-Sleep -Seconds $waitTime
            }
        }
    }
    
    Write-Log -Message "Failed to send email after $maxRetries attempts" -LogLevel "ERROR"
    return $false
}

# Sends an email notification with details about the admin account creation
function Send-EmailNotification {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$LogPath,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$AdminUserUPN,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$Password,

        [Parameter(Mandatory = $true)]
        [ValidatePattern('^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')]
        [string]$StandardUserEmail,

        [ValidateNotNullOrEmpty()]
        [string]$EmailFrom = $DefaultEmailFrom,

        [ValidateNotNullOrEmpty()]
        [string]$SmtpServer = $DefaultSmtpServer
    )

    # Retrieve the full name and username of the current user
    $currentUserFullName = GetCurrentUserName
    $currentUserSamAccountName = whoami

    # Get log content
    try {
        $logContent = Get-Content -Path $LogPath -Raw
        if ([string]::IsNullOrEmpty($logContent)) {
            Write-Log -Message "Warning: Log file is empty" -LogLevel "WARNING"
            $logContent = "No log content available."
        }
    }
    catch {
        Write-Log -Message "Error reading log file: $_" -LogLevel "ERROR"
        $logContent = "Error reading log content."
    }

    # Prepare shared email parameters
    $emailParams = @{
        From       = $EmailFrom
        SmtpServer = $SmtpServer
    }

    # Send notification to support team
    $supportEmailParams = $emailParams.Clone()
    $supportEmailParams += @{
        To         = "<EMAIL>"
        Subject    = "Admin Account Creation for $AdminUserUPN"
        Body       = @"
An admin account has been created for $AdminUserUPN by $currentUserFullName ($currentUserSamAccountName).

DETAILED LOG:
=============
$logContent

This is an automated notification.
"@
        Attachments = $LogPath
        UseSSL     = $false
        Port       = 25
    }

    # Send credentials to standard user
    $userEmailParams = $emailParams.Clone()
    $userEmailParams += @{
        To      = $StandardUserEmail
        Subject = "Your Admin Account Credentials"
        Body    = @"
Hello,

Your admin account has been created with the following credentials:

Username: $AdminUserUPN
Password: $Password

Please change this password upon your first login.

CREATION LOG:
============
$logContent

Best regards,
IT Support Team
"@
        UseSSL  = $false
        Port    = 25
    }

    # Send emails with retry and capture results
    $results = @(
        Send-EmailWithRetry @supportEmailParams
        Send-EmailWithRetry @userEmailParams
    )

    # Report results
    if ($results -notcontains $false) {
        $message = "Email notifications sent successfully."
    }
    else {
        $message = "Failed to send one or more email notifications."
    }
    
    Write-Host $message
}

function Send-DeletionEmailNotification {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$LogPath,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$AdminUserUPN,

        [Parameter(Mandatory = $true)]
        [ValidatePattern('^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')]
        [string]$StandardUserEmail,

        [ValidateNotNullOrEmpty()]
        [string]$EmailFrom = $DefaultEmailFrom,

        [ValidateNotNullOrEmpty()]
        [string]$SmtpServer = $DefaultSmtpServer
    )

    # Retrieve the full name and username of the current user
    $currentUserFullName = GetCurrentUserName
    $currentUserSamAccountName = whoami

    # Get log content
    try {
        $logContent = Get-Content -Path $LogPath -Raw
        if ([string]::IsNullOrEmpty($logContent)) {
            Write-Log -Message "Warning: Log file is empty" -LogLevel "WARNING"
            $logContent = "No log content available."
        }
    }
    catch {
        Write-Log -Message "Error reading log file: $_" -LogLevel "ERROR"
        $logContent = "Error reading log content."
    }

    # Prepare shared email parameters
    $emailParams = @{
        From       = $EmailFrom
        SmtpServer = $SmtpServer
    }

    # Send notification to support team
    $supportEmailParams = $emailParams.Clone()
    $supportEmailParams += @{
        To         = "<EMAIL>"
        Subject    = "Admin Account Deletion for $AdminUserUPN"
        Body       = @"
An admin account has been deleted: $AdminUserUPN by $currentUserFullName ($currentUserSamAccountName).

DETAILED LOG:
=============
$logContent

This is an automated notification.
"@
        Attachments = $LogPath
        UseSSL     = $false
        Port       = 25
    }

    # Send notification to standard user
    $userEmailParams = $emailParams.Clone()
    $userEmailParams += @{
        To      = $StandardUserEmail
        Subject = "Your Admin Account Has Been Deleted"
        Body    = @"
Hello,

Your admin account ($AdminUserUPN) has been deleted from Active Directory.

If you believe this was done in error, please contact IT Support.

DELETION LOG:
============
$logContent

Best regards,
IT Support Team
"@
        UseSSL  = $false
        Port    = 25
    }

    # Send emails with retry and capture results
    $results = @(
        Send-EmailWithRetry @supportEmailParams
        Send-EmailWithRetry @userEmailParams
    )

    # Report results
    if ($results -notcontains $false) {
        $message = "Email notifications sent successfully."
    }
    else {
        $message = "Failed to send one or more email notifications."
    }
    
    Write-Host $message
}

function Send-ResetEmailNotification {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$LogPath,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$AdminUserUPN,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$Password,

        [Parameter(Mandatory = $true)]
        [ValidatePattern('^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')]
        [string]$StandardUserEmail,

        [ValidateNotNullOrEmpty()]
        [string]$EmailFrom = $DefaultEmailFrom,

        [ValidateNotNullOrEmpty()]
        [string]$SmtpServer = $DefaultSmtpServer
    )

    # Retrieve the full name and username of the current user
    $currentUserFullName = GetCurrentUserName
    $currentUserSamAccountName = whoami

    # Get log content
    try {
        $logContent = Get-Content -Path $LogPath -Raw
        if ([string]::IsNullOrEmpty($logContent)) {
            Write-Log -Message "Warning: Log file is empty" -LogLevel "WARNING"
            $logContent = "No log content available."
        }
    }
    catch {
        Write-Log -Message "Error reading log file: $_" -LogLevel "ERROR"
        $logContent = "Error reading log content."
    }

    # Prepare shared email parameters
    $emailParams = @{
        From       = $EmailFrom
        SmtpServer = $SmtpServer
    }

    # Send notification to support team
    $supportEmailParams = $emailParams.Clone()
    $supportEmailParams += @{
        To         = "<EMAIL>"
        Subject    = "Admin Account Reset for $AdminUserUPN"
        Body       = @"
An admin account has been reset: $AdminUserUPN by $currentUserFullName ($currentUserSamAccountName).

DETAILED LOG:
=============
$logContent

This is an automated notification.
"@
        Attachments = $LogPath
        UseSSL     = $false
        Port       = 25
    }

    # Send reset notification to standard user
    $userEmailParams = $emailParams.Clone()
    $userEmailParams += @{
        To      = $StandardUserEmail
        Subject = "Your Admin Account Has Been Reset"
        Body    = @"
Hello,

Your admin account ($AdminUserUPN) has been reset with the following credentials:

Username: $AdminUserUPN
Password: $Password

Please change this password upon your first login.
The account is currently disabled and will need to be enabled by IT Support.

RESET LOG:
==========
$logContent

Best regards,
IT Support Team
"@
        UseSSL  = $false
        Port    = 25
    }

    # Send emails with retry and capture results
    $results = @(
        Send-EmailWithRetry @supportEmailParams
        Send-EmailWithRetry @userEmailParams
    )

    # Report results
    if ($results -notcontains $false) {
        $message = "Email notifications sent successfully."
    }
    else {
        $message = "Failed to send one or more email notifications."
    }
    
    Write-Host $message
}

# Retrieves the full name of the current user
function GetCurrentUserName {
    try {
        # Use [System.Security.Principal.WindowsIdentity]::GetCurrent().Name for domain\username
        $currentUserSamAccountName = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name.Split('\')[1]

        $currentUser = Get-ADUser -Identity $currentUserSamAccountName -Properties GivenName, Surname -ErrorAction Stop
        return "$($currentUser.GivenName) $($currentUser.Surname)"
    } catch {
        Write-Log -Message "Error retrieving current user name: $_" -LogLevel "ERROR"
        return "Unknown User"
    }
}

# Writes a log message with a timestamp and the current user's name
function Write-Log {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Message,

        [ValidateSet('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')]
        [string]$LogLevel = 'INFO'
    )

    # If CurrentLogPath is not set, create a temporary log
    if (-not $script:CurrentLogPath) {
        $script:CurrentLogPath = Join-Path $script:LogDirectory "AdminCreation_TEMP_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
    }

    # Get the current date and time with milliseconds
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss.fff"
    
    # Get user details
    $currentUserName = $env:USERNAME
    $currentUserFullName = GetCurrentUserName

    # Color coding for console output
    $colorMap = @{
        'DEBUG' = 'Gray'
        'INFO' = 'White'
        'WARNING' = 'Yellow'
        'ERROR' = 'Red'
        'CRITICAL' = 'DarkRed'
    }

    # Construct the log message
    $logMessage = "{0,-23} | {1,-15} | {2,-40} | {3,-8} | {4}" -f `
        $timestamp, `
        $currentUserName, `
        $currentUserFullName, `
        $LogLevel.ToUpper(), `
        $Message

    try {
        # Create directory if it doesn't exist
        $logDir = Split-Path -Path $script:CurrentLogPath -Parent
        if (-not (Test-Path -Path $logDir)) {
            New-Item -ItemType Directory -Path $logDir -Force | Out-Null
        }

        # Write to log file
        Add-Content -Path $script:CurrentLogPath -Value $logMessage
        
        # Console output
        Write-Host $logMessage -ForegroundColor $colorMap[$LogLevel]
    }
    catch {
        $errorMsg = "Failed to write log: $($_.Exception.Message)"
        Write-Error $errorMsg
        Write-Host $errorMsg -ForegroundColor Red
    }
}

function Get-ValidUPN {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $false)]
        [ValidateNotNullOrEmpty()]
        [string]$Domain = "jeragm.com",

        [Parameter(Mandatory = $false)]
        [switch]$ForDeletion
    )

    begin {
        [string]$currentUser = $env:USERNAME
        Write-Log "Starting UPN validation process by user: $currentUser"
    }

    process {
        do {
            try {
                Write-Log "Requesting UPN input from user"
                [string]$upn = Read-Host "Enter the UPN of the existing standard account (e.g., user.name@$Domain)"
                
                if ([string]::IsNullOrWhiteSpace($upn)) {
                    throw "UPN cannot be empty or whitespace"
                }

                Write-Log "Sanitizing UPN input: $upn"
                $upn = [System.Web.HttpUtility]::HtmlEncode($upn).Trim()
                $upn = $upn -replace '[^\w.-@]', ''
                Write-Log "Sanitized UPN result: $upn"

                [string]$upnPattern = '^[\w][\w\.-]{1,62}[\w]@[\w][\w-]{1,62}\.[a-zA-Z]{2,}$'
                
                if (-not $upn -match $upnPattern) {
                    Write-Host "Invalid UPN format. Only alphanumeric characters, dots and hyphens allowed." -ForegroundColor Red
                    Write-Log "Invalid UPN format detected: $upn"
                    $isValid = $false
                    continue
                }

                if (-not $upn.Contains('@')) {
                    $upn = "$upn@$Domain"
                    Write-Log "Domain appended to UPN: $upn"
                }
                elseif (-not $upn.EndsWith("@$Domain")) {
                    Write-Host "UPN must end with @$Domain" -ForegroundColor Red
                    Write-Log "Invalid domain in UPN: $upn"
                    $isValid = $false
                    continue
                }

                try {
                    $existingUser = Get-ADUser -Filter "UserPrincipalName -eq '$upn'" -ErrorAction Stop
                    if (-not $existingUser) {
                        Write-Host "Standard user account not found in Active Directory" -ForegroundColor Red
                        Write-Log "UPN validation failed - Standard user not found: $upn"
                        $isValid = $false
                        continue
                    }

                    # Check if admin account exists
                    $adminSamAccountName = "$($existingUser.SamAccountName)-a"
                    $adminAccountExists = Get-ADUser -Filter "SamAccountName -eq '$adminSamAccountName'" -ErrorAction SilentlyContinue
                    
                    if ($ForDeletion) {
                        if (-not $adminAccountExists) {
                            Write-Host "No admin account exists for this user" -ForegroundColor Red
                            Write-Log "UPN validation failed - No admin account exists for: $upn"
                            $isValid = $false
                            continue
                        }
                    } else {
                        if ($adminAccountExists) {
                            Write-Host "Admin account already exists for this user" -ForegroundColor Red
                            Write-Log "UPN validation failed - Admin account already exists for: $upn"
                            $isValid = $false
                            continue
                        }
                    }

                    $isValid = $true
                }
                catch {
                    Write-Log "Error checking AD for existing UPN: $_"
                    throw "Failed to verify UPN in AD: $_"
                }
            }
            catch {
                Write-Host "Error processing UPN: $_" -ForegroundColor Red
                Write-Log "Error in UPN validation process: $_"
                $isValid = $false
            }
        } while (-not $isValid)
    }

    end {
        Write-Log "UPN validation completed successfully. Final UPN: $upn"
        return [string]$upn.ToLower()
    }
}

function Get-ValidUPN {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $false)]
        [ValidateNotNullOrEmpty()]
        [string]$Domain = "jeragm.com",

        [Parameter(Mandatory = $false)]
        [switch]$ForDeletion,

        [Parameter(Mandatory = $false)]
        [switch]$ForReset
    )

    begin {
        [string]$currentUser = $env:USERNAME
        Write-Log "Starting UPN validation process by user: $currentUser"
    }

    process {
        do {
            try {
                Write-Log "Requesting UPN input from user"
                [string]$upn = Read-Host "Enter the UPN of the existing standard account (e.g., user.name@$Domain)"
                
                if ([string]::IsNullOrWhiteSpace($upn)) {
                    throw "UPN cannot be empty or whitespace"
                }

                Write-Log "Sanitizing UPN input: $upn"
                $upn = [System.Web.HttpUtility]::HtmlEncode($upn).Trim()
                $upn = $upn -replace '[^\w.-@]', ''
                Write-Log "Sanitized UPN result: $upn"

                [string]$upnPattern = '^[\w][\w\.-]{1,62}[\w]@[\w][\w-]{1,62}\.[a-zA-Z]{2,}$'
                
                if (-not $upn -match $upnPattern) {
                    Write-Host "Invalid UPN format. Only alphanumeric characters, dots and hyphens allowed." -ForegroundColor Red
                    Write-Log "Invalid UPN format detected: $upn"
                    $isValid = $false
                    continue
                }

                if (-not $upn.Contains('@')) {
                    $upn = "$upn@$Domain"
                    Write-Log "Domain appended to UPN: $upn"
                }
                elseif (-not $upn.EndsWith("@$Domain")) {
                    Write-Host "UPN must end with @$Domain" -ForegroundColor Red
                    Write-Log "Invalid domain in UPN: $upn"
                    $isValid = $false
                    continue
                }

                try {
                    $existingUser = Get-ADUser -Filter "UserPrincipalName -eq '$upn'" -ErrorAction Stop
                    if (-not $existingUser) {
                        Write-Host "Standard user account not found in Active Directory" -ForegroundColor Red
                        Write-Log "UPN validation failed - Standard user not found: $upn"
                        $isValid = $false
                        continue
                    }

                    # Check if admin account exists
                    $adminSamAccountName = "$($existingUser.SamAccountName)-a"
                    $adminAccountExists = Get-ADUser -Filter "SamAccountName -eq '$adminSamAccountName'" -ErrorAction SilentlyContinue
                    
                    if ($ForDeletion -or $ForReset) {
                        if (-not $adminAccountExists) {
                            Write-Host "No admin account exists for this user" -ForegroundColor Red
                            Write-Log "UPN validation failed - No admin account exists for: $upn"
                            $isValid = $false
                            continue
                        }
                    } 
                    else {
                        # For creation
                        if ($adminAccountExists) {
                            Write-Host "Admin account already exists for this user" -ForegroundColor Red
                            Write-Log "UPN validation failed - Admin account already exists for: $upn"
                            $isValid = $false
                            continue
                        }
                    }

                    $isValid = $true
                }
                catch {
                    Write-Log "Error checking AD for existing UPN: $_"
                    throw "Failed to verify UPN in AD: $_"
                }
            }
            catch {
                Write-Host "Error processing UPN: $_" -ForegroundColor Red
                Write-Log "Error in UPN validation process: $_"
                $isValid = $false
            }
        } while (-not $isValid)
    }

    end {
        Write-Log "UPN validation completed successfully. Final UPN: $upn"
        return [string]$upn.ToLower()
    }
}

function Get-ADUserDetails {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$UserUPN
    )

    begin {
        Write-Log -Message "Starting AD user details retrieval for UPN: $UserUPN" -LogLevel "INFO"
    }

    process {
        try {
            # Sanitize input
            $UserUPN = $UserUPN.Trim()
            
            # Validate UPN format
            if (-not ($UserUPN -match '^[\w][\w\.-]{0,63}@[\w][\w-]{0,63}\.[a-zA-Z]{2,}$')) {
                throw "Invalid UPN format: $UserUPN"
            }

            # Get user details with specific properties needed
            $properties = @(
                'GivenName',
                'Surname',
                'DisplayName',
                'SamAccountName',
                'UserPrincipalName',
                'EmailAddress',
                'Enabled',
                'DistinguishedName'
            )

            $user = Get-ADUser -Filter "UserPrincipalName -eq '$UserUPN'" `
                             -Properties $properties `
                             -ErrorAction Stop

            if ($null -eq $user) {
                throw "User not found in AD"
            }

            # Verify account is enabled
            if (-not $user.Enabled) {
                Write-Log -Message "Warning: User account is disabled: $UserUPN" -LogLevel "WARNING"
            }

            Write-Log -Message "Successfully retrieved AD user details for: $UserUPN" -LogLevel "INFO"
            return $user

        }
        catch [Microsoft.ActiveDirectory.Management.ADIdentityNotFoundException] {
            Write-Log -Message "User not found in AD: $UserUPN" -LogLevel "ERROR"
            Write-Host "User not found in Active Directory." -ForegroundColor Red
            return $null
        }
        catch [Microsoft.ActiveDirectory.Management.ADServerDownException] {
            Write-Log -Message "AD server connection failed for user lookup: $UserUPN" -LogLevel "ERROR"
            Write-Host "Unable to connect to Active Directory server." -ForegroundColor Red
            return $null
        }
        catch {
            Write-Log -Message "Error retrieving AD user details for $UserUPN : $_" -LogLevel "ERROR"
            Write-Host "Failed to retrieve user details: $_" -ForegroundColor Red
            return $null
        }
    }

    end {
        Write-Log -Message "Completed AD user details retrieval process" -LogLevel "INFO"
    }
}

#Selects ths OU the Admin account will be created into
function Select-OU {
    <#
    .SYNOPSIS
    Prompts the user to select an Organizational Unit (OU) for an admin account.

    .DESCRIPTION
    This function allows the user to select an OU from a predefined list and logs the selection process.
    It returns the corresponding OU path based on the user's input.

    .OUTPUTS
    String - The selected OU path.
    #>

    [CmdletBinding()]
    param ()

    # Log the start of the OU selection process
    Write-Log -Message "Starting OU selection process" -LogLevel "DEBUG"

    # Initialize the OU path variable
    [string]$ouPath = $null

    try {
        do {
            # Prompt the user for OU selection
            Write-Host "Select the OU for the admin account: 1 for London, 2 for Singapore."
            [string]$selection = Read-Host "Enter 1 or 2"
            Write-Log -Message "User entered selection: $selection" -LogLevel "DEBUG"

            # Determine the OU path based on user selection
            switch ($selection) {
                "1" {
                    $ouPath = "OU=London,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com"
                    Write-Log -Message "Selected OU: London" -LogLevel "INFO"
                }
                "2" {
                    $ouPath = "OU=Singapore,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com"
                    Write-Log -Message "Selected OU: Singapore" -LogLevel "INFO"
                }
                default {
                    Write-Host "Invalid selection. Please select either 1 or 2."
                    Write-Log -Message "Invalid OU selection: $selection" -LogLevel "WARNING"
                }
            }
        } while (-not $ouPath)
    }
    catch {
        # Log any exceptions that occur during the selection process
        Write-Log -Message "An error occurred during OU selection: $_" -LogLevel "ERROR"
        throw
    }

    # Log the completion of the OU selection process
    Write-Log -Message "OU selection process completed with OU: $ouPath" -LogLevel "DEBUG"
    
    # Return the selected OU path
    return $ouPath
}
#endregion

function Reset-StdAdminAccount {
    [CmdletBinding()]
    param()

    try {
        # Get standard user UPN first (with ForReset flag)
        $standardUserUPN = Get-ValidUPN -Domain $script:DefaultDomain -ForReset
        
        # Initialize logging with the standard user's UPN
        $logPath = Initialize-Logging -StandardUserUPN $standardUserUPN
        Write-Log -Message "Starting admin account reset process" -LogLevel "INFO"

        # Get standard user details
        $standardUser = Get-ADUserDetails -UserUPN $standardUserUPN

        if ($null -eq $standardUser) {
            throw "Unable to retrieve standard user details"
        }

        # Construct admin account SamAccountName
        $adminSamAccountName = "$($standardUser.SamAccountName)-a"

        # Check if admin account exists and get full details
        $adminAccount = Get-ADUser -Filter "SamAccountName -eq '$adminSamAccountName'" `
                                 -Properties DisplayName, UserPrincipalName, DistinguishedName, Created, Modified, Enabled `
                                 -ErrorAction Stop

        if ($null -eq $adminAccount) {
            throw "No admin account found for user $($standardUser.DisplayName) (Expected: $adminSamAccountName)"
        }

        # Prompt for confirmation with detailed information
        Write-Host "`nAdmin Account Details:" -ForegroundColor Yellow
        Write-Host "Display Name: $($adminAccount.DisplayName)" -ForegroundColor Cyan
        Write-Host "Username: $($adminAccount.UserPrincipalName)" -ForegroundColor Cyan
        Write-Host "SAM Account Name: $($adminAccount.SamAccountName)" -ForegroundColor Cyan
        Write-Host "Distinguished Name: $($adminAccount.DistinguishedName)" -ForegroundColor Cyan
        Write-Host "Account Status: $(if($adminAccount.Enabled){'Enabled'}else{'Disabled'})" -ForegroundColor Cyan
        Write-Host "Created: $($adminAccount.Created)" -ForegroundColor Cyan
        Write-Host "Last Modified: $($adminAccount.Modified)" -ForegroundColor Cyan

        $confirmation = Read-Host "`nAre you sure you want to reset this admin account? (Y/N)"

        if ($confirmation -eq 'Y') {
            # Generate new password
            $newPassword = Generate-Password
            $securePassword = ConvertTo-SecureString $newPassword -AsPlainText -Force

            # Reset the account
            Set-ADAccountPassword -Identity $adminAccount.DistinguishedName -NewPassword $securePassword -Reset -ErrorAction Stop
            Set-ADUser -Identity $adminAccount.DistinguishedName -ChangePasswordAtLogon $true -Enabled $false -ErrorAction Stop

            $successMessage = "Admin account $($adminAccount.UserPrincipalName) has been successfully reset"
            Write-Host "`n$successMessage" -ForegroundColor Green
            Write-Log -Message $successMessage -LogLevel "INFO"

            # Send email notifications
            if (-not [string]::IsNullOrEmpty($standardUser.EmailAddress)) {
                $emailParams = @{
                    LogPath = $script:CurrentLogPath
                    AdminUserUPN = $adminAccount.UserPrincipalName
                    Password = $newPassword
                    StandardUserEmail = $standardUser.EmailAddress
                }
                Send-ResetEmailNotification @emailParams
            }
            else {
                Write-Log -Message "No email address found for standard user" -LogLevel "WARNING"
            }
        }
        else {
            Write-Host "`nReset cancelled by user" -ForegroundColor Yellow
            Write-Log -Message "Admin account reset cancelled by user" -LogLevel "INFO"
        }
    }
    catch {
        $errorMessage = "Failed to reset admin account: $_"
        Write-Log -Message $errorMessage -LogLevel "ERROR"
        Write-Host $errorMessage -ForegroundColor Red
        throw
    }
}


function Create-StdAdminAccount {
    [CmdletBinding()]
    param()

    try {
        # Get standard user UPN first
        $standardUserUPN = Get-ValidUPN -Domain $script:DefaultDomain
        
        # Initialize logging with the standard user's UPN
        $logPath = Initialize-Logging -StandardUserUPN $standardUserUPN
        Write-Log -Message "Starting admin account creation process" -LogLevel "INFO"

        # Get standard user details
        $standardUser = Get-ADUserDetails -UserUPN $standardUserUPN

        if ($null -eq $standardUser) {
            throw "Unable to retrieve standard user details"
        }

        # Select OU and prepare account details
        $ouPath = Select-OU
        $displayName = "$($standardUser.GivenName) $($standardUser.Surname) (Admin Account)" 
        $logonName = "$($standardUser.SamAccountName)-a"
        $adminUserUPN = "$logonName@$script:DefaultDomain"

        # Show account details that will be created
        Write-Host "`nNew Admin Account Details:" -ForegroundColor Yellow
        Write-Host "Display Name: $displayName" -ForegroundColor Cyan
        Write-Host "Username: $adminUserUPN" -ForegroundColor Cyan
        Write-Host "SAM Account Name: $logonName" -ForegroundColor Cyan
        Write-Host "OU Path: $ouPath" -ForegroundColor Cyan

        # Prompt for confirmation
        $confirmation = Read-Host "`nDo you want to create this admin account? (Y/N)"

        if ($confirmation -eq 'Y') {
            # Generate password and create account
            $password = Generate-Password
            $securePassword = ConvertTo-SecureString $password -AsPlainText -Force

            $newUserParams = @{
                Name = $displayName
                GivenName = $standardUser.GivenName
                Surname = $standardUser.Surname
                DisplayName = $displayName
                Description = "Admin account for $($standardUser.DisplayName)"
                UserPrincipalName = $adminUserUPN
                SamAccountName = $logonName
                AccountPassword = $securePassword
                Path = $ouPath
                Enabled = $false
            }

            # Create the admin account
            New-ADUser @newUserParams -ErrorAction Stop

            $successMessage = "Admin account created successfully: $adminUserUPN"
            Write-Host "`n$successMessage" -ForegroundColor Green
            Write-Log -Message $successMessage -LogLevel "INFO"

            # Send email notifications using Send-EmailNotification
            if (-not [string]::IsNullOrEmpty($standardUser.EmailAddress)) {
                $emailParams = @{
                    LogPath = $script:CurrentLogPath
                    AdminUserUPN = $adminUserUPN
                    Password = $password
                    StandardUserEmail = $standardUser.EmailAddress
                }
                Send-EmailNotification @emailParams
            }
            else {
                Write-Log -Message "No email address found for standard user" -LogLevel "WARNING"
            }
        }
        else {
            Write-Host "`nAccount creation cancelled by user" -ForegroundColor Yellow
            Write-Log -Message "Admin account creation cancelled by user" -LogLevel "INFO"
        }
    }
    catch {
        $errorMessage = "Failed to create admin account: $_"
        Write-Log -Message $errorMessage -LogLevel "ERROR"
        Write-Host $errorMessage -ForegroundColor Red
        throw
    }
}

function Remove-StdAdminAccount {
    [CmdletBinding()]
    param()

    try {
        # Get standard user UPN first (with ForDeletion flag)
        $standardUserUPN = Get-ValidUPN -Domain $script:DefaultDomain -ForDeletion
        
        # Initialize logging with the standard user's UPN
        $logPath = Initialize-Logging -StandardUserUPN $standardUserUPN
        Write-Log -Message "Starting admin account deletion process" -LogLevel "INFO"

        # Get standard user details
        $standardUser = Get-ADUserDetails -UserUPN $standardUserUPN

        if ($null -eq $standardUser) {
            throw "Unable to retrieve standard user details"
        }

        # Construct admin account SamAccountName
        $adminSamAccountName = "$($standardUser.SamAccountName)-a"

        # Check if admin account exists and get full details
        $adminAccount = Get-ADUser -Filter "SamAccountName -eq '$adminSamAccountName'" `
                                 -Properties DisplayName, UserPrincipalName, DistinguishedName, Created, Modified `
                                 -ErrorAction Stop

        if ($null -eq $adminAccount) {
            throw "No admin account found for user $($standardUser.DisplayName) (Expected: $adminSamAccountName)"
        }

        # Prompt for confirmation with detailed information
        Write-Host "`nAdmin Account Details:" -ForegroundColor Yellow
        Write-Host "Display Name: $($adminAccount.DisplayName)" -ForegroundColor Cyan
        Write-Host "Username: $($adminAccount.UserPrincipalName)" -ForegroundColor Cyan
        Write-Host "SAM Account Name: $($adminAccount.SamAccountName)" -ForegroundColor Cyan
        Write-Host "Distinguished Name: $($adminAccount.DistinguishedName)" -ForegroundColor Cyan
        Write-Host "Created: $($adminAccount.Created)" -ForegroundColor Cyan
        Write-Host "Last Modified: $($adminAccount.Modified)" -ForegroundColor Cyan

        $confirmation = Read-Host "`nAre you sure you want to delete this admin account? (Y/N)"

        if ($confirmation -eq 'Y') {
            # Remove the admin account
            Remove-ADUser -Identity $adminAccount.DistinguishedName -Confirm:$false -ErrorAction Stop

            $successMessage = "Admin account $($adminAccount.UserPrincipalName) has been successfully deleted"
            Write-Host "`n$successMessage" -ForegroundColor Green
            Write-Log -Message $successMessage -LogLevel "INFO"

            # Send email notifications using Send-EmailNotification
           # Replace the email notification section in Remove-StdAdminAccount with:
if (-not [string]::IsNullOrEmpty($standardUser.EmailAddress)) {
    $emailParams = @{
        LogPath = $script:CurrentLogPath
        AdminUserUPN = $adminAccount.UserPrincipalName
        StandardUserEmail = $standardUser.EmailAddress
    }
    Send-DeletionEmailNotification @emailParams
}
else {
    Write-Log -Message "No email address found for standard user" -LogLevel "WARNING"
}
        }
        else {
            Write-Host "`nDeletion cancelled by user" -ForegroundColor Yellow
            Write-Log -Message "Admin account deletion cancelled by user" -LogLevel "INFO"
        }
    }
    catch {
        $errorMessage = "Failed to delete admin account: $_"
        Write-Log -Message $errorMessage -LogLevel "ERROR"
        Write-Host $errorMessage -ForegroundColor Red
        throw
    }
}

# Main script execution
try {
    # Prompt user for action
    Write-Host "Select an action:"
    Write-Host "1. Create Admin Account"
    Write-Host "2. Delete Admin Account"
    Write-Host "3. Reset Admin Account Password"
    $choice = Read-Host "`nEnter your choice (1, 2, or 3)"

    switch ($choice) {
        "1" { Create-StdAdminAccount }
        "2" { Remove-StdAdminAccount }
        "3" { Reset-StdAdminAccount }
        default { 
            Write-Host "Invalid choice. Please select 1, 2, or 3." -ForegroundColor Red 
            exit
        }
    }
}
catch {
    Write-Host "Script execution failed: $_" -ForegroundColor Red
}
finally {
    Read-Host -Prompt "Press Enter to exit"
}