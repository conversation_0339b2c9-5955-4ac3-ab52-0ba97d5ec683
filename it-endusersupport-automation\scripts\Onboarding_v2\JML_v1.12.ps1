#Requires -Version 5.1
#Requires -Modules ActiveDirectory

<#
.SYNOPSIS
JML (<PERSON><PERSON>, Mover, Leaver) Admin Account Management System v1.12

.DESCRIPTION
This is the main entry point for the JML Admin Account Management System. It provides
a comprehensive, modular solution for creating, deleting, and resetting admin accounts
in Active Directory with full Jira integration, email notifications, and enterprise-grade
security features.

Key Features:
- Modular architecture with 8 specialized modules
- Zero-configuration deployment with intelligent defaults
- Comprehensive security with data redaction and secure credential storage
- Full Jira integration with ticket validation and automated updates
- Email notifications with retry logic and SSL fallback
- Comprehensive audit trails and logging
- Enterprise-grade error handling and recovery

.PARAMETER ConfigPath
Path to the configuration file. Defaults to 'AdminAccountConfig.psd1' in script directory.

.PARAMETER LogLevel
Logging level: DEBUG, INFO, WARNING, ERROR, CRITICAL. Defaults to INFO.

.PARAMETER SkipJiraIntegration
Switch to skip Jira integration for testing or offline scenarios.

.PARAMETER ShowVersion
Switch to display version information and exit.

.PARAMETER RunSetup
Switch to run the setup utility for initial configuration.

.EXAMPLE
.\JML_v1.12.ps1
Runs the script with default settings and interactive menu.

.EXAMPLE
.\JML_v1.12.ps1 -LogLevel DEBUG -SkipJiraIntegration
Runs with debug logging and no Jira integration.

.EXAMPLE
.\JML_v1.12.ps1 -RunSetup
Runs the setup utility for initial configuration.

.EXAMPLE
.\JML_v1.12.ps1 -ShowVersion
Displays version information and exits.

.NOTES
Version:        1.12
Author:         Emmanuel Akinjomo
Creation Date:  2025-01-09
Last Modified:  2025-01-09
Security Level: Enhanced with comprehensive data protection

Required Permissions:
- Active Directory: User creation rights in target OUs
- SMTP: Send email permissions
- File System: Read/Write access to log directory
- Jira: API access with comment and attachment permissions

Dependencies:
- PowerShell 5.1 or higher
- ActiveDirectory module
- Microsoft.PowerShell.SecretManagement module (recommended)
- Microsoft.PowerShell.SecretStore module (recommended)

Module Architecture:
- JML-Configuration.psm1: Configuration management and intelligent defaults
- JML-Security.psm1: Data redaction, credential management, hashing
- JML-Logging.psm1: Secure logging with audit trails
- JML-Utilities.psm1: General utility functions
- JML-ActiveDirectory.psm1: AD operations and user management
- JML-Email.psm1: Email notifications and SMTP operations
- JML-Jira.psm1: Jira integration and API operations
- JML-Setup.psm1: Setup and environment validation
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [ValidateScript({
        if (-not (Test-Path $_ -PathType Leaf)) {
            throw "Configuration file not found: $_"
        }
        if (-not ($_ -match '\.psd1$')) {
            throw "Configuration file must be a PowerShell Data file (.psd1)"
        }
        return $true
    })]
    [string]$ConfigPath,

    [Parameter(Mandatory = $false)]
    [ValidateSet('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')]
    [string]$LogLevel = 'INFO',

    [Parameter(Mandatory = $false)]
    [switch]$SkipJiraIntegration,

    [Parameter(Mandatory = $false)]
    [switch]$ShowVersion,

    [Parameter(Mandatory = $false)]
    [switch]$RunSetup
)

# Determine script directory robustly
$ScriptDirectory = if ($PSScriptRoot) {
    $PSScriptRoot
} elseif ($MyInvocation.MyCommand.Path) {
    Split-Path -Parent $MyInvocation.MyCommand.Path
} elseif ($script:MyInvocation.MyCommand.Path) {
    Split-Path -Parent $script:MyInvocation.MyCommand.Path
} else {
    # Fallback to current directory
    Get-Location | Select-Object -ExpandProperty Path
}

# Set default ConfigPath if not provided
if (-not $ConfigPath) {
    $ConfigPath = Join-Path $ScriptDirectory "AdminAccountConfig.psd1"
}

#region Module Imports and Initialization

# Script constants
$script:JMLVersion = "1.12"
$script:JMLBuildDate = "2025-01-09"

# Import required modules in dependency order
$ModulePath = Join-Path $ScriptDirectory "Modules"

Write-Host "Loading JML modules..." -ForegroundColor Cyan
$modulesToLoad = @(
    "JML-Configuration.psm1",
    "JML-Security.psm1",
    "JML-Logging.psm1",
    "JML-Utilities.psm1",
    "JML-Setup.psm1",
    "JML-ActiveDirectory.psm1",
    "JML-Email.psm1",
    "JML-Jira.psm1"
)

$allModulesLoaded = $true
foreach ($moduleName in $modulesToLoad) {
    $modulePath = Join-Path $ModulePath $moduleName
    try {
        Import-Module $modulePath -Force -ErrorAction Stop
        Write-Host "  $moduleName loaded successfully." -ForegroundColor Green
    }
    catch {
        Write-Error "Failed to load module '$moduleName': $($_.Exception.Message)"
        Write-Host "  Please ensure the file exists and is a valid PowerShell module in the correct directory." -ForegroundColor Red
        Write-Host "  Module Path: $modulePath" -ForegroundColor Gray
        $allModulesLoaded = $false
    }
}

if ($allModulesLoaded) {
    Write-Host "All JML modules loaded successfully." -ForegroundColor Green
} else {
    Write-Host "One or more JML modules failed to load. Please review the errors above and ensure all modules are present and valid." -ForegroundColor Red
    exit 1
}
catch {
    Write-Error "Failed to load JML modules: $($_.Exception.Message)"
    Write-Host "Please ensure all module files are present in the Modules directory." -ForegroundColor Red
    Write-Host "Run with -RunSetup to validate the environment." -ForegroundColor Yellow
    exit 1
}

# Global script state
$script:Config = $null
$script:CurrentLogPath = $null
$script:ExecutionContext = @{
    StartTime = Get-Date
    ExecutingUser = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name
    ComputerName = $env:COMPUTERNAME
    PowerShellVersion = $PSVersionTable.PSVersion.ToString()
    ScriptPath = $PSCommandPath
    ScriptDirectory = $ScriptDirectory
    Version = $script:JMLVersion
    BuildDate = $script:JMLBuildDate
}

#endregion

#region Version and Setup Handling

# Handle version display
if ($ShowVersion) {
    $versionInfo = Get-JMLVersion
    
    Write-Host ""
    Write-Host "JML Admin Account Management System" -ForegroundColor Cyan
    Write-Host "Version: $($versionInfo.JMLVersion)" -ForegroundColor Green
    Write-Host "Build Date: $script:JMLBuildDate" -ForegroundColor Green
    Write-Host "PowerShell: $($versionInfo.PowerShellVersion)" -ForegroundColor Yellow
    Write-Host "Operating System: $($versionInfo.OperatingSystem)" -ForegroundColor Yellow
    Write-Host "Current User: $($versionInfo.CurrentUser)" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Module Status:" -ForegroundColor Cyan
    
    foreach ($module in $versionInfo.Modules.GetEnumerator()) {
        $status = if ($module.Value.Loaded) { "[OK]" } else { "[MISSING]" }
        $color = if ($module.Value.Loaded) { "Green" } else { "Red" }
        Write-Host "  $($module.Key): $($module.Value.Version) $status" -ForegroundColor $color
    }
    
    Write-Host ""
    exit 0
}

# Handle setup mode
if ($RunSetup) {
    Write-Host ""
    Write-Host "JML Setup Utility" -ForegroundColor Cyan
    Write-Host "Version $script:JMLVersion" -ForegroundColor Green
    Write-Host "=" * 50 -ForegroundColor Gray
    Write-Host ""
    
    $setupChoice = Read-Host @"
Select setup options:
1. Validate environment only
2. Setup credentials only  
3. Full setup (validate + credentials + install missing modules)
4. Exit

Enter your choice (1-4)
"@

    switch ($setupChoice) {
        "1" { 
            $result = Start-JMLSetup -ValidateEnvironment
        }
        "2" { 
            $result = Start-JMLSetup -SetupCredentials
        }
        "3" { 
            $result = Start-JMLSetup -ValidateEnvironment -SetupCredentials -InstallMissingModules
        }
        "4" { 
            Write-Host "Setup cancelled." -ForegroundColor Yellow
            exit 0
        }
        default { 
            Write-Host "Invalid choice. Exiting." -ForegroundColor Red
            exit 1
        }
    }
    
    if ($result) {
        Write-Host ""
        Write-Host "Setup completed successfully! You can now run the main script." -ForegroundColor Green
    } else {
        Write-Host ""
        Write-Host "Setup completed with issues. Please review the messages above." -ForegroundColor Yellow
    }
    
    exit 0
}

#endregion

#region Configuration Initialization

try {
    Write-Progress -Activity "Initializing JML System" -Status "Loading configuration..." -PercentComplete 10
    
    # Initialize configuration
    $configInitialized = Initialize-SmartConfiguration -ConfigPath $ConfigPath
    
    if (-not $configInitialized) {
        throw "Failed to initialize configuration"
    }
    
    # Get the loaded configuration
    $script:Config = Get-ModuleConfiguration
    
    if (-not $script:Config) {
        throw "Configuration not available after initialization"
    }
    
    Write-Progress -Activity "Initializing JML System" -Status "Configuration loaded successfully" -PercentComplete 30
}
catch {
    Write-Error "Configuration initialization failed: $($_.Exception.Message)"
    Write-Host "Try running with -RunSetup to validate your environment." -ForegroundColor Yellow
    exit 1
}

#endregion

#region Core Admin Account Functions

<#
.SYNOPSIS
Creates a new admin account based on a standard user's details.

.DESCRIPTION
Orchestrates the complete admin account creation process including user validation,
Jira ticket processing, account creation, email notifications, and audit logging.

.PARAMETER TicketKey
Optional Jira ticket key for automated processing.

.EXAMPLE
New-AdminAccount -TicketKey "HELP-12345"

.NOTES
This is the main orchestration function that coordinates all modules.
#>
function New-AdminAccount {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [ValidatePattern('^[A-Z]+-\d+$')]
        [string]$TicketKey
    )

    try {
        Write-Host ""
        Write-Host "=== ADMIN ACCOUNT CREATION ===" -ForegroundColor Cyan
        Write-Host "JML System v$script:JMLVersion" -ForegroundColor Green
        Write-Host ""

        # Initialize logging for this operation
        $standardUserUPN = if ($TicketKey) {
            # Get UPN from Jira ticket if provided
            Write-Host "Processing Jira ticket: $TicketKey" -ForegroundColor Cyan

            if (-not $SkipJiraIntegration) {
                # Initialize Jira connection
                $jiraCredential = Get-SecureCredential -CredentialName "JiraApiToken" -Purpose "Jira API access"
                $jiraUsername = Get-SecureCredential -CredentialName "JiraUsername" -Purpose "Jira username"

                if ($jiraCredential -and $jiraUsername) {
                    $jiraCred = New-Object System.Management.Automation.PSCredential($jiraUsername, $jiraCredential)
                    Initialize-JiraConnection -ServerUrl $script:Config.Jira.ServerUrl -Credential $jiraCred -TestConnection

                    # Validate ticket
                    $ticketValidation = Test-JiraTicketValidation -TicketKey $TicketKey -ExpectedWorkType $script:Config.Jira.ExpectedWorkTypes.CreateAdmin

                    if (-not $ticketValidation.IsValid) {
                        throw "Jira ticket validation failed: $($ticketValidation.ErrorMessage)"
                    }

                    # Extract UPN from ticket
                    $firstName = $ticketValidation.Fields.FirstName
                    $lastName = $ticketValidation.Fields.LastName

                    if ($firstName -and $lastName) {
                        New-StandardUPN -FirstName $firstName -LastName $lastName -Domain $script:Config.ScriptSettings.DefaultDomain
                    } else {
                        throw "Could not extract user details from Jira ticket"
                    }
                } else {
                    throw "Jira credentials not available. Run setup to configure credentials."
                }
            } else {
                Read-Host "Enter the UPN of the user for admin account creation"
            }
        } else {
            # Interactive mode
            Get-ValidatedUPN -Domain $script:Config.ScriptSettings.DefaultDomain
        }

        # Initialize secure logging
        $script:CurrentLogPath = Initialize-SecureLogging -StandardUserUPN $standardUserUPN

        Write-SecureLog -Message "Starting admin account creation process" -LogLevel "INFO" -AuditTrail @{
            Operation = "AdminAccountCreationStart"
            TargetUser = Get-StringHash -InputString $standardUserUPN
            TicketKey = $TicketKey
            ExecutingUser = $script:ExecutionContext.ExecutingUser
            Version = $script:JMLVersion
        }

        # Get user details from Active Directory
        Write-Host "Retrieving user details from Active Directory..." -ForegroundColor Yellow
        $standardUser = Get-OptimizedADUserDetails -UserUPN $standardUserUPN -UseCache

        if (-not $standardUser) {
            throw "User not found in Active Directory: $standardUserUPN"
        }

        Write-SecureLog -Message "Standard user found in Active Directory" -LogLevel "INFO" -AuditTrail @{
            Operation = "StandardUserFound"
            UserDisplayName = Protect-SensitiveData -Text $standardUser.DisplayName
            UserEnabled = $standardUser.Enabled
        }

        # Check if admin account already exists
        $adminSamAccountName = "$($standardUser.SamAccountName)-a"
        $existingAdminAccount = Get-ADUser -Filter "SamAccountName -eq '$adminSamAccountName'" -ErrorAction SilentlyContinue

        if ($existingAdminAccount) {
            $message = "Admin account already exists for this user: $adminSamAccountName"
            Write-Host $message -ForegroundColor Red
            Write-SecureLog -Message $message -LogLevel "ERROR"

            if ($TicketKey -and -not $SkipJiraIntegration) {
                Add-EnhancedJiraComment -TicketKey $TicketKey -CommentText "[ERROR] Admin account creation failed: Account already exists ($adminSamAccountName)" -UseADF -IncludeTimestamp
            }

            return $false
        }

        # Select OU for admin account
        $officeLocation = if ($TicketKey -and $ticketValidation.Fields.OfficeLocation) {
            $ticketValidation.Fields.OfficeLocation
        } else {
            $standardUser.Office
        }

        $targetOU = Select-OU -OfficeLocation $officeLocation

        Write-SecureLog -Message "Target OU selected for admin account" -LogLevel "INFO" -AuditTrail @{
            Operation = "OUSelection"
            TargetOU = Protect-SensitiveData -Text $targetOU
            OfficeLocation = $officeLocation
        }

        # Generate secure password
        Write-Host "Generating secure password..." -ForegroundColor Yellow
        $adminPassword = New-SecurePassword

        # Create admin account UPN
        $adminUserUPN = New-StandardUPN -FirstName $standardUser.GivenName -LastName $standardUser.Surname -Domain $script:Config.ScriptSettings.DefaultDomain
        $adminUserUPN = $adminUserUPN -replace "@", "-a@"

        Write-Host "Creating admin account..." -ForegroundColor Yellow
        Write-SecureLog -Message "Creating admin account in Active Directory" -LogLevel "INFO" -AuditTrail @{
            Operation = "AdminAccountCreation"
            AdminSamAccountName = $adminSamAccountName
            AdminUPN = Get-StringHash -InputString $adminUserUPN
            TargetOU = Get-StringHash -InputString $targetOU
        }

        # Create the admin account
        $adminAccountParams = @{
            Name = "$($standardUser.DisplayName) (Admin)"
            SamAccountName = $adminSamAccountName
                UserPrincipalName  = $adminUserUPN
            GivenName = $standardUser.GivenName
            Surname = $standardUser.Surname
            DisplayName = "$($standardUser.DisplayName) (Admin)"
            Description = "Admin account for $($standardUser.DisplayName)"
            Path = $targetOU
            AccountPassword = (ConvertTo-SecureString $adminPassword -AsPlainText -Force)
            Enabled = $true
            ChangePasswordAtLogon = $script:Config.ActiveDirectory.PasswordPolicy.ChangePasswordAtLogon
            PasswordNeverExpires = $script:Config.ActiveDirectory.PasswordPolicy.PasswordNeverExpires
        }

        New-ADUser @adminAccountParams -ErrorAction Stop

        Write-Host "Admin account created successfully: $adminSamAccountName" -ForegroundColor Green
        Write-SecureLog -Message "Admin account created successfully" -LogLevel "INFO" -AuditTrail @{
            Operation = "AdminAccountCreated"
            AdminSamAccountName = $adminSamAccountName
            Success = $true
        }

        # Send email notifications
        if ($script:Config.Email.EnableNotifications) {
            Write-Host "Sending email notifications..." -ForegroundColor Yellow
            try {
                Send-EmailNotification -LogPath $script:CurrentLogPath -AdminUserUPN $adminUserUPN -Password $adminPassword -StandardUserEmail $standardUser.EmailAddress
                Write-SecureLog -Message "Email notifications sent successfully" -LogLevel "INFO"
            }
            catch {
                Write-Warning "Failed to send email notifications: $($_.Exception.Message)"
                Write-SecureLog -Message "Email notification failed: $($_.Exception.Message)" -LogLevel "WARNING"
            }
        }

        # Update Jira ticket
        if ($TicketKey -and -not $SkipJiraIntegration) {
            Write-Host "Updating Jira ticket..." -ForegroundColor Yellow
            try {
                $jiraComment = @"
[SUCCESS] Admin Account Created Successfully

Account Details:
- Admin Username: $adminSamAccountName
- Admin UPN: $adminUserUPN
- Target OU: $(Split-Path $targetOU -Leaf)
- Created By: $(Get-CurrentUserName)
- Creation Time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')

Next Steps:
- Password has been sent to the user via email
- User should change password on first login
- Account is ready for use

This is an automated update from the JML Admin Account System v$script:JMLVersion
"@

                Add-EnhancedJiraComment -TicketKey $TicketKey -CommentText $jiraComment -UseADF -IncludeTimestamp
                Add-EnhancedJiraAttachment -TicketKey $TicketKey -FilePath $script:CurrentLogPath

                Write-SecureLog -Message "Jira ticket updated successfully" -LogLevel "INFO"
            }
            catch {
                Write-Warning "Failed to update Jira ticket: $($_.Exception.Message)"
                Write-SecureLog -Message "Jira update failed: $($_.Exception.Message)" -LogLevel "WARNING"
            }
        }

        Write-Host ""
        Write-Host "Admin account creation completed successfully!" -ForegroundColor Green
        Write-Host "Admin Username: $adminSamAccountName" -ForegroundColor Cyan
        Write-Host "Admin UPN: $adminUserUPN" -ForegroundColor Cyan
        Write-Host "Log file: $script:CurrentLogPath" -ForegroundColor Gray
        Write-Host ""

        Write-SecureLog -Message "Admin account creation process completed successfully" -LogLevel "INFO" -AuditTrail @{
            Operation = "AdminAccountCreationComplete"
            Success = $true
            Duration = ((Get-Date) - $script:ExecutionContext.StartTime).TotalSeconds
        }

        return $true
    }
    catch {
        $errorMessage = "Admin account creation failed: $($_.Exception.Message)"
        Write-Host $errorMessage -ForegroundColor Red
        Write-SecureLog -Message $errorMessage -LogLevel "ERROR" -AuditTrail @{
            Operation = "AdminAccountCreationError"
            ErrorType = $_.Exception.GetType().Name
            Success = $false
        }

        # Update Jira ticket with error
        if ($TicketKey -and -not $SkipJiraIntegration) {
            try {
                Add-EnhancedJiraComment -TicketKey $TicketKey -CommentText "[ERROR] Admin account creation failed: $($_.Exception.Message)" -UseADF -IncludeTimestamp
            }
            catch {
                Write-Warning "Failed to update Jira ticket with error: $($_.Exception.Message)"
            }
        }

        return $false
    }
}

<#
.SYNOPSIS
Removes an existing admin account.

.DESCRIPTION
Orchestrates the complete admin account deletion process including validation,
Jira ticket processing, account deletion, email notifications, and audit logging.

.PARAMETER TicketKey
Optional Jira ticket key for automated processing.

.EXAMPLE
Remove-StdAdminAccount -TicketKey "HELP-12345"

.NOTES
This function safely removes admin accounts with comprehensive logging.
#>
function Remove-StdAdminAccount {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [ValidatePattern('^[A-Z]+-\d+$')]
        [string]$TicketKey
    )

    try {
        Write-Host ""
        Write-Host "=== ADMIN ACCOUNT DELETION ===" -ForegroundColor Red
        Write-Host "JML System v$script:JMLVersion" -ForegroundColor Green
        Write-Host ""

        # Get UPN for deletion
        $standardUserUPN = if ($TicketKey) {
            Write-Host "Processing Jira ticket: $TicketKey" -ForegroundColor Cyan

            if (-not $SkipJiraIntegration) {
                # Initialize Jira and extract UPN from ticket
                $jiraCredential = Get-SecureCredential -CredentialName "JiraApiToken" -Purpose "Jira API access"
                $jiraUsername = Get-SecureCredential -CredentialName "JiraUsername" -Purpose "Jira username"

                if ($jiraCredential -and $jiraUsername) {
                    $jiraCred = New-Object System.Management.Automation.PSCredential($jiraUsername, $jiraCredential)
                    Initialize-JiraConnection -ServerUrl $script:Config.Jira.ServerUrl -Credential $jiraCred -TestConnection

                    $ticketValidation = Test-JiraTicketValidation -TicketKey $TicketKey -ExpectedWorkType $script:Config.Jira.ExpectedWorkTypes.DeleteAdmin

                    if (-not $ticketValidation.IsValid) {
                        throw "Jira ticket validation failed: $($ticketValidation.ErrorMessage)"
                    }

                    # Extract UPN from ticket or prompt
                    if ($ticketValidation.Fields.FirstName -and $ticketValidation.Fields.LastName) {
                        New-StandardUPN -FirstName $ticketValidation.Fields.FirstName -LastName $ticketValidation.Fields.LastName -Domain $script:Config.ScriptSettings.DefaultDomain
                    } else {
                        Read-Host "Enter the UPN of the user whose admin account should be deleted"
                    }
                } else {
                    throw "Jira credentials not available. Run setup to configure credentials."
                }
            } else {
                Read-Host "Enter the UPN of the user whose admin account should be deleted"
            }
        } else {
            Get-ValidatedUPN -Domain $script:Config.ScriptSettings.DefaultDomain -ForDeletion
        }

        # Initialize secure logging
        $script:CurrentLogPath = Initialize-SecureLogging -StandardUserUPN $standardUserUPN

        Write-SecureLog -Message "Starting admin account deletion process" -LogLevel "INFO" -AuditTrail @{
            Operation = "AdminAccountDeletionStart"
            TargetUser = Get-StringHash -InputString $standardUserUPN
            TicketKey = $TicketKey
            ExecutingUser = $script:ExecutionContext.ExecutingUser
            Version = $script:JMLVersion
        }

        # Get standard user details
        $standardUser = Get-OptimizedADUserDetails -UserUPN $standardUserUPN -UseCache
        if (-not $standardUser) {
            throw "Standard user not found in Active Directory: $standardUserUPN"
        }

        # Find admin account
        $adminSamAccountName = "$($standardUser.SamAccountName)-a"
        $adminAccount = Get-ADUser -Filter "SamAccountName -eq '$adminSamAccountName'" -ErrorAction SilentlyContinue

        if (-not $adminAccount) {
            $message = "No admin account found for user: $standardUserUPN"
            Write-Host $message -ForegroundColor Yellow
            Write-SecureLog -Message $message -LogLevel "WARNING"

            if ($TicketKey -and -not $SkipJiraIntegration) {
                Add-EnhancedJiraComment -TicketKey $TicketKey -CommentText "[WARNING] Admin account deletion: No admin account found for this user" -UseADF -IncludeTimestamp
            }

            return $false
        }

        # Confirm deletion
        Write-Host "Admin account found: $($adminAccount.DisplayName)" -ForegroundColor Yellow
        Write-Host "SAM Account Name: $($adminAccount.SamAccountName)" -ForegroundColor Yellow
        Write-Host "UPN: $($adminAccount.UserPrincipalName)" -ForegroundColor Yellow
        Write-Host ""

        if (-not $TicketKey) {
            $confirmation = Read-Host "Are you sure you want to DELETE this admin account? (Type 'DELETE' to confirm)"
            if ($confirmation -ne 'DELETE') {
                Write-Host "Deletion cancelled by user." -ForegroundColor Yellow
                return $false
            }
        }

        # Delete the admin account
        Write-Host "Deleting admin account..." -ForegroundColor Red
        Write-SecureLog -Message "Deleting admin account from Active Directory" -LogLevel "INFO" -AuditTrail @{
            Operation = "AdminAccountDeletion"
            AdminSamAccountName = $adminAccount.SamAccountName
            AdminUPN = Get-StringHash -InputString $adminAccount.UserPrincipalName
        }

        Remove-ADUser -Identity $adminAccount.SamAccountName -Confirm:$false -ErrorAction Stop

        Write-Host "Admin account deleted successfully: $($adminAccount.SamAccountName)" -ForegroundColor Green
        Write-SecureLog -Message "Admin account deleted successfully" -LogLevel "INFO" -AuditTrail @{
            Operation = "AdminAccountDeleted"
            AdminSamAccountName = $adminAccount.SamAccountName
            Success = $true
        }

        # Send email notification
        if ($script:Config.Email.EnableNotifications) {
            Write-Host "Sending email notification..." -ForegroundColor Yellow
            try {
                Send-DeletionEmailNotification -LogPath $script:CurrentLogPath -AdminUserUPN $adminAccount.UserPrincipalName -StandardUserEmail $standardUser.EmailAddress
                Write-SecureLog -Message "Deletion email notification sent successfully" -LogLevel "INFO"
            }
            catch {
                Write-Warning "Failed to send email notification: $($_.Exception.Message)"
                Write-SecureLog -Message "Email notification failed: $($_.Exception.Message)" -LogLevel "WARNING"
            }
        }

        # Update Jira ticket
        if ($TicketKey -and -not $SkipJiraIntegration) {
            Write-Host "Updating Jira ticket..." -ForegroundColor Yellow
            try {
                $jiraComment = @"
[SUCCESS] Admin Account Deleted Successfully

Deleted Account Details:
- Admin Username: $($adminAccount.SamAccountName)
- Admin UPN: $($adminAccount.UserPrincipalName)
- Deleted By: $(Get-CurrentUserName)
- Deletion Time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')

Action Completed:
- Admin account has been permanently removed from Active Directory
- Email notification sent to support team
- All access associated with this admin account has been revoked

This is an automated update from the JML Admin Account System v$script:JMLVersion
"@

                Add-EnhancedJiraComment -TicketKey $TicketKey -CommentText $jiraComment -UseADF -IncludeTimestamp
                Add-EnhancedJiraAttachment -TicketKey $TicketKey -FilePath $script:CurrentLogPath

                Write-SecureLog -Message "Jira ticket updated successfully" -LogLevel "INFO"
            }
            catch {
                Write-Warning "Failed to update Jira ticket: $($_.Exception.Message)"
                Write-SecureLog -Message "Jira update failed: $($_.Exception.Message)" -LogLevel "WARNING"
            }
        }

        Write-Host ""
        Write-Host "Admin account deletion completed successfully!" -ForegroundColor Green
        Write-Host "Deleted Account: $($adminAccount.SamAccountName)" -ForegroundColor Cyan
        Write-Host "Log file: $script:CurrentLogPath" -ForegroundColor Gray
        Write-Host ""

        Write-SecureLog -Message "Admin account deletion process completed successfully" -LogLevel "INFO" -AuditTrail @{
            Operation = "AdminAccountDeletionComplete"
            Success = $true
            Duration = ((Get-Date) - $script:ExecutionContext.StartTime).TotalSeconds
        }

        return $true
    }
    catch {
        $errorMessage = "Admin account deletion failed: $($_.Exception.Message)"
        Write-Host $errorMessage -ForegroundColor Red
        Write-SecureLog -Message $errorMessage -LogLevel "ERROR" -AuditTrail @{
            Operation = "AdminAccountDeletionError"
            ErrorType = $_.Exception.GetType().Name
            Success = $false
        }

        # Update Jira ticket with error
        if ($TicketKey -and -not $SkipJiraIntegration) {
            try {
                Add-EnhancedJiraComment -TicketKey $TicketKey -CommentText "[ERROR] Admin account deletion failed: $($_.Exception.Message)" -UseADF -IncludeTimestamp
            }
            catch {
                Write-Warning "Failed to update Jira ticket with error: $($_.Exception.Message)"
            }
        }

        return $false
    }
}

<#
.SYNOPSIS
Resets the password for an existing admin account.

.DESCRIPTION
Orchestrates the complete admin account password reset process including validation,
Jira ticket processing, password reset, email notifications, and audit logging.

.PARAMETER TicketKey
Optional Jira ticket key for automated processing.

.EXAMPLE
Reset-StdAdminAccount -TicketKey "HELP-12345"

.NOTES
This function safely resets admin account passwords with comprehensive logging.
#>
function Reset-StdAdminAccount {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [ValidatePattern('^[A-Z]+-\d+$')]
        [string]$TicketKey
    )

    try {
        Write-Host ""
        Write-Host "=== ADMIN ACCOUNT PASSWORD RESET ===" -ForegroundColor Yellow
        Write-Host "JML System v$script:JMLVersion" -ForegroundColor Green
        Write-Host ""

        # Get UPN for reset
        $standardUserUPN = if ($TicketKey) {
            Write-Host "Processing Jira ticket: $TicketKey" -ForegroundColor Cyan

            if (-not $SkipJiraIntegration) {
                # Initialize Jira and extract UPN from ticket
                $jiraCredential = Get-SecureCredential -CredentialName "JiraApiToken" -Purpose "Jira API access"
                $jiraUsername = Get-SecureCredential -CredentialName "JiraUsername" -Purpose "Jira username"

                if ($jiraCredential -and $jiraUsername) {
                    $jiraCred = New-Object System.Management.Automation.PSCredential($jiraUsername, $jiraCredential)
                    Initialize-JiraConnection -ServerUrl $script:Config.Jira.ServerUrl -Credential $jiraCred -TestConnection

                    $ticketValidation = Test-JiraTicketValidation -TicketKey $TicketKey -ExpectedWorkType $script:Config.Jira.ExpectedWorkTypes.ResetAdmin

                    if (-not $ticketValidation.IsValid) {
                        throw "Jira ticket validation failed: $($ticketValidation.ErrorMessage)"
                    }

                    # Extract UPN from ticket or prompt
                    if ($ticketValidation.Fields.FirstName -and $ticketValidation.Fields.LastName) {
                        New-StandardUPN -FirstName $ticketValidation.Fields.FirstName -LastName $ticketValidation.Fields.LastName -Domain $script:Config.ScriptSettings.DefaultDomain
                    } else {
                        Read-Host "Enter the UPN of the user whose admin account password should be reset"
                    }
                } else {
                    throw "Jira credentials not available. Run setup to configure credentials."
                }
            } else {
                Read-Host "Enter the UPN of the user whose admin account password should be reset"
            }
        } else {
            Get-ValidatedUPN -Domain $script:Config.ScriptSettings.DefaultDomain -ForReset
        }

        # Initialize secure logging
        $script:CurrentLogPath = Initialize-SecureLogging -StandardUserUPN $standardUserUPN

        Write-SecureLog -Message "Starting admin account password reset process" -LogLevel "INFO" -AuditTrail @{
            Operation = "AdminAccountResetStart"
            TargetUser = Get-StringHash -InputString $standardUserUPN
            TicketKey = $TicketKey
            ExecutingUser = $script:ExecutionContext.ExecutingUser
            Version = $script:JMLVersion
        }

        # Get standard user details
        $standardUser = Get-OptimizedADUserDetails -UserUPN $standardUserUPN -UseCache
        if (-not $standardUser) {
            throw "Standard user not found in Active Directory: $standardUserUPN"
        }

        # Find admin account
        $adminSamAccountName = "$($standardUser.SamAccountName)-a"
        $adminAccount = Get-ADUser -Filter "SamAccountName -eq '$adminSamAccountName'" -ErrorAction SilentlyContinue

        if (-not $adminAccount) {
            $message = "No admin account found for user: $standardUserUPN"
            Write-Host $message -ForegroundColor Yellow
            Write-SecureLog -Message $message -LogLevel "WARNING"

            if ($TicketKey -and -not $SkipJiraIntegration) {
                Add-EnhancedJiraComment -TicketKey $TicketKey -CommentText "[WARNING] Admin account password reset: No admin account found for this user" -UseADF -IncludeTimestamp
            }

            return $false
        }

        # Display account info
        Write-Host "Admin account found: $($adminAccount.DisplayName)" -ForegroundColor Yellow
        Write-Host "SAM Account Name: $($adminAccount.SamAccountName)" -ForegroundColor Yellow
        Write-Host "UPN: $($adminAccount.UserPrincipalName)" -ForegroundColor Yellow
        Write-Host ""

        # Generate new password
        Write-Host "Generating new secure password..." -ForegroundColor Yellow
        $newPassword = New-SecurePassword

        # Reset password
        Write-Host "Resetting admin account password..." -ForegroundColor Yellow
        Write-SecureLog -Message "Resetting admin account password" -LogLevel "INFO" -AuditTrail @{
            Operation = "AdminAccountPasswordReset"
            AdminSamAccountName = $adminAccount.SamAccountName
            AdminUPN = Get-StringHash -InputString $adminAccount.UserPrincipalName
        }

        Set-ADAccountPassword -Identity $adminAccount.SamAccountName -NewPassword (ConvertTo-SecureString $newPassword -AsPlainText -Force) -Reset -ErrorAction Stop
        Set-ADUser -Identity $adminAccount.SamAccountName -ChangePasswordAtLogon $script:Config.ActiveDirectory.PasswordPolicy.ChangePasswordAtLogon -ErrorAction Stop

        Write-Host "Admin account password reset successfully: $($adminAccount.SamAccountName)" -ForegroundColor Green
        Write-SecureLog -Message "Admin account password reset successfully" -LogLevel "INFO" -AuditTrail @{
            Operation = "AdminAccountPasswordResetComplete"
            AdminSamAccountName = $adminAccount.SamAccountName
            Success = $true
        }

        # Send email notification
        if ($script:Config.Email.EnableNotifications) {
            Write-Host "Sending email notification..." -ForegroundColor Yellow
            try {
                Send-ResetEmailNotification -LogPath $script:CurrentLogPath -AdminUserUPN $adminAccount.UserPrincipalName -NewPassword $newPassword -StandardUserEmail $standardUser.EmailAddress
                Write-SecureLog -Message "Reset email notification sent successfully" -LogLevel "INFO"
            }
            catch {
                Write-Warning "Failed to send email notification: $($_.Exception.Message)"
                Write-SecureLog -Message "Email notification failed: $($_.Exception.Message)" -LogLevel "WARNING"
            }
        }

        # Update Jira ticket
        if ($TicketKey -and -not $SkipJiraIntegration) {
            Write-Host "Updating Jira ticket..." -ForegroundColor Yellow
            try {
                $jiraComment = @"
[SUCCESS] Admin Account Password Reset Successfully

Reset Account Details:
- Admin Username: $($adminAccount.SamAccountName)
- Admin UPN: $($adminAccount.UserPrincipalName)
- Reset By: $(try { Get-CurrentUserName } catch { $script:ExecutionContext.ExecutingUser })
- Reset Time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')

Action Completed:
- New secure password has been generated and set
- Password change required on next login
- New credentials sent to user via email
- Account is ready for use

This is an automated update from the JML Admin Account System v$script:JMLVersion
"@

                Add-EnhancedJiraComment -TicketKey $TicketKey -CommentText $jiraComment -UseADF -IncludeTimestamp
                Add-EnhancedJiraAttachment -TicketKey $TicketKey -FilePath $script:CurrentLogPath

                Write-SecureLog -Message "Jira ticket updated successfully" -LogLevel "INFO"
            }
            catch {
                Write-Warning "Failed to update Jira ticket: $($_.Exception.Message)"
                Write-SecureLog -Message "Jira update failed: $($_.Exception.Message)" -LogLevel "WARNING"
            }
        }

        Write-Host ""
        Write-Host "Admin account password reset completed successfully!" -ForegroundColor Green
        Write-Host "Account: $($adminAccount.SamAccountName)" -ForegroundColor Cyan
        Write-Host "New password has been sent to the user via email." -ForegroundColor Cyan
        Write-Host "Log file: $script:CurrentLogPath" -ForegroundColor Gray
        Write-Host ""

        Write-SecureLog -Message "Admin account password reset process completed successfully" -LogLevel "INFO" -AuditTrail @{
            Operation = "AdminAccountResetComplete"
            Success = $true
            Duration = ((Get-Date) - $script:ExecutionContext.StartTime).TotalSeconds
        }

        return $true
    }
    catch {
        $errorMessage = "Admin account password reset failed: $($_.Exception.Message)"
        Write-Host $errorMessage -ForegroundColor Red
        Write-SecureLog -Message $errorMessage -LogLevel "ERROR" -AuditTrail @{
            Operation = "AdminAccountResetError"
            ErrorType = $_.Exception.GetType().Name
            Success = $false
        }

        # Update Jira ticket with error
        if ($TicketKey -and -not $SkipJiraIntegration) {
            try {
                Add-EnhancedJiraComment -TicketKey $TicketKey -CommentText "[ERROR] Admin account password reset failed: $($_.Exception.Message)" -UseADF -IncludeTimestamp
            }
            catch {
                Write-Warning "Failed to update Jira ticket with error: $($_.Exception.Message)"
            }
        }

        return $false
    }
}

#endregion

#region Main Menu and Execution Logic

<#
.SYNOPSIS
Displays the main menu for the JML system.

.DESCRIPTION
Shows an interactive menu with all available options and system information.

.NOTES
Provides a user-friendly interface for all JML operations.
#>
function Show-MainMenu {
    [CmdletBinding()]
    param()

    Clear-Host

    Write-Host ""
    Write-Host ("=" * 80) -ForegroundColor Cyan
    Write-Host "                    JML Admin Account Management System                      " -ForegroundColor Cyan
    Write-Host "                              Version $script:JMLVersion                                    " -ForegroundColor Cyan
    Write-Host ("=" * 80) -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Current User: " -NoNewline -ForegroundColor Gray
    Write-Host $script:ExecutionContext.ExecutingUser -ForegroundColor Yellow
    Write-Host "Computer: " -NoNewline -ForegroundColor Gray
    Write-Host $script:ExecutionContext.ComputerName -ForegroundColor Yellow
    Write-Host "Configuration: " -NoNewline -ForegroundColor Gray
    Write-Host "Loaded" -ForegroundColor Green

    if ($SkipJiraIntegration) {
        Write-Host "Jira Integration: " -NoNewline -ForegroundColor Gray
        Write-Host "DISABLED" -ForegroundColor Red
    } else {
        Write-Host "Jira Integration: " -NoNewline -ForegroundColor Gray
        Write-Host "Enabled" -ForegroundColor Green
    }

    Write-Host ""
    Write-Host "Available Operations:" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "  1. Create Admin Account" -ForegroundColor Green
    Write-Host "     Create a new admin account for a standard user"
    Write-Host ""
    Write-Host "  2. Delete Admin Account" -ForegroundColor Red
    Write-Host "     Remove an existing admin account"
    Write-Host ""
    Write-Host "  3. Reset Admin Account Password" -ForegroundColor Yellow
    Write-Host "     Reset the password for an existing admin account"
    Write-Host ""
    Write-Host "  4. System Information" -ForegroundColor Cyan
    Write-Host "     Display version and module information"
    Write-Host ""
    Write-Host "  5. Run Setup" -ForegroundColor Magenta
    Write-Host "     Configure credentials and validate environment"
    Write-Host ""
    Write-Host "  6. Exit" -ForegroundColor Gray
    Write-Host "     Exit the application"
    Write-Host ""
    Write-Host "Enter your choice (1-6): " -NoNewline -ForegroundColor White
}

<#
.SYNOPSIS
Displays system information and module status.

.DESCRIPTION
Shows comprehensive system information including version details,
module status, and configuration information.

.NOTES
Useful for troubleshooting and system verification.
#>
function Show-SystemInformation {
    [CmdletBinding()]
    param()

    Clear-Host

    Write-Host ""
    Write-Host ("=" * 80) -ForegroundColor Cyan
    Write-Host "                           System Information                                " -ForegroundColor Cyan
    Write-Host ("=" * 80) -ForegroundColor Cyan
    Write-Host ""

    $versionInfo = Get-JMLVersion

    Write-Host "JML System Information:" -ForegroundColor Green
    Write-Host "  Version: $($versionInfo.JMLVersion)" -ForegroundColor White
    Write-Host "  Build Date: $script:JMLBuildDate" -ForegroundColor White
    Write-Host "  Script Path: $($versionInfo.ScriptPath)" -ForegroundColor Gray
    Write-Host ""

    Write-Host "Environment Information:" -ForegroundColor Green
    Write-Host "  PowerShell Version: $($versionInfo.PowerShellVersion)" -ForegroundColor White
    Write-Host "  Operating System: $($versionInfo.OperatingSystem)" -ForegroundColor White
    Write-Host "  Current User: $($versionInfo.CurrentUser)" -ForegroundColor White
    Write-Host "  Computer Name: $($versionInfo.ComputerName)" -ForegroundColor White
    Write-Host ""

    Write-Host "Module Status:" -ForegroundColor Green
    foreach ($module in $versionInfo.Modules.GetEnumerator() | Sort-Object Key) {
        $status = if ($module.Value.Loaded) { "[OK] Loaded" } else { "[MISSING] Not Loaded" }
        $color = if ($module.Value.Loaded) { "Green" } else { "Red" }
        Write-Host "  $($module.Key): " -NoNewline -ForegroundColor White
        Write-Host "$($module.Value.Version) " -NoNewline -ForegroundColor Gray
        Write-Host $status -ForegroundColor $color
    }

    Write-Host ""
    Write-Host "Configuration Status:" -ForegroundColor Green
    if ($script:Config) {
        Write-Host "  Configuration File: " -NoNewline -ForegroundColor White
        Write-Host "[OK] Loaded" -ForegroundColor Green
        Write-Host "  Default Domain: " -NoNewline -ForegroundColor White
        Write-Host $script:Config.ScriptSettings.DefaultDomain -ForegroundColor Yellow
        Write-Host "  Log Directory: " -NoNewline -ForegroundColor White
        Write-Host $script:Config.Logging.LogDirectory -ForegroundColor Yellow
        Write-Host "  Data Redaction: " -NoNewline -ForegroundColor White
        $redactionStatus = if ($script:Config.Logging.EnableDataRedaction) { "[OK] Enabled" } else { "Disabled" }
        $redactionColor = if ($script:Config.Logging.EnableDataRedaction) { "Green" } else { "Yellow" }
        Write-Host $redactionStatus -ForegroundColor $redactionColor
    } else {
        Write-Host "  Configuration: " -NoNewline -ForegroundColor White
        Write-Host "[ERROR] Not Loaded" -ForegroundColor Red
    }

    Write-Host ""
    Write-Host "Press any key to return to main menu..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

<#
.SYNOPSIS
Main execution logic for the JML system.

.DESCRIPTION
Handles the main menu loop and user interaction for the JML system.

.NOTES
This is the main entry point for interactive mode.
#>
function Start-MainExecution {
    [CmdletBinding()]
    param()

    try {
        Write-Progress -Activity "Initializing JML System" -Status "Starting main execution..." -PercentComplete 90

        # Log system startup
        Write-SecureLog -Message "JML System started in interactive mode" -LogLevel "INFO" -AuditTrail @{
            Operation = "SystemStartup"
            Version = $script:JMLVersion
            ExecutingUser = $script:ExecutionContext.ExecutingUser
            ComputerName = $script:ExecutionContext.ComputerName
            SkipJiraIntegration = $SkipJiraIntegration.IsPresent
        }

        Write-Progress -Activity "Initializing JML System" -Status "Ready" -PercentComplete 100
        Start-Sleep -Milliseconds 500
        Write-Progress -Activity "Initializing JML System" -Completed

        # Main menu loop
        do {
            Show-MainMenu
            $choice = Read-Host

            switch ($choice) {
                "1" {
                    $ticketKey = Read-Host "Enter Jira ticket key (optional, press Enter to skip)"
                    if ([string]::IsNullOrWhiteSpace($ticketKey)) {
                        New-AdminAccount
                    } else {
                        New-AdminAccount -TicketKey $ticketKey.Trim().ToUpper()
                    }
                    Write-Host ""
                    Write-Host "Press any key to continue..." -ForegroundColor Gray
                    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
                }
                "2" {
                    $ticketKey = Read-Host "Enter Jira ticket key (optional, press Enter to skip)"
                    if ([string]::IsNullOrWhiteSpace($ticketKey)) {
                        Remove-StdAdminAccount
                    } else {
                        Remove-StdAdminAccount -TicketKey $ticketKey.Trim().ToUpper()
                    }
                    Write-Host ""
                    Write-Host "Press any key to continue..." -ForegroundColor Gray
                    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
                }
                "3" {
                    $ticketKey = Read-Host "Enter Jira ticket key (optional, press Enter to skip)"
                    if ([string]::IsNullOrWhiteSpace($ticketKey)) {
                        Reset-StdAdminAccount
                    } else {
                        Reset-StdAdminAccount -TicketKey $ticketKey.Trim().ToUpper()
                    }
                    Write-Host ""
                    Write-Host "Press any key to continue..." -ForegroundColor Gray
                    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
                }
                "4" {
                    Show-SystemInformation
                }
                "5" {
                    Clear-Host
                    Write-Host ""
                    Write-Host "Running JML Setup..." -ForegroundColor Cyan
                    Write-Host ""
                    $setupResult = Start-JMLSetup -ValidateEnvironment -SetupCredentials -InstallMissingModules
                    Write-Host ""
                    if ($setupResult) {
                        Write-Host "Setup completed successfully!" -ForegroundColor Green
                    } else {
                        Write-Host "Setup completed with issues. Please review the messages above." -ForegroundColor Yellow
                    }
                    Write-Host ""
                    Write-Host "Press any key to continue..." -ForegroundColor Gray
                    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
                }
                "6" {
                    Write-Host ""
                    Write-Host "Thank you for using the JML Admin Account Management System!" -ForegroundColor Green
                    Write-Host "Version $script:JMLVersion - Enhanced Security Edition" -ForegroundColor Cyan
                    Write-Host ""

                    Write-SecureLog -Message "JML System shutdown by user" -LogLevel "INFO" -AuditTrail @{
                        Operation = "SystemShutdown"
                        Duration = ((Get-Date) - $script:ExecutionContext.StartTime).TotalSeconds
                    }

                    return
                }
                default {
                    Write-Host ""
                    Write-Host "Invalid choice. Please select a number between 1 and 6." -ForegroundColor Red
                    Write-Host ""
                    Write-Host "Press any key to continue..." -ForegroundColor Gray
                    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
                }
            }
        } while ($true)
    }
    catch {
        Write-Error "Main execution failed: $($_.Exception.Message)"
        Write-SecureLog -Message "Main execution error: $($_.Exception.Message)" -LogLevel "ERROR"
        exit 1
    }
}

#endregion

#region Script Execution

# Start main execution
try {
    Start-MainExecution
}
catch {
    Write-Error "Script execution failed: $($_.Exception.Message)"
    exit 1
}
finally {
    # Cleanup
    if ($script:CurrentLogPath) {
        Write-Host "Log file saved: $script:CurrentLogPath" -ForegroundColor Gray
    }
}

#endregion
