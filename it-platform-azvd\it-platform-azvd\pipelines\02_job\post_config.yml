parameters:
  svcConnection : ""
  location      : ""
  environment   : ""
  primaryRegion : ""

jobs:
- deployment : Post_Config_${{ parameters.location }}
  displayName: ${{ parameters.environment }}_PostConfig_${{ parameters.location }}
  environment: ${{ parameters.environment }}_Release
  pool:
    vmimage: 'windows-latest'

  strategy:
    runOnce:
      deploy:
        steps:
        - checkout: self

        - template: /pipelines/03_step/config_workspace.yml # Configures the shared workspace
          parameters:
            svcConnection: $(${{ parameters.environment }}_svcConnection)
            subscriptions: $(${{ parameters.environment }}_subscriptions)
            location     : ${{ parameters.location }}
            environment  : ${{ parameters.environment }}

        - template: /pipelines/03_step/config_rbac.yml # Configures RBAC on the VMs
          parameters:
            svcConnection: $(${{ parameters.environment }}_svcConnection)
            subscriptions: $(${{ parameters.environment }}_subscriptions)
            location     : ${{ parameters.location }}
            environment  : ${{ parameters.environment }}
            primaryRegion: ${{ parameters.primaryRegion }}

        - ${{ if eq(parameters.location, parameters.primaryRegion) }}:
          - template: /pipelines/03_step/config_host.yml # Configures host settings for FSLogix and the Cato Root CA
            parameters:
              svcConnection: $(${{ parameters.environment }}_svcConnection)
              subscriptions: $(${{ parameters.environment }}_subscriptions)
              location     : ${{ parameters.location }}
              environment  : ${{ parameters.environment }}

        - template: /pipelines/03_step/config_primaryDevice.yml # Configures Primary Device
          parameters:
            svcConnection: $(${{ parameters.environment }}_svcConnection)
            subscriptions: $(${{ parameters.environment }}_subscriptions)
            location     : ${{ parameters.location }}
            primaryRegion: ${{ parameters.primaryRegion }}