//SCOPE

targetScope = 'subscription'

// PARAMETERS

param p_utcNow	string = utcNow()
@description('Workspace parameters stored in ./param/$subscriptionName/$teamName.json')
param p_workspace       	object
@description('Tags stored in ./param/$subscriptionName/$teamName.json')
param p_tags            	object

@description('Location of Host Pool, Application Group and Workspace')
@allowed([
	'uksouth'
	'ukwest'
	'eastasia'
	'southeastasia'
])
param p_location    	string

@description('Azure Environment')
@allowed([
	'prod'
	
])
param p_environment string

param p_serviceCode    string
param p_storageAccount object


// VARIABLES

@description('Abbreviation of location name for other resource naming')
var v_locationSwitch = {
	uksouth       : 'uks'
	ukwest        : 'ukw'
	eastasia      : 'eaa'
	southeastasia : 'sea'
}

@description('Selecting another region if an Asian region was selected, as Azure Virtual Desktop is not available in Asia yet')
var v_avdLocationSwitch = {
	uksouth       : 'uksouth'
	ukwest        : 'ukwest'
	southeastasia : 'uksouth'
	eastasia      : 'ukwest'
}

@description('Abbreviation of subscription name for resource naming')
var v_environmentSwitch = {
	prod : 'prd'
	nprd : 'nprd'
}

// RESOURCES

resource rg_azvd 'Microsoft.Resources/resourceGroups@2022-09-01' = {
    name    : '${v_locationSwitch[p_location]}-${v_environmentSwitch[p_environment]}-${p_serviceCode}-azvd-rg' // Example: uks-prd-ssv-azvd-rg
    location: p_location
    tags    : p_tags
}

// MODULES

module m_workspace 'workspace.bicep' = {
    name : 'm_workspace_${p_utcNow}'
    scope: rg_azvd
    params: {
		p_tags            : p_tags
		p_location        : v_avdLocationSwitch[p_location]
		p_locationShort   : v_locationSwitch[p_location]
		p_environmentShort: v_environmentSwitch[p_environment]
		p_serviceCode     : p_serviceCode
		p_workspace       : p_workspace
	}
}

module m_storage 'storage.bicep' = {
	name : 'm_storage_${p_utcNow}'
	scope: rg_azvd
	params: {
	p_tags             : p_tags
	p_location         : p_location
	p_locationShort    : v_locationSwitch[p_location]
	p_environmentShort : v_environmentSwitch[p_environment]
	p_storageAccount   : p_storageAccount
}
}

//OUTPUTS
output o_resourceGroupName string = rg_azvd.name
