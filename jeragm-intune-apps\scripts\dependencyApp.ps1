[CmdletBinding()]
param (
    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$appName,

    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$appVersion
)

#Set-PSDebug -Trace 2 #Enable to see debug log
$ErrorActionPreference = "Stop"

# Import parameters from config.yml
Write-Host "Get application config"
$basePath  = $pwd.Path + "\applications"
$appFolder = "$basePath\$appName"

$appConfig = Get-Content "$appFolder\$appVersion\config.yml" | ConvertFrom-Yaml -Ordered

$dependencies = $appConfig.dependencies

if ($dependencies -ne "none") {
    Write-Host "Connect to Microsoft Graph API"
    Connect-MSIntuneGraph -TenantID $env:tenantId -ClientID $env:servicePrincipalId -ClientSecret $env:servicePrincipalKey

    foreach ($dependency in $dependencies.GetEnumerator()) {            
        $dependencyApp      = "$($dependency.Name)"
        $dependencyVersion  = "$($dependency.Value.version)"
        $dependencyType     = "$($dependency.Value.type)"

        Write-Host "Adding $dependencyApp $dependencyVersion as dependency"

        $appId         = (Get-IntuneWin32App -DisplayName $appConfig.name | Where-Object {$_.displayVersion -eq $appVersion}).id
        $dependencyId  = (Get-IntuneWin32App -DisplayName $dependencyApp  | Where-Object {$_.displayVersion -eq $dependencyVersion}).id
        $AppDependency = New-IntuneWin32AppDependency -ID $dependencyId -DependencyType $dependencyType

        Add-IntuneWin32AppDependency -ID $appId -Dependency $AppDependency
    }
}