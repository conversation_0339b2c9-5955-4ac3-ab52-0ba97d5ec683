name         : "Docker"
publisher    : "Docker, Inc."
appVersion   : "4.12.0.85629"
description  : "Docker is a set of platform as a service products that use OS-level virtualization to deliver software in packages called containers."
installExp   : "system"
featured     : false
supersede    : "none"
assignments  : 
  Intune-Users-Developers:
  - intent   : "available"
dependencies : "none"
commandLine  :
- install    : '"Docker Desktop Installer.exe" install --quiet'
- uninstall  : '"%ProgramFiles%\Docker\Docker\Docker Desktop Installer.exe" uninstall --quiet'