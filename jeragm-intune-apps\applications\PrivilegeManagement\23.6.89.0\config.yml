name         : "PrivilegeManagement"
publisher    : "BeyondTrust"
appVersion   : "*********"
description  : "BeyondTrust Privilege Management for Windows Servers reduces the risk of privilege misuse by assigning admin privileges to only authorized tasks that require them, controlling application and script usage, and logging and monitoring on privileged activities."
installExp   : "system"
featured     : false
supersede    : "none"
assignments  : 
  JERAGM Internal Users:
  - intent   : "available"
  Privileged Accounts:
  - intent   : "available"
dependencies : "none"
commandLine  :
- install    : 'msiexec.exe /i "PrivilegeManagementForWindows_x64.msi" IC3MODE=1 /qn /norestart'
- uninstall  : 'msiexec.exe "PrivilegeManagementForWindows_x64.msi" /uninstall' #not tested yet