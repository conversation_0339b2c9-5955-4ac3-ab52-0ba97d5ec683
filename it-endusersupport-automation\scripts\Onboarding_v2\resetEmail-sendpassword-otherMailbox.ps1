#requires -Module ActiveDirectory
#requires -Version 5.1

<#
.SYNOPSIS
    Resets an AD user's password, attaches files, and emails their TLOGIN credentials.

.DESCRIPTION
    Advanced password reset and notification script with the following features:
    - Secure password generation with complexity validation
    - SMTP authentication support
    - Multiple recipient support (CC/BCC)
    - Custom email template support
    - Comprehensive logging with transcripts
    - Progress tracking for longer operations
    - Flexible authentication options

.PARAMETER UserPrincipalName
    The UPN of the target user (e.g., "<EMAIL>")

.PARAMETER AttachmentPath
    Network path containing files to attach

.PARAMETER SmtpCredential
    Optional PSCredential for SMTP authentication

.PARAMETER EmailTemplate
    Optional path to custom email template file

.PARAMETER CcRecipients
    Optional array of CC recipients

.PARAMETER BccRecipients
    Optional array of BCC recipients

.EXAMPLE
    $smtpCred = Get-Credential
    .\resetEmail-sendpassword-otherMailbox.ps1 -UserPrincipalName "<EMAIL>" `
        -AttachmentPath "\\server\share\docs" -SmtpCredential $smtpCred

.NOTES
    Version: 2.0
    Updated: 2024-03-19
#>

[CmdletBinding(DefaultParameterSetName = 'NoAuth')]
param(
    [Parameter(Mandatory = $true, Position = 0)]
    [ValidatePattern('^[\w\-\.]+@([\w\-]+\.)+[\w\-]{2,}$')]
    [string]$UserPrincipalName,

    [Parameter(Mandatory = $true, Position = 1)]
    [ValidateScript({
        if (-not (Test-Path $_ -PathType Container)) {
            throw "Path '$_' does not exist or is not a directory."
        }
        return $true
    })]
    [string]$AttachmentPath,

    [Parameter(ParameterSetName = 'SmtpAuth')]
    [System.Management.Automation.PSCredential]
    [System.Management.Automation.Credential()]
    $SmtpCredential,

    [Parameter(Mandatory = $false)]
    [ValidateScript({
        if ($_) {
            if (-not (Test-Path $_ -PathType Leaf)) {
                throw "Template file '$_' does not exist."
            }
            if (-not $_.EndsWith('.html')) {
                throw "Template file must be an HTML file."
            }
        }
        return $true
    })]
    [string]$EmailTemplate,

    [Parameter(Mandatory = $false)]
    [string[]]$CcRecipients,

    [Parameter(Mandatory = $false)]
    [string[]]$BccRecipients
)

###############################################################################
#                          GLOBAL CONFIGURATIONS                              #
###############################################################################
# Script variables
$script:LogPath = "C:\Temp\Scripts\Desktop Support\Logs\PasswordReset-$(Get-Date -Format 'yyyyMMdd-HHmmss').log"
$script:TranscriptPath = "C:\Temp\Scripts\Desktop Support\Logs\PasswordReset-Transcript-$(Get-Date -Format 'yyyyMMdd-HHmmss').txt"

# Email settings
$script:SmtpServer = "smtp.jeragm.com"
$script:SmtpPort   = 25
$script:UseSsl     = $false
$script:FromAddress = "<EMAIL>"

# Updated default email template with escaped curly braces for CSS
$script:DefaultEmailTemplate = @"
<!DOCTYPE html>
<html>
<head>
    <style>
        body {{ font-family: Arial, sans-serif; }}
        .header {{ color: #003366; }}
        .credentials {{ background-color: #f5f5f5; padding: 10px; margin: 10px 0; }}
        .warning {{ color: #cc0000; }}
    </style>
</head>
<body>
    <h2 class="header">Account Password Reset Notification</h2>
    <p>Hello,</p>
    <p>Your account password has been reset. Please find your TLOGIN credentials below:</p>
    <div class="credentials">
        <p><strong>UPN:</strong> {0}<br>
        <strong>Password:</strong> {1}</p>
    </div>
    <p class="warning">Important: You will be required to change your password at next logon.</p>
    <p>If you did not request this password reset, please contact IT Support immediately.</p>
    <br>
    <p>Best regards,<br>IT Support Team</p>
</body>
</html>
"@

###############################################################################
#                          LOGGING FUNCTIONS                                  #
###############################################################################
Function Initialize-Logging {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$LogFile,
        [Parameter(Mandatory = $true)]
        [string]$TranscriptFile
    )
    try {
        # Create log directory if it doesn't exist
        $logDir = Split-Path $LogFile -Parent
        if (-not (Test-Path $logDir)) {
            New-Item -ItemType Directory -Path $logDir -Force | Out-Null
            Write-Verbose "Created log directory: $logDir"
        }

        # Initialize log file
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        $header = @"
========================================
Password Reset Script Log
Started: $timestamp
========================================

"@
        Set-Content -Path $LogFile -Value $header -Encoding UTF8

        # Start transcript
        Start-Transcript -Path $TranscriptFile -Force
        Write-Verbose "Initialized logging to $LogFile"
        Write-Verbose "Started transcript at $TranscriptFile"
    }
    catch {
        Write-Error "Failed to initialize logging: $($_.Exception.Message)"
        throw
    }
}

Function Write-Log {
    [CmdletBinding()]
    param(
        [string]$Message,
        [ValidateSet("INFO", "WARN", "ERROR", "DEBUG", "SUCCESS")]
        [string]$Level = "INFO",
        [switch]$NoConsole
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    try {
        Add-Content -Path $script:LogPath -Value $logEntry -Encoding UTF8
        
        if (-not $NoConsole) {
            switch ($Level) {
                "INFO"    { Write-Host $logEntry -ForegroundColor White }
                "WARN"    { Write-Host $logEntry -ForegroundColor Yellow }
                "ERROR"   { Write-Host $logEntry -ForegroundColor Red }
                "DEBUG"   { Write-Host $logEntry -ForegroundColor Cyan }
                "SUCCESS" { Write-Host $logEntry -ForegroundColor Green }
            }
        }
    }
    catch {
        Write-Error "Failed to write log entry: $($_.Exception.Message)"
    }
}

###############################################################################
#                     PASSWORD GENERATION AND VALIDATION                      #
###############################################################################
Function New-SecurePassword {
    [CmdletBinding()]
    param(
        [int]$Length = 16,
        [int]$MinSpecial = 2,
        [int]$MinNumbers = 2,
        [int]$MinUpperCase = 2
    )
    
    try {
        Write-Verbose "Generating a secure password of length $Length using pure PowerShell."
    
        # Define the character sets as strings.
        $upperChars   = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        $lowerChars   = "abcdefghijklmnopqrstuvwxyz"
        $numberChars  = "0123456789"
        $specialChars = "!@#$%^&*()-_=+[]{}|;:,.<>?"
    
        # Initialize an array to hold password characters.
        $passwordArray = @()
    
        # Add the required special characters.
        for ($i = 0; $i -lt $MinSpecial; $i++) {
            $index = Get-Random -Maximum $specialChars.Length
            $passwordArray += $specialChars[$index]
        }
    
        # Add the required numeric characters.
        for ($i = 0; $i -lt $MinNumbers; $i++) {
            $index = Get-Random -Maximum $numberChars.Length
            $passwordArray += $numberChars[$index]
        }
    
        # Add the required uppercase characters.
        for ($i = 0; $i -lt $MinUpperCase; $i++) {
            $index = Get-Random -Maximum $upperChars.Length
            $passwordArray += $upperChars[$index]
        }
    
        # Fill the remaining length with lowercase letters.
        while ($passwordArray.Count -lt $Length) {
            $index = Get-Random -Maximum $lowerChars.Length
            $passwordArray += $lowerChars[$index]
        }
    
        # Shuffle the array elements randomly and join them into a string.
        $shuffled = $passwordArray | Get-Random -Count $passwordArray.Count
        $password = $shuffled -join ''
    
        Write-Verbose "Password generated successfully."
        return $password
    }
    catch {
        Write-Error "Error generating secure password: $($_.Exception.Message)"
        throw
    }
}

###############################################################################
#                          EMAIL FUNCTIONS                                    #
###############################################################################
Function Send-CredentialEmail {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$ToAddress,
        [Parameter(Mandatory = $true)]
        [string]$Upn,
        [Parameter(Mandatory = $true)]
        [string]$Password,
        [Parameter(Mandatory = $false)]
        [string[]]$Attachments,
        [Parameter(Mandatory = $false)]
        [string[]]$Cc,
        [Parameter(Mandatory = $false)]
        [string[]]$Bcc,
        [Parameter(Mandatory = $false)]
        [System.Management.Automation.PSCredential]
        $Credential
    )
    try {
        Write-Verbose "Preparing email to $ToAddress..."
        
        # Load custom template if specified; otherwise, use our default template
        $emailBody = if ($EmailTemplate -and (Test-Path $EmailTemplate)) {
            $template = Get-Content $EmailTemplate -Raw
            $template -f $Upn, $Password
        }
        else {
            $script:DefaultEmailTemplate -f $Upn, $Password
        }
        
        $emailParams = @{
            From                       = $script:FromAddress
            To                         = $ToAddress
            Subject                    = "Your Account Password Reset Notification"
            Body                       = $emailBody
            BodyAsHtml                 = $true
            SmtpServer                 = $script:SmtpServer
            Port                       = $script:SmtpPort
            UseSsl                     = $script:UseSsl
            DeliveryNotificationOption = 'OnFailure'
        }
        
        if ($Credential) {
            $emailParams['Credential'] = $Credential
        }
        if ($Cc) {
            $emailParams['Cc'] = $Cc
        }
        if ($Bcc) {
            $emailParams['Bcc'] = $Bcc
        }
        if ($Attachments) {
            $emailParams['Attachments'] = $Attachments
        }
        
        Send-MailMessage @emailParams
        Write-Log -Message "Email sent successfully to $ToAddress" -Level SUCCESS
    }
    catch {
        Write-Log -Message "Failed to send email: $($_.Exception.Message)" -Level ERROR
        throw
    }
}

###############################################################################
#                           MAIN PROCESS FUNCTION                             #
###############################################################################
# ... existing code ...

Function Reset-UserCredentials {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$UserPrincipalName,
        [Parameter(Mandatory = $true)]
        [string]$AttachmentPath,
        [Parameter(Mandatory = $false)]
        [System.Management.Automation.PSCredential]
        $SmtpCredential,
        [Parameter(Mandatory = $false)]
        [string[]]$CcRecipients,
        [Parameter(Mandatory = $false)]
        [string[]]$BccRecipients
    )
    
    try {
        # Step 1: Verify AD user and get otherMailbox attribute
        Write-Progress -Activity "Resetting User Credentials" -Status "Looking up AD user..." -PercentComplete 0
        $adUser = Get-ADUser -Filter "UserPrincipalName -eq '$UserPrincipalName'" `
                            -Properties mail, displayName, otherMailbox -ErrorAction Stop
        
        if (-not $adUser) {
            throw "User not found: $UserPrincipalName"
        }
        Write-Log -Message "Found user: $($adUser.DisplayName)" -Level INFO
        
        # Verify otherMailbox exists
        if (-not $adUser.otherMailbox) {
            throw "No otherMailbox attribute found for user: $UserPrincipalName"
        }
        Write-Log -Message "Found otherMailbox: $($adUser.otherMailbox)" -Level INFO
        
        # Step 2: Generate and set new password
        Write-Progress -Activity "Resetting User Credentials" -Status "Resetting password..." -PercentComplete 25
        $newPassword = New-SecurePassword
        $securePassword = ConvertTo-SecureString -String $newPassword -AsPlainText -Force
        
        Set-ADAccountPassword -Identity $adUser -NewPassword $securePassword -Reset -ErrorAction Stop
        Set-ADUser -Identity $adUser -ChangePasswordAtLogon $true -ErrorAction Stop
        Write-Log -Message "Password reset successful" -Level SUCCESS
        
        # Step 3: Collect attachments
        Write-Progress -Activity "Resetting User Credentials" -Status "Processing attachments..." -PercentComplete 50
        $attachments = @()
        if (Test-Path $AttachmentPath) {
            $attachments = Get-ChildItem -Path $AttachmentPath -File | Select-Object -ExpandProperty FullName
            Write-Log -Message "Found $($attachments.Count) attachment(s)" -Level INFO
        }
        
        # Step 4: Use otherMailbox as recipient email
        Write-Progress -Activity "Resetting User Credentials" -Status "Preparing email..." -PercentComplete 75
        $emailTo = $adUser.otherMailbox
        
        # Step 5: Send email
        Write-Progress -Activity "Resetting User Credentials" -Status "Sending email..." -PercentComplete 90
        $emailParams = @{
            ToAddress = $emailTo
            Upn = $UserPrincipalName
            Password = $newPassword
            Attachments = $attachments
        }
        
        if ($SmtpCredential) {
            $emailParams['Credential'] = $SmtpCredential
        }
        if ($CcRecipients) {
            $emailParams['Cc'] = $CcRecipients
        }
        if ($BccRecipients) {
            $emailParams['Bcc'] = $BccRecipients
        }
        
        Send-CredentialEmail @emailParams
        
        Write-Progress -Activity "Resetting User Credentials" -Status "Complete" -PercentComplete 100
        Write-Log -Message "Password reset and notification completed successfully" -Level SUCCESS
    }
    catch {
        Write-Log -Message "Error: $($_.Exception.Message)" -Level ERROR
        throw
    }
}

# ... rest of the code ...

###############################################################################
#                            SCRIPT EXECUTION                                 #
###############################################################################
try {
    Initialize-Logging -LogFile $script:LogPath -TranscriptFile $script:TranscriptPath
    Write-Log -Message "Starting password reset process for $UserPrincipalName" -Level INFO
    
    $params = @{
        UserPrincipalName = $UserPrincipalName
        AttachmentPath = $AttachmentPath
    }
    
    if ($SmtpCredential) {
        $params['SmtpCredential'] = $SmtpCredential
    }
    if ($CcRecipients) {
        $params['CcRecipients'] = $CcRecipients
    }
    if ($BccRecipients) {
        $params['BccRecipients'] = $BccRecipients
    }
    
    Reset-UserCredentials @params
}
catch {
    Write-Log -Message "Script failed: $($_.Exception.Message)" -Level ERROR
    exit 1
}
finally {
    Stop-Transcript
}