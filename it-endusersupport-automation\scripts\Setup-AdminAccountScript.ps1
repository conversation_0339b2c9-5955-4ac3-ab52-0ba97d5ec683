#Requires -Version 5.1

<#
.SYNOPSIS
Setup script for the Enhanced Admin Account Creation Script.

.DESCRIPTION
This script helps configure the secure credential storage and validates the environment
for the Admin Account Creation Script. It sets up the PowerShell SecretManagement vault,
validates configuration files, and provides setup guidance.

.PARAMETER SetupCredentials
Switch to configure credential storage.

.PARAMETER ValidateEnvironment
Switch to validate the environment and dependencies.

.PARAMETER CreateSampleConfig
Switch to create a sample configuration file.

.EXAMPLE
.\Setup-AdminAccountScript.ps1 -SetupCredentials -ValidateEnvironment

.NOTES
Version: 2.0
Author: Emmanuel <PERSON>mo
Security Level: Enhanced
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [switch]$SetupCredentials,
    
    [Parameter(Mandatory = $false)]
    [switch]$ValidateEnvironment,
    
    [Parameter(Mandatory = $false)]
    [switch]$CreateSampleConfig
)

# Color scheme for consistent output
$Colors = @{
    Success = "Green"
    Warning = "Yellow"
    Error = "Red"
    Information = "Cyan"
    Debug = "Gray"
    Progress = "Blue"
}

function Write-SetupMessage {
    param(
        [string]$Message,
        [ValidateSet('Success', 'Warning', 'Error', 'Information', 'Debug', 'Progress')]
        [string]$Type = 'Information'
    )
    
    Write-Host "[$Type] $Message" -ForegroundColor $Colors[$Type]
}

function Test-ModuleAvailability {
    param([string]$ModuleName)
    
    $module = Get-Module -ListAvailable -Name $ModuleName -ErrorAction SilentlyContinue
    return $null -ne $module
}

function Install-RequiredModule {
    param(
        [string]$ModuleName,
        [string]$Description
    )
    
    Write-SetupMessage "Checking module: $ModuleName" -Type Information
    
    if (Test-ModuleAvailability -ModuleName $ModuleName) {
        Write-SetupMessage "Module $ModuleName is already installed." -Type Success
        return $true
    }
    
    Write-SetupMessage "Installing module: $ModuleName ($Description)" -Type Progress
    
    try {
        Install-Module -Name $ModuleName -Scope CurrentUser -Force -SkipPublisherCheck -AllowClobber -ErrorAction Stop
        Write-SetupMessage "Successfully installed $ModuleName" -Type Success
        return $true
    }
    catch {
        Write-SetupMessage "Failed to install $ModuleName : $($_.Exception.Message)" -Type Error
        return $false
    }
}

function Initialize-SecretVault {
    param(
        [string]$VaultName = "AdminAccountVault"
    )
    
    Write-SetupMessage "Setting up SecretManagement vault: $VaultName" -Type Progress
    
    try {
        # Check if vault already exists
        $existingVault = Get-SecretVault -Name $VaultName -ErrorAction SilentlyContinue
        
        if ($existingVault) {
            Write-SetupMessage "Vault '$VaultName' already exists." -Type Information
            return $true
        }
        
        # Register the SecretStore vault
        Register-SecretVault -Name $VaultName -ModuleName Microsoft.PowerShell.SecretStore -DefaultVault -ErrorAction Stop
        
        Write-SetupMessage "Successfully created vault: $VaultName" -Type Success
        
        # Configure the vault for automation (optional password)
        $storeConfig = @{
            Authentication = 'None'  # No password required for automation
            PasswordTimeout = -1     # Never timeout
            Interaction = 'None'     # No user interaction required
        }
        
        Set-SecretStoreConfiguration @storeConfig -Confirm:$false -ErrorAction Stop
        Write-SetupMessage "Configured vault for automation use." -Type Success
        
        return $true
    }
    catch {
        Write-SetupMessage "Failed to setup SecretManagement vault: $($_.Exception.Message)" -Type Error
        return $false
    }
}

function Set-SecretCredentials {
    param([string]$VaultName = "AdminAccountVault")
    
    Write-SetupMessage "Setting up credentials in vault..." -Type Progress
    
    # Jira Username
    $jiraUsername = Read-Host "Enter Jira Username (e.g., <EMAIL>)"
    if (-not [string]::IsNullOrWhiteSpace($jiraUsername)) {
        try {
            Set-Secret -Name "AdminScript-JiraUsername" -Secret $jiraUsername -Vault $VaultName -ErrorAction Stop
            Write-SetupMessage "Jira username stored successfully." -Type Success
        }
        catch {
            Write-SetupMessage "Failed to store Jira username: $($_.Exception.Message)" -Type Error
        }
    }
    
    # Jira API Token
    $jiraToken = Read-Host "Enter Jira API Token" -AsSecureString
    if ($jiraToken.Length -gt 0) {
        try {
            Set-Secret -Name "AdminScript-JiraApiToken" -Secret $jiraToken -Vault $VaultName -ErrorAction Stop
            Write-SetupMessage "Jira API token stored successfully." -Type Success
        }
        catch {
            Write-SetupMessage "Failed to store Jira API token: $($_.Exception.Message)" -Type Error
        }
    }
    
    # SMTP Credentials (optional)
    $setupSmtp = Read-Host "Do you want to configure SMTP credentials? (Y/N)"
    if ($setupSmtp -eq 'Y') {
        $smtpCreds = Get-Credential -Message "Enter SMTP credentials"
        if ($smtpCreds) {
            try {
                Set-Secret -Name "AdminScript-SmtpCredentials" -Secret $smtpCreds -Vault $VaultName -ErrorAction Stop
                Write-SetupMessage "SMTP credentials stored successfully." -Type Success
            }
            catch {
                Write-SetupMessage "Failed to store SMTP credentials: $($_.Exception.Message)" -Type Error
            }
        }
    }
}

function Test-Environment {
    Write-SetupMessage "Validating environment..." -Type Progress
    
    $issues = @()
    
    # Check PowerShell version
    if ($PSVersionTable.PSVersion.Major -lt 5) {
        $issues += "PowerShell 5.1 or higher is required. Current version: $($PSVersionTable.PSVersion)"
    } else {
        Write-SetupMessage "PowerShell version: $($PSVersionTable.PSVersion) ✓" -Type Success
    }
    
    # Check required modules
    $requiredModules = @(
        @{Name = "ActiveDirectory"; Description = "Active Directory management" },
        @{Name = "JiraPS"; Description = "Jira integration" }
    )
    
    foreach ($module in $requiredModules) {
        if (Test-ModuleAvailability -ModuleName $module.Name) {
            Write-SetupMessage "Module $($module.Name): Available ✓" -Type Success
        } else {
            $issues += "Required module missing: $($module.Name) ($($module.Description))"
        }
    }
    
    # Check optional security modules
    $securityModules = @(
        @{Name = "Microsoft.PowerShell.SecretManagement"; Description = "Secure credential storage" },
        @{Name = "Microsoft.PowerShell.SecretStore"; Description = "Local secret store" }
    )
    
    foreach ($module in $securityModules) {
        if (Test-ModuleAvailability -ModuleName $module.Name) {
            Write-SetupMessage "Security module $($module.Name): Available ✓" -Type Success
        } else {
            Write-SetupMessage "Optional security module missing: $($module.Name)" -Type Warning
        }
    }
    
    # Check configuration file
    $configPath = Join-Path $PSScriptRoot "AdminAccountConfig.psd1"
    if (Test-Path $configPath) {
        Write-SetupMessage "Configuration file: Found ✓" -Type Success
        
        # Validate configuration structure
        try {
            $config = Import-PowerShellDataFile -Path $configPath -ErrorAction Stop
            $requiredSections = @('ScriptSettings', 'Logging', 'ActiveDirectory', 'Email', 'Jira', 'Security', 'UserExperience')
            
            foreach ($section in $requiredSections) {
                if ($config.ContainsKey($section)) {
                    Write-SetupMessage "Config section '$section': Present ✓" -Type Success
                } else {
                    $issues += "Missing configuration section: $section"
                }
            }
        }
        catch {
            $issues += "Configuration file is invalid: $($_.Exception.Message)"
        }
    } else {
        $issues += "Configuration file not found: $configPath"
    }
    
    # Check log directory permissions
    $logDir = "C:\Temp\Scripts\Desktop Support\Logs"
    try {
        if (-not (Test-Path $logDir)) {
            New-Item -ItemType Directory -Path $logDir -Force | Out-Null
        }
        
        # Test write permissions
        $testFile = Join-Path $logDir "test_$(Get-Date -Format 'yyyyMMdd_HHmmss').tmp"
        "test" | Out-File -FilePath $testFile -ErrorAction Stop
        Remove-Item $testFile -Force -ErrorAction SilentlyContinue
        
        Write-SetupMessage "Log directory permissions: OK ✓" -Type Success
    }
    catch {
        $issues += "Cannot write to log directory: $logDir. Error: $($_.Exception.Message)"
    }
    
    # Report results
    if ($issues.Count -eq 0) {
        Write-SetupMessage "Environment validation completed successfully! ✓" -Type Success
        return $true
    } else {
        Write-SetupMessage "Environment validation found issues:" -Type Error
        foreach ($issue in $issues) {
            Write-SetupMessage "  - $issue" -Type Error
        }
        return $false
    }
}

# Main execution
Write-SetupMessage "Admin Account Script Setup Utility" -Type Progress
Write-SetupMessage "Version 2.0 - Enhanced Security Edition" -Type Information
Write-SetupMessage "=" * 50 -Type Information

if ($ValidateEnvironment) {
    Write-SetupMessage "Starting environment validation..." -Type Progress
    $envValid = Test-Environment
    
    if (-not $envValid) {
        Write-SetupMessage "Please resolve the issues above before running the admin account script." -Type Warning
    }
}

if ($SetupCredentials) {
    Write-SetupMessage "Starting credential setup..." -Type Progress
    
    # Install SecretManagement modules if needed
    $secretMgmtInstalled = Install-RequiredModule -ModuleName "Microsoft.PowerShell.SecretManagement" -Description "Secure credential storage"
    $secretStoreInstalled = Install-RequiredModule -ModuleName "Microsoft.PowerShell.SecretStore" -Description "Local secret store"
    
    if ($secretMgmtInstalled -and $secretStoreInstalled) {
        # Import modules
        Import-Module Microsoft.PowerShell.SecretManagement -Force
        Import-Module Microsoft.PowerShell.SecretStore -Force
        
        # Setup vault
        if (Initialize-SecretVault) {
            Set-SecretCredentials
        }
    } else {
        Write-SetupMessage "Cannot setup credentials without SecretManagement modules." -Type Error
    }
}

if ($CreateSampleConfig) {
    $configPath = Join-Path $PSScriptRoot "AdminAccountConfig.psd1"
    if (Test-Path $configPath) {
        Write-SetupMessage "Configuration file already exists: $configPath" -Type Warning
        $overwrite = Read-Host "Do you want to overwrite it? (Y/N)"
        if ($overwrite -ne 'Y') {
            Write-SetupMessage "Configuration file creation cancelled." -Type Information
            exit
        }
    }
    
    Write-SetupMessage "Sample configuration file already exists in the script directory." -Type Information
    Write-SetupMessage "Please review and customize: $configPath" -Type Information
}

Write-SetupMessage "Setup completed. Please review any warnings or errors above." -Type Success
Write-SetupMessage "For help, see the documentation in the script header." -Type Information
