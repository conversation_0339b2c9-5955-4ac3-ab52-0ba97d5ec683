name: '🚀 Bicep Release ${{ parameters.environment }} workspace ~ $(Date:yyyy-MM-dd HH-mm) UTC'


pr: none
trigger: none


resources:
  repositories:
  - repository: pipelines
    type      : git
    name      : it/it-devops-pipelines
    ref       : refs/heads/main


parameters:
- name: environment # The configuration files in the folder with the same name under ./params will be used
  displayName: Please select your environment
  default: "prod"
  values:
  - "prod"
  - "nonprod"

- name: location # Location to deploy the route tables
  displayName: Please select your location
  default: "uksouth"
  values:
  - "uksouth"
  - "ukwest"
  - "eastasia"
  - "southeastasia"
  - "eastasia"
  - "southeastasia"


variables:
  - template: /params/config.yml


stages:
- template: /pipelines/01_stage/bicep_build.yml@pipelines
  parameters:
    bicepFolder: "workspace"

- ${{ if eq(parameters.environment, 'prod') }}:
  - template: /pipelines/01_stage/bicep_test.yml@pipelines
    parameters:
      bicepFolder: "workspace"

- template: /pipelines/01_stage/bicep_release.yml@pipelines
  parameters:
    bicepFolder: "workspace"
    # environment lifecycles
    environmentList: ["${{ parameters.environment }}"]
    locationLoop   : true
    locationList   : 
      - ${{ if or(eq(parameters.location, 'uksouth'), eq(parameters.location, 'ukwest')) }}:
        - uksouth
        - ukwest
      - ${{ if or(eq(parameters.location, 'southeastasia'), eq(parameters.location, 'eastasia')) }}:
        - southeastasia
        - eastasia

- ${{ if ne(parameters.environment, 'prod') }}:
  - template: /pipelines/01_stage/bicep_test.yml@pipelines
    parameters:
      bicepFolder: "workspace"

