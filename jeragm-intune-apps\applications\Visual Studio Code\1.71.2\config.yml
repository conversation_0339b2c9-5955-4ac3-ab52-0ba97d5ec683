name         : "Visual Studio Code"
publisher    : "Microsoft"
appVersion   : "1.71.2"
description  : "Visual Studio Code is a code editor redefined and optimized for building and debugging modern web and cloud applications."
installExp   : "system"
featured     : false
assignments  : 
  Intune-Users-Developers:
  - intent   : "available"
dependencies : "none"
commandLine  :
- install    : 'Visual Studio Code.exe /verysilent /norestart /mergetasks=!runcode /log="%temp%VSCodeInstall.log"'
- uninstall  : '"C :\Program Files\Microsoft VS Code\unins000.exe" /verysilent /norestart'