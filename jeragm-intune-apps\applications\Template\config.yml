name         : # The display name of the application, this can be different than the application name.
publisher    : # The name of the publisher 
appVersion   : # The version, can be a string for example 2.40 or 2.40-beta
description  : # Description for the application.
installExp   : # Either "system" or "user". Use "system" if the installation requires administrative privileges
featured     : # "false" or "true". Setting this to "true" will make the app more prominent in the Company Portal
supersede    : # version of the app to replace, for example "6.7.1". Set to "none" to disable
assignments  : # group(s) to assign. If providing multiple groups separate by a comma example "group one, group two"
  groupName  : # Type the exact app name of the group. Repeat this line and the two sub-values for additional groups.
  - intent   : # "available", "required" or "uninstall" for the groups specified in assignment
dependencies : # Only to be used if there is an app dependency otherwise leave all values as "none"
  appName    : # Don't add this line if there are no dependencies. Type the exact app name of the dependency. Repeat this line and the two sub-values for additional dependencies.
  - version  : # Don't add this line if there are no dependencies. Type the version of the app that is the dependency, for example "6.7.1"
  - type     : # Don't add this line if there are no dependencies. Specify the dependency behavior, use AutoInstall to force install without an assignment requirement and Detect when an assignment is required. If unsure use "AutoInstall"
commandLine  :
- install    : # Install command for example 'Azure CLI.msi /quiet'. When using a "install.ps1" file use "applicationName.exe" as the script will be converted to .exe.
- uninstall  : # Uninstall command for example 'Azure CLI.msi /uninstall'