parameters:
  subscriptions : ""
  svcConnection : ""
  location      : ""
  primaryRegion : ""

steps:
- task: AzurePowerShell@5
  displayName: Config RBAC ${{ parameters.location }}
  inputs:
    azurePowerShellVersion: latestVersion
    azureSubscription     : ${{ parameters.svcConnection }}
    pwsh                  : true
    scriptPath            : $(System.DefaultWorkingDirectory)/scripts/config_rbac.ps1
    scriptType            : filePath
    workingDirectory      : $(System.DefaultWorkingDirectory)/scripts/
    ScriptArguments       :
      -location       "${{ parameters.location }}"
      -subscriptions  "${{ parameters.subscriptions }}"
      -primaryRegion  "${{ parameters.primaryRegion }}"
      -deploymentName "deployment-$(Build.BuildId)"
