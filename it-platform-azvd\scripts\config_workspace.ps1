# [CmdletBinding()]
param (
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$deploymentName,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$subscriptions,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$location
)

# LOCATION SWITCH
switch ($location) {
    southeastasia { 
        $deploymentLocation = "South East Asia"
        $subscription = $subscriptions.Split(',') | Where-Object { $_ -match "sea" }
    }
    eastasia { 
        $deploymentLocation = "East Asia"
        $subscription = $subscriptions.Split(',') | Where-Object { $_ -match "eaa" }
    }
    uksouth { 
        $deploymentLocation = "UK South" 
        $subscription = $subscriptions.Split(',') | Where-Object { $_ -match "uks" }
    }
    ukwest { 
        $deploymentLocation = "UK West" 
        $subscription = $subscriptions.Split(',') | Where-Object { $_ -match "ukw" }
    }
}

# Set the subscription Context
Write-Host "`n`n##[command]`tSet-AzContext -Subscription $($subscription) 🚶"
Set-AzContext -Subscription $subscription

$tenantId       = $(Get-AzContext).tenant.id
$subscriptionId = $(Get-AzContext).subscription.id

Write-Host "##[debug]🐞`tTenant ID          : $tenantId"
Write-Host "##[debug]🐞`tSubscription       : $subscription"
Write-Host "##[debug]🐞`tSubscription ID    : $subscriptionId"
Write-Host "##[debug]🐞`tDeployment Location: $deploymentLocation"
Write-host "##[debug]🐞`tDeployment Name    : $deploymentName"

# Get the outputs from the deployment
$outputs = (Get-AzDeployment -Name "$deploymentName").Outputs

$resourceGroup    = $outputs.get_item("o_resourceGroupName").value
$sharedAzvdRgName = $outputs.get_item("o_sharedAzvdRgName").value
$workspaceName    = $outputs.get_item("o_workspaceName").value

# Add the application group to the workspace
forEach ($appgroup in $outputs["o_applicationGroupName"].Value) {
    $applicationGroupName = $appgroup["name"].Value

    Write-Host "##[debug]🐞`tApplication Group Name : $applicationGroupName"

    $check = (Get-AzWvdWorkspace -ResourceGroupName $sharedAzvdRgName -Name $workspaceName).ApplicationGroupReference

    if ($check -match $applicationGroupName) {
        Write-Host "The Application Group $applicationGroupName is already registered"

    } else {
        Write-Host "The Application Group is not registered, registering now"

        Register-AzWvdApplicationGroup -ResourceGroupName $sharedAzvdRgName -WorkspaceName $workspaceName `
            -ApplicationGroupPath "/subscriptions/$subscriptionId/resourceGroups/$resourceGroup/providers/Microsoft.DesktopVirtualization/applicationGroups/$applicationGroupName"
    }
}
