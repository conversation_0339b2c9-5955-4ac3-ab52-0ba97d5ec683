{"p_appGroups": {"value": [{"applicationGroupType": "Desktop", "description": "Azure Virtual Desktop Application Group for Rietlanden - Pooled.", "friendlyName": "JERAGM Application Group - Rietlanden (Pooled)", "appgroupName": "rietlanden-pooled", "aadUserGroup": "AZVD Users - Rietlanden - Pooled", "hostpoolName": "rietlanden-pooled"}, {"applicationGroupType": "RemoteApp", "description": "RemoteApps for Rietlanden - Pooled.", "friendlyName": "RemoteApps for Rietlanden - Rietlanden (Pooled)", "appgroupName": "rietlanden-remoteapp", "aadUserGroup": "AZVD Users - Rietlanden - Pooled", "hostpoolName": "rietlanden-pooled"}]}, "p_remoteApps": {"value": [{"applicationName": "remote-desktop", "description": "Remote Desktop Connection", "friendlyName": "Remote Desktop Connection", "filePath": "C:\\Windows\\System32\\mstsc.exe", "hostpoolName": "rietlanden-pooled"}]}, "p_hostPools": {"value": [{"customRdpProperty": "drivestoredirect:s:;audiomode:i:0;videoplaybackmode:i:1;redirectclipboard:i:0;redirectprinters:i:0;devicestoredirect:s:;redirectcomports:i:0;redirectsmartcards:i:0;usbdevicestoredirect:s:;enablecredsspsupport:i:1;use multimon:i:1;autoreconnection enabled:i:1;bandwidthautodetect:i:1;networkautodetect:i:1;compression:i:1;audiocapturemode:i:1;encode redirected video capture:i:1;redirected video capture encoding quality:i:2;camerastoredirect:s:*;selectedmonitors:s:;maximizetocurrentdisplays:i:1;singlemoninwindowedmode:i:1;screen mode id:i:1;smart sizing:i:1;dynamic resolution:i:1;desktopscalefactor:i:100;targetisaadjoined:i:1;enablerdsaadauth:i:1;redirectwebauthn:i:1;", "description": "Azure Virtual Desktop Host Pool for Rietlanden - Pooled.", "friendlyName": "JERAGM Host Pool - <PERSON><PERSON><PERSON><PERSON> (Pooled)", "hostPoolType": "<PERSON>d", "loadBalancerType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maxSessionLimit": 4, "personalDesktopAssignmentType": "Direct", "preferredAppGroupType": "Desktop", "startVMOnConnect": false, "hostpoolName": "rietlanden-pooled", "hostpoolNameShort": "azvdrtl", "validationEnvironment": false, "aadUserGroup": "AZVD Users - Rietlanden - Pooled", "fslogixEnabled": true}]}, "p_scalingPlans": {"value": [{"enabled": false, "hostpoolName": "rietlanden-pooled"}]}}