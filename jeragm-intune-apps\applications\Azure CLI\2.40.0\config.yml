name        : "Azure CLI"
publisher   : "Microsoft"
appVersion  : "2.40.0"
description : "Use the Azure CLI for Windows to connect to Azure and execute administrative commands on Azure resources."
installExp  : "system"
featured    : false
supersede   : none
assignments  : 
  Intune-Users-Developers:
  - intent   : "available"
dependency  :
- app       : "none"
- version   : "none"
- type      : "none"
commandLine :
- install   : 'Msiexec.exe /i "Azure CLI.msi" /qn'
- uninstall : 'Msiexec.exe /x "Azure CLI.msi" /qn'