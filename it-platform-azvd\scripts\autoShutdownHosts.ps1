$subscriptions = Get-AzSubscription

foreach ($subscription in $subscriptions) {
    #Clear out variables
    $PersonalHostPools = $null
    $AutoStartHosts = $null
    $HostsToStop = $null

    Set-AzContext -Subscription $subscription.Id
    $CurrentContext = Get-AzContext
    Write-Host "`n`n💡 Current Context: "$CurrentContext.Name""

        $HostPools = Get-AzWvdHostPool
        $PersonalHostPools = @()
        Write-Host "`n🏃‍♂️ Looking for Personal Host Pools"
        foreach ($HostPool in $HostPools) {
        if ($HostPool.HostPoolType -eq "Personal")
        {Write-Host "`t$($HostPool.Name) is a Personal Host Pool ☑️"
            $PersonalHostPools += $HostPool
            } else {
                Write-Host "`t$($HostPool.Name) is a Pooled Host Pool ❌"
            }
        }

        if ($PersonalHostPools -ne $null) {
            $AutoStartHosts = @()
            Write-Host "`n🔎 Checking for Personal Hosts with AutoStartonConnect Enabled"
            foreach ($PersonalHostPool in $PersonalHostPools) {
                if ($PersonalHostPool.StartVMOnConnect -eq $true){
                    Write-Host "$($PersonalHostPool.Name) has AutoStartHostPool enabled ☑️"
                    $AutoStartHosts += $PersonalHostPool
                } else {
                    Write-Host "$($PersonalHostPool.Name) does not have AutoStartHostPool enabled ❌"
                }
            }
        }


        if ($AutoStartHosts -ne $null) {
            $HostsToStop = @()
            Write-Host "`n🔎 Checking for Host Sessions"
            foreach ($AutoStartHost in $AutoStartHosts) {
                $resourceGroup = $AutoStartHost.Id.Split('/')[4]
                try {
                    $PersonalHosts = Get-AzWvdSessionHost -HostPoolName $AutoStartHost.Name -ResourceGroupName $resourceGroup
                }
                catch {
                    Write-Host "`t`t⛔ ##[error] ERROR: $_.Exception.Message"
                }
                foreach ($PersonalHost in $PersonalHosts) {
                    if ($PersonalHost.Session -eq "0"){
                        $PersonalHostName = $PersonalHost.Name.Split('/')[1]
                        Write-Host "$PersonalHostName has no active sessions"
                        $HostsToStop += $PersonalHost
                    } else {
                        Write-Host "$PersonalHostName has $($PersonalHost.Session) active sessions"
                    }
                }
            }
        }


        if ($HostsToStop -ne $null) {
            Write-Host "`n🛑 Stopping Inactive Hosts"
            foreach ($AVDHost in $HostsToStop) {
                try {
                    $resourceGroup = $AVDHost.Id.Split('/')[4]
                    $HostToStopName = $AVDHost.Name.Split('/')[1]
                    Stop-AzVM -ResourceGroupName $resourceGroup -Name $HostToStopName -Force
                    Write-Host "$($HostToStop.Name) has been stopped 🎉"
                }
                catch {
                    Write-Host "`t`t⛔ ##[error] ERROR: $_.Exception.Message"
                }
            }
        }

}

<#
  .SYNOPSIS
  Stop unused personal AVD Hosts.

  .DESCRIPTION
  The script looks through all subscriptions and finds all Personal Host Pools with AutoStartHostPool enabled. It then checks for active sessions and stops any hosts that have no active sessions.

  .PARAMETER InputPath
  None are required.

  .PARAMETER OutputPath
  None are required.

  .INPUTS
  None are required.

  .OUTPUTS
  None are required.
#>