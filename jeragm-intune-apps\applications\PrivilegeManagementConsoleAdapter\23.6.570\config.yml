name         : "PrivilegeManagementConsoleAdapter"
publisher    : "BeyondTrust"
appVersion   : "23.6.570"
description  : "BeyondTrust Privilege Management for Windows Servers reduces the risk of privilege misuse by assigning admin privileges to only authorized tasks that require them, controlling application and script usage, and logging and monitoring on privileged activities."
installExp   : "system"
featured     : false
supersede    : "none"
assignments  : 
  JERAGM Internal Users:
  - intent   : "available"
  Privileged Accounts:
  - intent   : "available"
dependencies :
  PrivilegeManagement:
  - version  : "*********"
  - type     : "AutoInstall"
commandLine  :
- install    : 'msiexec.exe /i "PrivilegeManagementConsoleAdapter_x64.msi" TENANTID="37f66ef5-24b6-370f-8fb0-0913c7d15b20" INSTALLATIONID="e9376094-fe6d-4b13-874f-7bc0f3881f11" INSTALLATIONKEY="P9HC0AWPBCAk5IJ8lZ2G6Fn7wiAt4+1lUZd3+jMvNbk=" SERVICEURI="https://jeragm-services.pm.beyondtrustcloud.com" GROUPID="d8c2b07a-f6d2-4763-8349-fd0bb67a54f0" /qn'
- uninstall  : 'msiexec.exe "PrivilegeManagementConsoleAdapter_x64.msi" /uninstall' #not tested yet