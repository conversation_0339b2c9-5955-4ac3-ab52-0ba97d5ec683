#Requires -Version 5.1

<#
.SYNOPSIS
JML Setup and Environment Validation Module

.DESCRIPTION
This module provides setup and environment validation functionality for the JML (Jo<PERSON>, Mover, Leaver) 
admin account management script. It handles credential storage setup, environment validation,
module installation, and configuration file management with comprehensive error handling.

.NOTES
Version:        1.12
Author:         <PERSON>
Creation Date:  2025-01-09
Security Level: Enhanced with comprehensive data protection

Dependencies:
- PowerShell 5.1 or higher
- JML-Configuration module
- JML-Security module
- JML-Logging module
#>

# Import required modules
Import-Module (Join-Path $PSScriptRoot "JML-Configuration.psm1") -Force
Import-Module (Join-Path $PSScriptRoot "JML-Security.psm1") -Force
Import-Module (Join-Path $PSScriptRoot "JML-Logging.psm1") -Force

# Module variables
$script:Colors = @{
    Success = "Green"
    Warning = "Yellow"
    Error = "Red"
    Information = "Cyan"
    Debug = "Gray"
    Progress = "Blue"
}

<#
.SYNOPSIS
Writes setup messages with consistent formatting and logging integration.

.DESCRIPTION
Provides standardized message output for setup operations with color coding,
logging integration, and audit trail support.

.PARAMETER Message
The message to display and log.

.PARAMETER Type
The message type for color coding and log level.

.PARAMETER AuditTrail
Optional audit trail information.

.EXAMPLE
Write-SetupMessage "Module installed successfully" -Type Success

.NOTES
Integrates with the JML logging system for comprehensive audit trails.
#>
function Write-SetupMessage {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Message,

        [Parameter(Mandatory = $false)]
        [ValidateSet('Success', 'Warning', 'Error', 'Information', 'Debug', 'Progress')]
        [string]$Type = 'Information',

        [Parameter(Mandatory = $false)]
        [hashtable]$AuditTrail
    )

    try {
        # Map setup message types to log levels
        $logLevel = switch ($Type) {
            'Success' { 'INFO' }
            'Warning' { 'WARNING' }
            'Error' { 'ERROR' }
            'Information' { 'INFO' }
            'Debug' { 'DEBUG' }
            'Progress' { 'INFO' }
            default { 'INFO' }
        }

        # Display message with color
        Write-Host "[$Type] $Message" -ForegroundColor $script:Colors[$Type]

        # Log message if logging is available
        if (Get-Command Write-SecureLog -ErrorAction SilentlyContinue) {
            Write-SecureLog -Message "Setup: $Message" -LogLevel $logLevel -AuditTrail $AuditTrail
        }
    }
    catch {
        # Fallback to basic output if logging fails
        Write-Host "[$Type] $Message" -ForegroundColor $script:Colors[$Type]
        Write-Warning "Setup logging failed: $($_.Exception.Message)"
    }
}

<#
.SYNOPSIS
Tests if a PowerShell module is available on the system.

.DESCRIPTION
Checks for module availability using Get-Module with comprehensive error handling
and logging for audit purposes.

.PARAMETER ModuleName
Name of the module to check.

.OUTPUTS
Boolean indicating if the module is available.

.EXAMPLE
$isAvailable = Test-ModuleAvailability -ModuleName "ActiveDirectory"

.NOTES
Used for dependency validation and setup verification.
#>
function Test-ModuleAvailability {
    [CmdletBinding()]
    [OutputType([bool])]
    param(
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$ModuleName
    )

    try {
        $module = Get-Module -ListAvailable -Name $ModuleName -ErrorAction SilentlyContinue
        $isAvailable = $null -ne $module

        Write-SetupMessage "Module availability check: $ModuleName = $isAvailable" -Type Debug -AuditTrail @{
            Operation = "ModuleAvailabilityCheck"
            ModuleName = $ModuleName
            IsAvailable = $isAvailable
        }

        return $isAvailable
    }
    catch {
        Write-SetupMessage "Error checking module availability for $ModuleName : $($_.Exception.Message)" -Type Error
        return $false
    }
}

<#
.SYNOPSIS
Installs required PowerShell modules with comprehensive error handling.

.DESCRIPTION
Installs PowerShell modules using Install-Module with security best practices,
comprehensive error handling, and audit trail logging.

.PARAMETER ModuleName
Name of the module to install.

.PARAMETER Description
Description of the module for user information.

.PARAMETER Scope
Installation scope (CurrentUser or AllUsers).

.OUTPUTS
Boolean indicating if installation was successful.

.EXAMPLE
$success = Install-RequiredModule -ModuleName "ActiveDirectory" -Description "Active Directory management"

.NOTES
Uses CurrentUser scope by default to avoid requiring elevated privileges.
#>
function Install-RequiredModule {
    [CmdletBinding()]
    [OutputType([bool])]
    param(
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$ModuleName,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$Description,

        [Parameter(Mandatory = $false)]
        [ValidateSet('CurrentUser', 'AllUsers')]
        [string]$Scope = 'CurrentUser'
    )

    try {
        Write-SetupMessage "Checking module: $ModuleName" -Type Information -AuditTrail @{
            Operation = "ModuleInstallationCheck"
            ModuleName = $ModuleName
            Description = $Description
        }

        if (Test-ModuleAvailability -ModuleName $ModuleName) {
            Write-SetupMessage "Module $ModuleName is already installed." -Type Success
            return $true
        }

        Write-SetupMessage "Installing module: $ModuleName ($Description)" -Type Progress -AuditTrail @{
            Operation = "ModuleInstallationStart"
            ModuleName = $ModuleName
            Scope = $Scope
        }

        $installParams = @{
            Name = $ModuleName
            Scope = $Scope
            Force = $true
            SkipPublisherCheck = $true
            AllowClobber = $true
            ErrorAction = 'Stop'
        }

        Install-Module @installParams

        Write-SetupMessage "Successfully installed $ModuleName" -Type Success -AuditTrail @{
            Operation = "ModuleInstallationSuccess"
            ModuleName = $ModuleName
        }

        return $true
    }
    catch [System.UnauthorizedAccessException] {
        $errorMsg = "Insufficient permissions to install module $ModuleName. Run as administrator or install manually."
        Write-SetupMessage $errorMsg -Type Error
        return $false
    }
    catch [System.Net.WebException] {
        $errorMsg = "Network error installing module $ModuleName. Check internet connectivity and proxy settings."
        Write-SetupMessage $errorMsg -Type Error
        return $false
    }
    catch {
        $errorMsg = "Failed to install $ModuleName : $($_.Exception.Message)"
        Write-SetupMessage $errorMsg -Type Error -AuditTrail @{
            Operation = "ModuleInstallationError"
            ModuleName = $ModuleName
            ErrorType = $_.Exception.GetType().Name
            ErrorMessage = $_.Exception.Message
        }
        return $false
    }
}

<#
.SYNOPSIS
Initializes PowerShell SecretManagement vault for secure credential storage.

.DESCRIPTION
Creates and configures a SecretManagement vault with automation-friendly settings
for secure credential storage with comprehensive error handling and logging.

.PARAMETER VaultName
Name of the vault to create.

.OUTPUTS
Boolean indicating if vault initialization was successful.

.EXAMPLE
$success = Initialize-SecretVault -VaultName "AdminAccountVault"

.NOTES
Configures vault for automation use with no password requirement.
#>
function Initialize-SecretVault {
    [CmdletBinding()]
    [OutputType([bool])]
    param(
        [Parameter(Mandatory = $false)]
        [ValidateNotNullOrEmpty()]
        [string]$VaultName = "AdminAccountVault"
    )

    try {
        Write-SetupMessage "Setting up SecretManagement vault: $VaultName" -Type Progress -AuditTrail @{
            Operation = "SecretVaultInitialization"
            VaultName = $VaultName
        }

        # Check if vault already exists
        $existingVault = Get-SecretVault -Name $VaultName -ErrorAction SilentlyContinue

        if ($existingVault) {
            Write-SetupMessage "Vault '$VaultName' already exists." -Type Information
            return $true
        }

        # Register the SecretStore vault
        Register-SecretVault -Name $VaultName -ModuleName Microsoft.PowerShell.SecretStore -DefaultVault -ErrorAction Stop

        Write-SetupMessage "Successfully created vault: $VaultName" -Type Success

        # Configure the vault for automation (optional password)
        $storeConfig = @{
            Authentication = 'None'  # No password required for automation
            PasswordTimeout = -1     # Never timeout
            Interaction = 'None'     # No user interaction required
        }

        Set-SecretStoreConfiguration @storeConfig -Confirm:$false -ErrorAction Stop
        Write-SetupMessage "Configured vault for automation use." -Type Success -AuditTrail @{
            Operation = "SecretVaultConfigured"
            VaultName = $VaultName
            Configuration = $storeConfig
        }

        return $true
    }
    catch {
        $errorMsg = "Failed to setup SecretManagement vault: $($_.Exception.Message)"
        Write-SetupMessage $errorMsg -Type Error -AuditTrail @{
            Operation = "SecretVaultError"
            VaultName = $VaultName
            ErrorType = $_.Exception.GetType().Name
        }
        return $false
    }
}

<#
.SYNOPSIS
Sets up secure credentials in the SecretManagement vault.

.DESCRIPTION
Prompts for and stores credentials in the SecretManagement vault with
comprehensive validation and error handling.

.PARAMETER VaultName
Name of the vault to store credentials in.

.OUTPUTS
Boolean indicating if credential setup was successful.

.EXAMPLE
$success = Set-SecretCredentials -VaultName "AdminAccountVault"

.NOTES
Prompts for Jira and SMTP credentials with secure input handling.
#>
function Set-SecretCredentials {
    [CmdletBinding()]
    [OutputType([bool])]
    param(
        [Parameter(Mandatory = $false)]
        [ValidateNotNullOrEmpty()]
        [string]$VaultName = "AdminAccountVault"
    )

    try {
        Write-SetupMessage "Setting up credentials in vault..." -Type Progress -AuditTrail @{
            Operation = "CredentialSetupStart"
            VaultName = $VaultName
        }

        $successCount = 0
        $totalCredentials = 0

        # Jira Username
        $jiraUsername = Read-Host "Enter Jira Username (e.g., <EMAIL>)"
        if (-not [string]::IsNullOrWhiteSpace($jiraUsername)) {
            $totalCredentials++
            try {
                # Validate input
                $cleanUsername = Confirm-InputSecurity -InputString $jiraUsername.Trim() -InputType "UPN"
                Set-Secret -Name "AdminScript-JiraUsername" -Secret $cleanUsername -Vault $VaultName -ErrorAction Stop
                Write-SetupMessage "Jira username stored successfully." -Type Success
                $successCount++
            }
            catch {
                Write-SetupMessage "Failed to store Jira username: $($_.Exception.Message)" -Type Error
            }
        }

        # Jira API Token
        $jiraToken = Read-Host "Enter Jira API Token" -AsSecureString
        if ($jiraToken.Length -gt 0) {
            $totalCredentials++
            try {
                Set-Secret -Name "AdminScript-JiraApiToken" -Secret $jiraToken -Vault $VaultName -ErrorAction Stop
                Write-SetupMessage "Jira API token stored successfully." -Type Success
                $successCount++
            }
            catch {
                Write-SetupMessage "Failed to store Jira API token: $($_.Exception.Message)" -Type Error
            }
        }

        # SMTP Credentials (optional)
        $setupSmtp = Read-Host "Do you want to configure SMTP credentials? (Y/N)"
        if ($setupSmtp -eq 'Y' -or $setupSmtp -eq 'y') {
            $totalCredentials++
            $smtpCreds = Get-Credential -Message "Enter SMTP credentials"
            if ($smtpCreds) {
                try {
                    Set-Secret -Name "AdminScript-SmtpCredentials" -Secret $smtpCreds -Vault $VaultName -ErrorAction Stop
                    Write-SetupMessage "SMTP credentials stored successfully." -Type Success
                    $successCount++
                }
                catch {
                    Write-SetupMessage "Failed to store SMTP credentials: $($_.Exception.Message)" -Type Error
                }
            }
        }

        # Report results
        Write-SetupMessage "Credential setup completed: $successCount/$totalCredentials credentials stored successfully." -Type Information -AuditTrail @{
            Operation = "CredentialSetupComplete"
            VaultName = $VaultName
            SuccessCount = $successCount
            TotalCredentials = $totalCredentials
        }

        return $successCount -eq $totalCredentials
    }
    catch {
        Write-SetupMessage "Credential setup failed: $($_.Exception.Message)" -Type Error
        return $false
    }
}

<#
.SYNOPSIS
Performs comprehensive environment validation for the JML system.

.DESCRIPTION
Validates PowerShell version, required modules, configuration files, permissions,
and other environment requirements with detailed reporting.

.OUTPUTS
Boolean indicating if environment validation passed.

.EXAMPLE
$isValid = Test-Environment

.NOTES
Performs comprehensive validation with detailed error reporting and remediation suggestions.
#>
function Test-Environment {
    [CmdletBinding()]
    [OutputType([bool])]
    param()

    try {
        Write-SetupMessage "Validating environment..." -Type Progress -AuditTrail @{
            Operation = "EnvironmentValidationStart"
        }

        $issues = @()
        $warnings = @()

        # Check PowerShell version
        if ($PSVersionTable.PSVersion.Major -lt 5) {
            $issues += "PowerShell 5.1 or higher is required. Current version: $($PSVersionTable.PSVersion)"
        } else {
            Write-SetupMessage "PowerShell version: $($PSVersionTable.PSVersion) ✓" -Type Success
        }

        # Check required modules
        $requiredModules = @(
            @{Name = "ActiveDirectory"; Description = "Active Directory management"; Required = $true },
            @{Name = "JiraPS"; Description = "Jira integration"; Required = $false }
        )

        foreach ($module in $requiredModules) {
            if (Test-ModuleAvailability -ModuleName $module.Name) {
                Write-SetupMessage "Module $($module.Name): Available ✓" -Type Success
            } else {
                if ($module.Required) {
                    $issues += "Required module missing: $($module.Name) ($($module.Description))"
                } else {
                    $warnings += "Optional module missing: $($module.Name) ($($module.Description))"
                }
            }
        }

        # Check optional security modules
        $securityModules = @(
            @{Name = "Microsoft.PowerShell.SecretManagement"; Description = "Secure credential storage" },
            @{Name = "Microsoft.PowerShell.SecretStore"; Description = "Local secret store" }
        )

        foreach ($module in $securityModules) {
            if (Test-ModuleAvailability -ModuleName $module.Name) {
                Write-SetupMessage "Security module $($module.Name): Available ✓" -Type Success
            } else {
                $warnings += "Optional security module missing: $($module.Name)"
            }
        }

        # Check configuration file
        $configPath = Join-Path (Split-Path $PSScriptRoot -Parent) "AdminAccountConfig.psd1"
        if (Test-Path $configPath) {
            Write-SetupMessage "Configuration file: Found ✓" -Type Success

            # Validate configuration structure
            try {
                $config = Import-PowerShellDataFile -Path $configPath -ErrorAction Stop
                $requiredSections = @('ScriptSettings', 'Logging', 'ActiveDirectory', 'Email', 'Jira', 'Security', 'UserExperience')

                foreach ($section in $requiredSections) {
                    if ($config.ContainsKey($section)) {
                        Write-SetupMessage "Config section '$section': Present ✓" -Type Success
                    } else {
                        $issues += "Missing configuration section: $section"
                    }
                }
            }
            catch {
                $issues += "Configuration file is invalid: $($_.Exception.Message)"
            }
        } else {
            $warnings += "Configuration file not found: $configPath (will use intelligent defaults)"
        }

        # Check log directory permissions
        $config = Get-ModuleConfiguration
        $logDir = if ($config) { $config.Logging.LogDirectory } else { "C:\Temp\Scripts\Desktop Support\Logs" }

        try {
            if (-not (Test-Path $logDir)) {
                New-Item -ItemType Directory -Path $logDir -Force | Out-Null
            }

            # Test write permissions
            $testFile = Join-Path $logDir "test_$(Get-Date -Format 'yyyyMMdd_HHmmss').tmp"
            "test" | Out-File -FilePath $testFile -ErrorAction Stop
            Remove-Item $testFile -Force -ErrorAction SilentlyContinue

            Write-SetupMessage "Log directory permissions: OK ✓" -Type Success
        }
        catch {
            $issues += "Cannot write to log directory: $logDir. Error: $($_.Exception.Message)"
        }

        # Report warnings
        foreach ($warning in $warnings) {
            Write-SetupMessage "  - $warning" -Type Warning
        }

        # Report results
        if ($issues.Count -eq 0) {
            Write-SetupMessage "Environment validation completed successfully! ✓" -Type Success -AuditTrail @{
                Operation = "EnvironmentValidationSuccess"
                WarningsCount = $warnings.Count
            }
            return $true
        } else {
            Write-SetupMessage "Environment validation found issues:" -Type Error
            foreach ($issue in $issues) {
                Write-SetupMessage "  - $issue" -Type Error
            }
            Write-SetupMessage "Please resolve the issues above before running the admin account script." -Type Warning
            return $false
        }
    }
    catch {
        Write-SetupMessage "Environment validation failed: $($_.Exception.Message)" -Type Error
        return $false
    }
}

<#
.SYNOPSIS
Performs complete setup of the JML system including credentials and environment validation.

.DESCRIPTION
Orchestrates the complete setup process including module installation, credential configuration,
vault setup, and environment validation with comprehensive error handling and user guidance.

.PARAMETER SetupCredentials
Switch to configure credential storage.

.PARAMETER ValidateEnvironment
Switch to validate the environment and dependencies.

.PARAMETER InstallMissingModules
Switch to automatically install missing modules.

.OUTPUTS
Boolean indicating if setup completed successfully.

.EXAMPLE
$success = Start-JMLSetup -SetupCredentials -ValidateEnvironment -InstallMissingModules

.NOTES
Provides complete setup orchestration with user-friendly guidance and error recovery.
#>
function Start-JMLSetup {
    [CmdletBinding()]
    [OutputType([bool])]
    param(
        [Parameter(Mandatory = $false)]
        [switch]$SetupCredentials,

        [Parameter(Mandatory = $false)]
        [switch]$ValidateEnvironment,

        [Parameter(Mandatory = $false)]
        [switch]$InstallMissingModules
    )

    try {
        Write-SetupMessage "JML Admin Account Script Setup Utility" -Type Progress
        Write-SetupMessage "Version 1.12 - Enhanced Security Edition" -Type Information
        Write-SetupMessage "=" * 50 -Type Information

        $setupSuccess = $true

        # Environment validation
        if ($ValidateEnvironment) {
            Write-SetupMessage "Starting environment validation..." -Type Progress
            $envValid = Test-Environment

            if (-not $envValid) {
                Write-SetupMessage "Environment validation failed. Please resolve the issues above." -Type Warning
                $setupSuccess = $false

                if ($InstallMissingModules) {
                    Write-SetupMessage "Attempting to install missing modules..." -Type Progress

                    # Install required modules
                    $moduleInstallSuccess = $true
                    $requiredModules = @(
                        @{Name = "ActiveDirectory"; Description = "Active Directory management" },
                        @{Name = "Microsoft.PowerShell.SecretManagement"; Description = "Secure credential storage" },
                        @{Name = "Microsoft.PowerShell.SecretStore"; Description = "Local secret store" }
                    )

                    foreach ($module in $requiredModules) {
                        if (-not (Test-ModuleAvailability -ModuleName $module.Name)) {
                            $installResult = Install-RequiredModule -ModuleName $module.Name -Description $module.Description
                            if (-not $installResult) {
                                $moduleInstallSuccess = $false
                            }
                        }
                    }

                    if ($moduleInstallSuccess) {
                        Write-SetupMessage "Module installation completed. Re-validating environment..." -Type Progress
                        $envValid = Test-Environment
                        $setupSuccess = $envValid
                    }
                }
            } else {
                Write-SetupMessage "Environment validation passed successfully!" -Type Success
            }
        }

        # Credential setup
        if ($SetupCredentials -and $setupSuccess) {
            Write-SetupMessage "Starting credential setup..." -Type Progress

            # Install SecretManagement modules if needed
            $secretMgmtInstalled = $true
            $requiredSecurityModules = @(
                @{Name = "Microsoft.PowerShell.SecretManagement"; Description = "Secure credential storage" },
                @{Name = "Microsoft.PowerShell.SecretStore"; Description = "Local secret store" }
            )

            foreach ($module in $requiredSecurityModules) {
                if (-not (Test-ModuleAvailability -ModuleName $module.Name)) {
                    $installResult = Install-RequiredModule -ModuleName $module.Name -Description $module.Description
                    if (-not $installResult) {
                        $secretMgmtInstalled = $false
                    }
                }
            }

            if ($secretMgmtInstalled) {
                # Import modules
                try {
                    Import-Module Microsoft.PowerShell.SecretManagement -Force -ErrorAction Stop
                    Import-Module Microsoft.PowerShell.SecretStore -Force -ErrorAction Stop

                    # Setup vault
                    if (Initialize-SecretVault) {
                        $credentialSetupResult = Set-SecretCredentials
                        if (-not $credentialSetupResult) {
                            Write-SetupMessage "Credential setup encountered issues. Please review the messages above." -Type Warning
                        }
                    } else {
                        Write-SetupMessage "Failed to initialize SecretManagement vault." -Type Error
                        $setupSuccess = $false
                    }
                }
                catch {
                    Write-SetupMessage "Failed to import SecretManagement modules: $($_.Exception.Message)" -Type Error
                    $setupSuccess = $false
                }
            } else {
                Write-SetupMessage "Cannot setup credentials without SecretManagement modules." -Type Error
                $setupSuccess = $false
            }
        }

        # Final status report
        if ($setupSuccess) {
            Write-SetupMessage "Setup completed successfully! ✓" -Type Success -AuditTrail @{
                Operation = "JMLSetupComplete"
                SetupCredentials = $SetupCredentials.IsPresent
                ValidateEnvironment = $ValidateEnvironment.IsPresent
                InstallMissingModules = $InstallMissingModules.IsPresent
            }
            Write-SetupMessage "The JML Admin Account Script is ready to use." -Type Information
        } else {
            Write-SetupMessage "Setup completed with issues. Please review any warnings or errors above." -Type Warning
            Write-SetupMessage "Some functionality may not be available until issues are resolved." -Type Information
        }

        Write-SetupMessage "For help and documentation, see the script headers and README files." -Type Information

        return $setupSuccess
    }
    catch {
        Write-SetupMessage "Setup failed with error: $($_.Exception.Message)" -Type Error
        return $false
    }
}

<#
.SYNOPSIS
Gets the current version information for the JML system.

.DESCRIPTION
Returns version information for the JML system including module versions,
dependencies, and system information.

.OUTPUTS
Hashtable containing version and system information.

.EXAMPLE
$versionInfo = Get-JMLVersion

.NOTES
Provides comprehensive version and system information for troubleshooting.
#>
function Get-JMLVersion {
    [CmdletBinding()]
    [OutputType([hashtable])]
    param()

    try {
        $versionInfo = @{
            JMLVersion = "1.12"
            PowerShellVersion = $PSVersionTable.PSVersion.ToString()
            OperatingSystem = [System.Environment]::OSVersion.ToString()
            ComputerName = $env:COMPUTERNAME
            CurrentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name
            ScriptPath = $PSScriptRoot
            Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            Modules = @{}
        }

        # Check JML modules
        $jmlModules = @(
            "JML-Configuration", "JML-Security", "JML-Logging", "JML-Utilities",
            "JML-ActiveDirectory", "JML-Email", "JML-Jira", "JML-Setup"
        )

        foreach ($moduleName in $jmlModules) {
            $module = Get-Module -Name $moduleName -ErrorAction SilentlyContinue
            if ($module) {
                $versionInfo.Modules[$moduleName] = @{
                    Version = if ($module.Version) { $module.Version.ToString() } else { "1.12" }
                    Path = $module.Path
                    Loaded = $true
                }
            } else {
                $versionInfo.Modules[$moduleName] = @{
                    Version = "Unknown"
                    Path = "Not loaded"
                    Loaded = $false
                }
            }
        }

        # Check external dependencies
        $dependencies = @("ActiveDirectory", "Microsoft.PowerShell.SecretManagement", "Microsoft.PowerShell.SecretStore")
        foreach ($dep in $dependencies) {
            $module = Get-Module -ListAvailable -Name $dep -ErrorAction SilentlyContinue | Select-Object -First 1
            if ($module) {
                $versionInfo.Modules[$dep] = @{
                    Version = $module.Version.ToString()
                    Path = $module.ModuleBase
                    Loaded = (Get-Module -Name $dep -ErrorAction SilentlyContinue) -ne $null
                }
            } else {
                $versionInfo.Modules[$dep] = @{
                    Version = "Not installed"
                    Path = "Not available"
                    Loaded = $false
                }
            }
        }

        return $versionInfo
    }
    catch {
        Write-SetupMessage "Failed to get version information: $($_.Exception.Message)" -Type Error
        return @{
            JMLVersion = "1.12"
            Error = $_.Exception.Message
        }
    }
}

# Export functions
Export-ModuleMember -Function Write-SetupMessage, Test-ModuleAvailability, Install-RequiredModule, Initialize-SecretVault, Set-SecretCredentials, Test-Environment, Start-JMLSetup, Get-JMLVersion
