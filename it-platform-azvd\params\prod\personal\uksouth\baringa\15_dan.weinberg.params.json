{
    "p_hosts": {
        "value": [
            {
                "properties": {
                    "adminUserName"   : "jeragmadm",
                    "availabilityZone": true,
                    "azvdType"        : "Personal",
                    "hostNumber"      : 15,
                    "hostpoolName"    : "consultants-personal",
                    "hostSku"         : "Standard_D4s_v3",
                    "userName"        : "<EMAIL>"
                },
                "storageProfile": {
                    "osDisk": {
                        "createOption": "FromImage", // FromImage creates via imageReference below, <PERSON><PERSON><PERSON> creates via imageId
                        "imageId"     : "",
                        "type"        : "Premium_LRS"
                    },
                    "imageReference": {
                        "offer"    : "windows-ent-cpc",
                        "publisher": "microsoftwindowsdesktop",
                        "sku"      : "win10-21h2-ent-cpc-m365",
                        "version"  : "latest"
                    }
                },
                "networking": {
                    "resourceGroup"     : "uks-prd-ssv-networking-rg",
                    "subnetName"        : "azure-virtual-desktop-snet",
                    "virtualNetworkName": "uks-prd-ssv-spoke-vnet"
                }
            }
        ]
    }
}