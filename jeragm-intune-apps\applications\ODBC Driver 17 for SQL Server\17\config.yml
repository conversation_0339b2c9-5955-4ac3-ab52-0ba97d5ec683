name        : "ODBC Driver 17 for SQL Server"
publisher   : "Microsoft"
appVersion  : "17"
description : "Microsoft ODBC Driver for SQL Server is a single dynamic-link library (DLL) containing run-time support for applications using native-code APIs to connect to SQL Server. Use Microsoft ODBC Driver 18 for SQL Server to create new applications or enhance existing applications that need to take advantage of newer SQL Server features."
installExp  : "system"
featured    : true
supersede   : "none"
assignments  : 
  JERAGM Internal Users:
  - intent   : "available"
dependencies : "none"
commandLine :
- install   : 'msiexec /quiet /passive /qn /i msodbcsql.msi IACCEPTMSODBCSQLLICENSETERMS=YES ADDLOCAL=ALL'
- uninstall : 'msiexec /quiet /passive /qn /uninstall msodbcsql.msi'