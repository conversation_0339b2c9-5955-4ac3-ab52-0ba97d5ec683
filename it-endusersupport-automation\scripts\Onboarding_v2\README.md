# Enhanced Admin Account Creation Script v2.0

## Overview

This PowerShell script automates the creation, deletion, and management of admin accounts in Active Directory with comprehensive security features, Jira integration, and audit trails.

## 🔒 Security Features

- **Secure Credential Storage**: Uses PowerShell SecretManagement module with multiple fallback options
- **Data Redaction**: Automatically redacts sensitive information in logs and Jira comments
- **Audit Trails**: Comprehensive logging with user identity tracking and operation details
- **Input Validation**: Robust validation and sanitization of all user inputs
- **Encrypted Communications**: Secure handling of credentials and sensitive data

## 🚀 Quick Start

### 1. Initial Setup

Run the setup script to configure your environment:

```powershell
.\Setup-AdminAccountScript.ps1 -ValidateEnvironment -SetupCredentials
```

### 2. Basic Usage

```powershell
# Run with default configuration
.\AdminAccountCreation.ps1

# Run with debug logging
.\AdminAccountCreation.ps1 -LogLevel DEBUG

# Run with custom configuration
.\AdminAccountCreation.ps1 -ConfigPath "C:\Config\MyConfig.psd1"

# Skip Jira integration for testing
.\AdminAccountCreation.ps1 -SkipJiraIntegration
```

## 📋 Prerequisites

### Required Software
- **PowerShell 5.1** or higher
- **Active Directory PowerShell Module**
- **JiraPS Module** for Jira integration

### Required Permissions
- **Active Directory**: User creation rights in target OUs
- **File System**: Read/Write access to log directory
- **SMTP**: Send email permissions (if email notifications enabled)
- **Jira**: API access with comment and attachment permissions

### Optional (Recommended for Enhanced Security)
- **Microsoft.PowerShell.SecretManagement** module
- **Microsoft.PowerShell.SecretStore** module

## 🔧 Configuration

### Configuration File Structure

The script uses a PowerShell Data File (.psd1) for configuration. Key sections include:

- **ScriptSettings**: General behavior and defaults
- **Logging**: Log levels, retention, and redaction settings
- **ActiveDirectory**: OU mappings and query optimization
- **Email**: SMTP settings and notification preferences
- **Jira**: API settings, field mappings, and formatting options
- **Security**: Credential storage and validation settings
- **UserExperience**: Console output and progress indicators

### Sample Configuration

```powershell
@{
    ScriptSettings = @{
        DefaultDomain = "yourdomain.com"
        MaxRetryAttempts = 3
        ShowProgress = $true
    }
    
    Logging = @{
        LogDirectory = "C:\Logs\AdminScript"
        EnableDataRedaction = $true
        LogRetentionDays = 30
    }
    
    # ... additional sections
}
```

## 🔐 Credential Storage Setup

### Option 1: PowerShell SecretManagement (Recommended)

1. Install required modules:
```powershell
Install-Module Microsoft.PowerShell.SecretManagement -Scope CurrentUser
Install-Module Microsoft.PowerShell.SecretStore -Scope CurrentUser
```

2. Run the setup script:
```powershell
.\Setup-AdminAccountScript.ps1 -SetupCredentials
```

3. Store your credentials:
```powershell
# Jira credentials
Set-Secret -Name "AdminScript-JiraUsername" -Secret "<EMAIL>"
Set-Secret -Name "AdminScript-JiraApiToken" -Secret (Read-Host -AsSecureString)
```

### Option 2: Windows Credential Manager

Store credentials using Windows Credential Manager with the following target names:
- `AdminScript-JiraUsername`
- `AdminScript-JiraApiToken`
- `AdminScript-SmtpCredentials`

### Option 3: Encrypted Files (Fallback)

The script can use DPAPI-encrypted credential files as a fallback option.

## 📊 Logging and Audit Trails

### Log File Features
- **Automatic Data Redaction**: Sensitive information is automatically masked
- **Audit Trails**: Complete operation tracking with user identity
- **Configurable Retention**: Automatic cleanup of old log files
- **Multiple Output Levels**: DEBUG, INFO, WARNING, ERROR, CRITICAL

### Log File Locations
- Default: `C:\Temp\Scripts\Desktop Support\Logs\`
- Configurable via configuration file
- Files named: `AdminAccount_{UserHash}_{Timestamp}.log`

### Sample Log Entry
```
2025-01-09 14:30:15.123 | DOMAIN\user*** | INFO     | Admin account created for user***@domain.com | AUDIT: Operation=Create; UserHash=A1B2C3D4
```

## 🎯 Jira Integration

### Features
- **Automatic Ticket Validation**: Validates ticket type and request type
- **Rich Comments**: Formatted comments with operation details
- **File Attachments**: Automatic log file attachment for successful operations
- **Error Handling**: Robust retry logic with exponential backoff

### Jira Comment Format
The script adds structured comments to Jira tickets:

```
Admin Account Created Successfully

A new admin account has been created for [REDACTED USER].

New Admin Account Details:
- Username: [REDACTED]
- Display Name: [REDACTED]
- Account Status: Disabled (requires manual activation)

The detailed log file has been attached for auditing purposes.

Processed by: [REDACTED]
Date: 2025-01-09 14:30:15
```

## 🛠️ Troubleshooting

### Common Issues

1. **Module Installation Failures**
   - Run PowerShell as Administrator
   - Check internet connectivity
   - Verify PowerShell execution policy: `Set-ExecutionPolicy RemoteSigned -Scope CurrentUser`

2. **Permission Errors**
   - Verify Active Directory permissions
   - Check log directory write permissions
   - Ensure SMTP relay permissions

3. **Jira Connection Issues**
   - Verify API token validity
   - Check network connectivity to Jira instance
   - Validate Jira permissions for comment and attachment operations

4. **Credential Storage Issues**
   - Ensure SecretManagement modules are installed
   - Verify vault configuration
   - Check Windows Credential Manager access

### Debug Mode

Run with debug logging for detailed troubleshooting:

```powershell
.\AdminAccountCreation.ps1 -LogLevel DEBUG
```

### Environment Validation

Use the setup script to validate your environment:

```powershell
.\Setup-AdminAccountScript.ps1 -ValidateEnvironment
```

## 📈 Performance Considerations

- **AD Query Optimization**: Configurable property filtering and result limits
- **Caching**: Optional result caching for improved performance
- **Retry Logic**: Exponential backoff for network operations
- **Resource Cleanup**: Automatic cleanup of temporary resources

## 🔄 Version History

### Version 2.0 (Current)
- Enhanced security with comprehensive data redaction
- PowerShell SecretManagement integration
- Improved error handling with typed exceptions
- Configurable retry logic and timeouts
- Rich Jira integration with formatted comments
- Comprehensive audit trails
- Performance optimizations

### Version 1.1 (Previous)
- Basic admin account creation
- Simple Jira integration
- Email notifications
- Basic logging

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review log files for detailed error information
3. Validate environment using the setup script
4. Contact your IT administrator for permission-related issues

## 🔒 Security Considerations

- **Never store credentials in plain text**
- **Regularly rotate API tokens and passwords**
- **Review log files for sensitive data exposure**
- **Implement proper NTFS permissions on script directories**
- **Use least-privilege principles for service accounts**

## 📝 License

This script is provided as-is for internal organizational use. Please review and test thoroughly before production deployment.
