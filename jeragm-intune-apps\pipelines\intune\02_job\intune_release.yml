parameters:
  appName      : ""
  appVersion   : ""
  svcConnection: ""

jobs:
- deployment : Release
  displayName: JERAGM_Intune_Release
  environment: JERAGM_NonProd

  pool:
    vmImage: windows-2019

  strategy:
    runOnce:
      deploy:
        steps:
        - checkout: self

        - template: /pipelines/intune/03_step/intune_build.yml
          parameters:
            svcConnection : $(svcConnection)
            appName       : "${{ parameters.appName }}"
            appVersion    : "${{ parameters.appVersion }}"