[CmdletBinding()]
param (
    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$appName,

    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$appVersion
)

#Set-PSDebug -Trace 2 #Enable to see debug log
$ErrorActionPreference = "Stop"

# Import parameters from config.yml
Write-Host "Get application config"
$basePath  = $pwd.Path + "\applications"
$appFolder = "$basePath\$appName"

$appConfig = Get-Content "$appFolder\$appVersion\config.yml" | ConvertFrom-Yaml -Ordered

if ($appConfig.supersede -ne "none") {
    Write-Host "Connect to Microsoft Graph API"
    Connect-MSIntuneGraph -TenantID $env:tenantId -ClientID $env:servicePrincipalId -ClientSecret $env:servicePrincipalKey

    # Get the app ID of the app that will supersede another app. 
    $appSuperseding = Get-IntuneWin32App -DisplayName $appConfig.name | Where-Object {$_.displayVersion -eq $appVersion}

    # Get the app ID of the app that will be superseded by another app. 
    $appSuperseded = Get-IntuneWin32App -DisplayName $appConfig.name | Where-Object {$_.displayVersion -eq $appConfig.supersede}

    $appSupersededDisplyaName  = $appSuperseded.DisplayVersion
    $appSupersedingDisplayName = $appSuperseding.DisplayVersion

    Write-Host "Debug $appSupersededDisplyaName"
    Write-Host "Superseding $appName $appSupersededDisplyaName with $appSupersedingDisplayName"

    $AppSupersedence = New-IntuneWin32AppSupersedence -ID $appSuperseded.id -SupersedenceType Update
    Add-IntuneWin32AppSupersedence -ID $appSuperseding.id -Supersedence $AppSupersedence
}