//PARAMETERS

param p_tags               object
param p_host               object
param p_location           string
param p_locationShort      string
param p_hostPoolName       string
param p_hostPoolNameShort  string
param p_hostPoolRegKey     string
@secure() // Prevents the key from being shown in the output
param p_password           string = '!kYdbvuIrFCGePyHf2aJf!${newGuid()}' // newGuid ensures the password is unique with each run
param p_commandToExecute   string = loadTextContent('../../../it-devops-pipelines/scripts/installCato.ps1')
param p_environmentShort   string
param p_storageAccountName string

//VARIABLES

var v_hostName           = '${p_locationShort}PWV${p_hostPoolNameShort}${padLeft(p_host.properties.hostNumber, 2, '0')}'
var v_storageAccountName = toLower('jgm${p_locationShort}${p_environmentShort}${p_storageAccountName}st')

//RESOURCES

resource virtualNetwork 'Microsoft.Network/virtualNetworks@2023-04-01' existing  = {
    name  : p_host.networking.virtualNetworkName
    scope : resourceGroup(p_host.networking.resourceGroup)
}

resource subnet 'Microsoft.Network/virtualNetworks/subnets@2023-04-01' existing = {
    name  : p_host.networking.subnetName
    parent: virtualNetwork
}

resource hostNic 'Microsoft.Network/networkInterfaces@2023-04-01' = {
    name    : '${toUpper(v_hostName)}-nic'// Example: UKSPWVAZVDTT01-nic, UKSPWVAZVD is the vmname and TT the team code.
    location: p_location
    tags    : p_tags
    properties: {
       ipConfigurations: [
        {
            name: 'ipconfig'
            properties: {
               privateIPAllocationMethod: 'Dynamic'
               subnet: {
                  id: subnet.id
               }
            }
        }]
    }
}

resource hosts 'Microsoft.Compute/virtualMachines@2023-03-01' = {
    name    : toUpper(v_hostName) // Example: UKSPWVAZVDTT01, UKSPWVAZVD is the vmname and TT the team code.
    location: p_location
    tags    : union(p_tags, { RBAC_User: p_host.properties.userName }, { AVD_Type: p_host.properties.azvdType })
    identity: {
        type: 'SystemAssigned'
    }
    zones: (p_host.properties.availabilityZone ? [ string(p_host.properties.hostNumber % 3 + 1) ] : null)
    properties: {
        licenseType: 'Windows_Client'
        hardwareProfile: {
            vmSize        : p_host.properties.hostSku
        }
        osProfile: p_host.storageProfile.osDisk.createOption == 'FromImage' ? {
            computerName  : toUpper(v_hostName) // Example: UKSPWVAZVDTT01, UKSPWVAZVD is the vmname and TT the team code.
            adminUsername : p_host.properties.adminUserName
            adminPassword : p_password
        } : null
        storageProfile: {
            osDisk: {
                name       : '${toUpper(v_hostName)}-osdisk' // Example: UKSPWVAZVDTT01-osdisk, UKSPWVAZVD is the vmname and TT the team code.
                managedDisk: {
                    storageAccountType: p_host.storageProfile.osDisk.type
                    id                : p_host.storageProfile.osDisk.createOption == 'FromImage' ? null : p_host.storageProfile.osDisk.imageId
                }
                osType      : 'Windows'
                createOption: p_host.storageProfile.osDisk.createOption
                deleteOption: 'Delete'
            }
            imageReference: p_host.storageProfile.osDisk.createOption == 'FromImage' ? p_host.storageProfile.imageReference : null
        }
        networkProfile: {
            networkInterfaces: [
                {
                    id: resourceId('Microsoft.Network/networkInterfaces', hostNic.name)
                    properties: {
                        deleteOption: 'Delete'
                    }
                }
            ]
        }
        diagnosticsProfile: {
            bootDiagnostics: {
                enabled: true
                storageUri: uri('https://${v_storageAccountName}.blob.${environment().suffixes.storage}', '/')
            }
        }
    }
}

resource vmCustomScriptExtension 'Microsoft.Compute/virtualMachines/extensions@2023-03-01'= {
    parent: hosts
    name: 'config-app'
     location: p_location
     properties: {
      publisher: 'Microsoft.Compute'
      type: 'CustomScriptExtension'
      typeHandlerVersion: '1.10'
      autoUpgradeMinorVersion: true
      protectedSettings: {
       commandToExecute: 'powershell.exe -ExecutionPolicy Unrestricted -Command ${p_commandToExecute}'
      }
     }
}

resource aadDomainJoin 'Microsoft.Compute/virtualMachines/extensions@2023-03-01'= {
    parent: hosts
    name: 'AADLoginForWindows'
    location: p_location
    properties: {
     publisher: 'Microsoft.Azure.ActiveDirectory'
     type: 'AADLoginForWindows'
     typeHandlerVersion: '1.0'
     autoUpgradeMinorVersion: true
     settings: {
      mdmId: '0000000a-0000-0000-c000-************'
     }
    }
    dependsOn: [
        vmCustomScriptExtension
    ]
}

resource joinHostPool 'Microsoft.Compute/virtualMachines/extensions@2023-03-01' = {
    parent: hosts
    name    : 'joinPool'
    location: p_location
    properties: {
        publisher: 'Microsoft.Powershell'
        type: 'DSC'
        typeHandlerVersion: '2.73'
        autoUpgradeMinorVersion: true
        settings: {
        #disable-next-line no-hardcoded-env-urls
            modulesUrl: 'https://wvdportalstorageblob.blob.core.windows.net/galleryartifacts/Configuration_03-30-2022.zip'
            configurationFunction: 'Configuration.ps1\\AddSessionHost'
            properties: {
                hostPoolName: p_hostPoolName
                registrationInfoToken: p_hostPoolRegKey
                aadJoin: true
            }
        }
    }
    dependsOn:[
        aadDomainJoin
    ]
}

// Outputs the name of the host to main.bicep
output o_hostname string = hosts.name
