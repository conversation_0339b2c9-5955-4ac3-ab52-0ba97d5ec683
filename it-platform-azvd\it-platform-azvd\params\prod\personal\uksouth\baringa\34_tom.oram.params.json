{"p_hosts": {"value": [{"properties": {"adminUserName": "jeragmadm", "availabilityZone": true, "azvdType": "Personal", "hostNumber": 34, "hostpoolName": "consultants-personal", "hostSku": "Standard_D8ds_v5", "userName": "<EMAIL>"}, "storageProfile": {"osDisk": {"createOption": "FromImage", "imageId": "", "type": "Premium_LRS"}, "imageReference": {"offer": "windows-ent-cpc", "publisher": "MicrosoftWindowsDesktop", "sku": "win10-22h2-ent-cpc-m365", "version": "latest"}}, "networking": {"resourceGroup": "uks-prd-ssv-networking-rg", "subnetName": "azure-virtual-desktop-snet", "virtualNetworkName": "uks-prd-ssv-spoke-vnet"}}]}}