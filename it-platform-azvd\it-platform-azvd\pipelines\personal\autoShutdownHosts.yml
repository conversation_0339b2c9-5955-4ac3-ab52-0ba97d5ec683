name: '🖥️ Stop Inactive AVD Personal Hosts ~ $(Date:yyyy-MM-dd HH-mm) UTC'


pr: none
trigger: none

schedules:
- cron: "0 0 * * *"
  displayName: Daily midnight Stop Hosts
  branches:
    include:
    - main

resources:
  repositories:
  - repository: pipelines
    type      : git
    name      : it/it-devops-pipelines
    ref       : refs/heads/main


parameters:
- name: environment
  displayName: Please select your environment
  default: "prod"
  values:
  - "prod"


variables:
  - template: /params/config.yml


steps:
- task: AzurePowerShell@5
  name: shutdownInactiveHosts
  displayName: 🧰 Shut Down Inactive Hosts
  inputs:
    azurePowerShellVersion: latestVersion
    azureSubscription     : $(${{ parameters.environment }}_svcConnection)
    pwsh                  : true
    scriptPath            : $(System.DefaultWorkingDirectory)/scripts/autoShutdownHosts.ps1
    scriptType            : filePath
    workingDirectory      : $(System.DefaultWorkingDirectory)/
    ScriptArguments: