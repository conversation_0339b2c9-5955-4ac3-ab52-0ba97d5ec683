#################################################################################
# Uses the ProductVersion property in the file                                  #
# Works with any program that stores the product version in the executable      #
#################################################################################

$version = "2023,1,0,558"
$fileLocation = "C:\Program Files\uniFLOW SmartClient\momsmartclnt.exe"

Try {
    $getVersion = (Get-Item $fileLocation).VersionInfo.ProductVersion
    If ($getVersion -like "*$version*"){
        Write-Output "Detected"
       Exit 0
    } 
    Exit 1
} 
Catch {
    Exit 1
}