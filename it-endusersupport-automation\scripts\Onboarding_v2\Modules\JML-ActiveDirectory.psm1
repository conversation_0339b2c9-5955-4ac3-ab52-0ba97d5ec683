#Requires -Version 5.1
#Requires -Modules ActiveDirectory

<#
.SYNOPSIS
JML Active Directory Management Module

.DESCRIPTION
This module provides Active Directory functionality for the JML (Joiner, Mover, Leaver) 
admin account management script. It handles user queries, account operations, OU selection,
and UPN validation with performance optimizations and caching.

.NOTES
Version:        2.0
Author:         <PERSON>
Creation Date:  2025-01-09
Security Level: Enhanced with comprehensive data protection

Dependencies:
- PowerShell 5.1 or higher
- ActiveDirectory module
- JML-Configuration module
- JML-Security module
- JML-Logging module
#>

# Import required modules
Import-Module (Join-Path $PSScriptRoot "JML-Configuration.psm1") -Force
Import-Module (Join-Path $PSScriptRoot "JML-Security.psm1") -Force
Import-Module (Join-Path $PSScriptRoot "JML-Logging.psm1") -Force
Import-Module (Join-Path $PSScriptRoot "JML-Utilities.psm1") -Force

# Module variables
$script:ADUserCache = @{}

<#
.SYNOPSIS
Retrieves user details from Active Directory with optimized queries and caching.

.DESCRIPTION
Performs efficient Active Directory queries with configurable property filtering,
result caching, and comprehensive error handling. Implements performance
optimizations for large domain environments.

.PARAMETER UserUPN
The User Principal Name to search for.

.PARAMETER Properties
Array of properties to retrieve. Uses optimized defaults if not specified.

.PARAMETER UseCache
Switch to enable result caching for improved performance.

.PARAMETER CacheExpirationMinutes
Cache expiration time in minutes.

.EXAMPLE
$user = Get-OptimizedADUserDetails -UserUPN "<EMAIL>" -UseCache

.NOTES
Implements AD query best practices with performance optimizations and caching.
#>
function Get-OptimizedADUserDetails {
    [CmdletBinding()]
    [OutputType([Microsoft.ActiveDirectory.Management.ADUser])]
    param(
        [Parameter(Mandatory = $true)]
        [ValidatePattern('^[\w][\w\.-]{1,62}[\w]@[\w][\w-]{1,62}\.[a-zA-Z]{2,}$')]
        [string]$UserUPN,

        [Parameter(Mandatory = $false)]
        [string[]]$Properties,

        [Parameter(Mandatory = $false)]
        [switch]$UseCache,

        [Parameter(Mandatory = $false)]
        [ValidateRange(1, 1440)]
        [int]$CacheExpirationMinutes = 15
    )

    try {
        Write-SecureLog -Message "Retrieving AD user details" -LogLevel "INFO" -AuditTrail @{
            Operation = "ADUserQuery"
            UserUPN = Get-StringHash -InputString $UserUPN
            CacheEnabled = $UseCache.IsPresent
        }

        # Get configuration
        $config = Get-ModuleConfiguration

        # Use configured properties or defaults
        if (-not $Properties) {
            $Properties = if ($config) {
                $config.ActiveDirectory.QueryOptimization.UserProperties
            } else {
                @('GivenName', 'Surname', 'DisplayName', 'SamAccountName', 'UserPrincipalName', 'EmailAddress', 'Enabled', 'DistinguishedName')
            }
        }

        # Check cache if enabled
        if ($UseCache -and $script:ADUserCache) {
            $cacheKey = "$UserUPN|$($Properties -join ',')"
            $cachedResult = $script:ADUserCache[$cacheKey]

            if ($cachedResult -and $cachedResult.Timestamp -gt (Get-Date).AddMinutes(-$CacheExpirationMinutes)) {
                Write-Verbose "Returning cached AD user result for $UserUPN"
                return $cachedResult.Data
            }
        }

        # Configure query parameters
        $queryParams = @{
            Filter = "UserPrincipalName -eq '$UserUPN'"
            Properties = $Properties
            ErrorAction = 'Stop'
        }

        # Add result limit if configured
        if ($config -and $config.ActiveDirectory.QueryOptimization.MaxResults) {
            $queryParams.ResultSetSize = $config.ActiveDirectory.QueryOptimization.MaxResults
        }

        # Execute query with timeout
        $user = $null
        $queryJob = Start-Job -ScriptBlock {
            param($QueryParams)
            Import-Module ActiveDirectory -Force
            Get-ADUser @QueryParams
        } -ArgumentList $queryParams

        $timeout = if ($config) {
            $config.ActiveDirectory.QueryOptimization.QueryTimeout
        } else {
            30
        }

        if (Wait-Job -Job $queryJob -Timeout $timeout) {
            $user = Receive-Job -Job $queryJob
            Remove-Job -Job $queryJob -Force
        } else {
            Remove-Job -Job $queryJob -Force
            throw "AD query timeout after $timeout seconds"
        }

        if (-not $user) {
            Write-SecureLog -Message "User not found in Active Directory" -LogLevel "WARNING" -AuditTrail @{
                Operation = "ADUserNotFound"
                UserUPN = Get-StringHash -InputString $UserUPN
            }
            return $null
        }

        # Cache result if enabled
        if ($UseCache) {
            if (-not $script:ADUserCache) {
                $script:ADUserCache = @{}
            }

            $cacheKey = "$UserUPN|$($Properties -join ',')"
            $script:ADUserCache[$cacheKey] = @{
                Data = $user
                Timestamp = Get-Date
            }
        }

        Write-SecureLog -Message "Successfully retrieved AD user details" -LogLevel "INFO" -AuditTrail @{
            Operation = "ADUserQuerySuccess"
            UserUPN = Get-StringHash -InputString $UserUPN
            PropertiesCount = $Properties.Count
        }

        return $user
    }
    catch [Microsoft.ActiveDirectory.Management.ADIdentityNotFoundException] {
        Write-SecureLog -Message "User not found in Active Directory: $($_.Exception.Message)" -LogLevel "WARNING"
        return $null
    }
    catch [System.TimeoutException] {
        Write-SecureLog -Message "AD query timeout: $($_.Exception.Message)" -LogLevel "ERROR"
        throw
    }
    catch {
        Write-SecureLog -Message "Failed to retrieve AD user details: $($_.Exception.Message)" -LogLevel "ERROR" -AuditTrail @{
            Operation = "ADUserQueryError"
            ErrorType = $_.Exception.GetType().Name
        }
        throw
    }
}

<#
.SYNOPSIS
Validates and retrieves a User Principal Name with enhanced input validation.

.DESCRIPTION
Provides comprehensive UPN validation with pattern matching, domain verification,
and existence checking. Supports different validation modes for create, delete,
and reset operations.

.PARAMETER Domain
The domain to validate against.

.PARAMETER ForDeletion
Switch indicating validation is for deletion operation.

.PARAMETER ForReset
Switch indicating validation is for reset operation.

.PARAMETER MaxAttempts
Maximum number of input attempts before failing.

.EXAMPLE
$upn = Get-ValidatedUPN -Domain "domain.com" -MaxAttempts 3

.NOTES
Implements comprehensive input validation with security best practices.
#>
function Get-ValidatedUPN {
    [CmdletBinding()]
    [OutputType([string])]
    param(
        [Parameter(Mandatory = $true)]
        [ValidatePattern('^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')]
        [string]$Domain,

        [Parameter(Mandatory = $false)]
        [switch]$ForDeletion,

        [Parameter(Mandatory = $false)]
        [switch]$ForReset,

        [Parameter(Mandatory = $false)]
        [ValidateRange(1, 10)]
        [int]$MaxAttempts = 3
    )

    $attempt = 1
    $validUPN = $null

    while ($attempt -le $MaxAttempts -and -not $validUPN) {
        try {
            Write-Host "`nAttempt $attempt of $MaxAttempts" -ForegroundColor Cyan

            # Provide context-specific prompts
            $promptMessage = switch ($true) {
                $ForDeletion { "Enter the UPN of the user whose admin account you want to DELETE" }
                $ForReset { "Enter the UPN of the user whose admin account you want to RESET" }
                default { "Enter the UPN of the user for whom you want to create an admin account" }
            }

            $upn = Read-Host $promptMessage

            # Input validation
            if ([string]::IsNullOrWhiteSpace($upn)) {
                Write-Host "UPN cannot be empty. Please try again." -ForegroundColor Red
                $attempt++
                continue
            }

            # Sanitize input using security module
            $upn = Confirm-InputSecurity -InputString $upn.Trim().ToLower() -InputType "UPN"

            # Domain validation
            $upnDomain = $upn.Split('@')[1]
            if ($upnDomain -ne $Domain.ToLower()) {
                Write-Host "UPN domain '$upnDomain' does not match expected domain '$Domain'." -ForegroundColor Red
                $attempt++
                continue
            }

            # Check if user exists in AD
            $existingUser = Get-OptimizedADUserDetails -UserUPN $upn -UseCache
            if (-not $existingUser) {
                Write-Host "User '$upn' not found in Active Directory." -ForegroundColor Red
                $attempt++
                continue
            }

            # Check admin account existence based on operation type
            if ($ForDeletion -or $ForReset) {
                $adminSamAccountName = "$($existingUser.SamAccountName)-a"
                $adminAccountExists = Get-ADUser -Filter "SamAccountName -eq '$adminSamAccountName'" -ErrorAction SilentlyContinue

                if (-not $adminAccountExists) {
                    Write-Host "No admin account exists for this user." -ForegroundColor Red
                    $attempt++
                    continue
                }
            }

            # All validations passed
            $validUPN = $upn

            Write-SecureLog -Message "UPN validation successful" -LogLevel "INFO" -AuditTrail @{
                Operation = "UPNValidation"
                ValidationMode = if ($ForDeletion) { "Delete" } elseif ($ForReset) { "Reset" } else { "Create" }
                Attempts = $attempt
                UserHash = Get-StringHash -InputString $upn
            }

        }
        catch {
            Write-Host "Error validating UPN: $($_.Exception.Message)" -ForegroundColor Red
            Write-SecureLog -Message "UPN validation error: $($_.Exception.Message)" -LogLevel "ERROR"
            $attempt++
        }
    }

    if (-not $validUPN) {
        $errorMsg = "Failed to get valid UPN after $MaxAttempts attempts."
        Write-SecureLog -Message $errorMsg -LogLevel "ERROR"
        throw $errorMsg
    }

    return $validUPN
}

# Maintain backward compatibility
function Get-ValidUPN {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Domain,

        [Parameter(Mandatory = $false)]
        [switch]$ForDeletion,

        [Parameter(Mandatory = $false)]
        [switch]$ForReset
    )

    return Get-ValidatedUPN -Domain $Domain -ForDeletion:$ForDeletion -ForReset:$ForReset
}

# Maintain backward compatibility
function Get-ADUserDetails {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$UserUPN
    )

    return Get-OptimizedADUserDetails -UserUPN $UserUPN -UseCache
}

<#
.SYNOPSIS
Prompts the user to select an Organizational Unit (OU) for an admin account.

.DESCRIPTION
This function allows the user to select an OU or automatically determine it based on
predefined mappings and office location information. It logs the selection process
and returns the corresponding OU path.

.PARAMETER OfficeLocation
Optional office location to automatically map to an OU.

.OUTPUTS
String - The selected OU path.

.EXAMPLE
$ouPath = Select-OU -OfficeLocation "Singapore"

.EXAMPLE
$ouPath = Select-OU  # Manual selection

.NOTES
Uses configuration-based OU mappings with fallback to manual selection.
#>
function Select-OU {
    [CmdletBinding()]
    [OutputType([string])]
    param (
        [Parameter(Mandatory = $false)]
        [string]$OfficeLocation
    )

    # Log the start of the OU selection process
    Write-SecureLog -Message "Starting OU selection process" -LogLevel "DEBUG"

    # Initialize the OU path variable
    [string]$ouPath = $null

    try {
        # Get configuration
        $config = Get-ModuleConfiguration

        # Define OU Mappings from configuration or use defaults
        $officeLocationToOU = if ($config -and $config.ActiveDirectory.OUMappings) {
            $config.ActiveDirectory.OUMappings
        } else {
            @{
                "singapore"      = "OU=Singapore,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com"
                "united kingdom" = "OU=London,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com"
                "london"         = "OU=London,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com"
            }
        }

        if ($OfficeLocation -and $officeLocationToOU.ContainsKey($OfficeLocation.ToLower())) {
            # OU determined from provided Office Location (e.g., Jira ticket)
            $ouPath = $officeLocationToOU[$OfficeLocation.ToLower()]
            Write-SecureLog -Message "Automatically selected OU based on Office Location '$OfficeLocation': $ouPath" -LogLevel "INFO"
        }

        if (-not $ouPath) {
            # Manual selection if no office location is provided or no mapping is found
            do {
                # Prompt the user for OU selection
                Write-Host "Unable to automatically determine OU. Please select the OU for the admin account:"

                # Display available options from configuration or defaults
                $options = @()
                $index = 1
                foreach ($location in $officeLocationToOU.Keys) {
                    Write-Host "$index for $($location.Substring(0,1).ToUpper() + $location.Substring(1))"
                    $options += @{
                        Index = $index
                        Location = $location
                        OU = $officeLocationToOU[$location]
                    }
                    $index++
                }

                [string]$selection = Read-Host "Enter your choice (1-$($options.Count))"
                Write-SecureLog -Message "User entered selection: $selection" -LogLevel "DEBUG"

                # Validate selection
                if ($selection -match '^\d+$') {
                    $selectionInt = [int]$selection
                    $selectedOption = $options | Where-Object { $_.Index -eq $selectionInt }

                    if ($selectedOption) {
                        $ouPath = $selectedOption.OU
                        Write-SecureLog -Message "Selected OU: $($selectedOption.Location)" -LogLevel "INFO"
                    } else {
                        Write-Host "Invalid selection. Please select a number between 1 and $($options.Count)." -ForegroundColor Red
                        Write-SecureLog -Message "Invalid OU selection: $selection" -LogLevel "WARNING"
                    }
                } else {
                    Write-Host "Invalid input. Please enter a number." -ForegroundColor Red
                    Write-SecureLog -Message "Invalid OU selection format: $selection" -LogLevel "WARNING"
                }
            } while (-not $ouPath)
        }
    }
    catch {
        # Log any exceptions that occur during the selection process
        Write-SecureLog -Message "An error occurred during OU selection: $($_.Exception.Message)" -LogLevel "ERROR"
        throw
    }

    # Log the completion of the OU selection process
    Write-SecureLog -Message "OU selection process completed with OU: $ouPath" -LogLevel "DEBUG"

    # Return the selected OU path
    return $ouPath
}

# Export functions
Export-ModuleMember -Function Get-OptimizedADUserDetails, Get-ValidatedUPN, Get-ValidUPN, Get-ADUserDetails, Select-OU
