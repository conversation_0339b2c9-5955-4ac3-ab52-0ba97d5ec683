﻿# Prompt user for User Principal Name
$userPrincipalName = Read-Host "Enter User Principal Name (UPN)"

# Hardcoded output path
$outputPath = '\\share.jeragm.com\share\Common\Installer\Emmanuel'

# Validate and construct export path
if (-not $outputPath) {
    $outputPath = '.\'
}

# Construct full export path
$exportCsvPath = Join-Path -Path $outputPath -ChildPath "$userPrincipalName.csv"

# Prompt user to export to CSV
$exportToCsv = Read-Host "Export to CSV? (True/False)"

# Convert user input to boolean
$exportToCsv = $exportToCsv -eq 'True'

# Import the Active Directory module
try {
    Import-Module ActiveDirectory -ErrorAction Stop
} catch {
    Write-Host "Error importing Active Directory module: $_"
    exit
}

# Get user information
try {
    $user = Get-ADUser -Filter {UserPrincipalName -eq $userPrincipalName} -Properties MemberOf -ErrorAction Stop
} catch {
    Write-Host "Error getting user information: $_"
    exit
}

# Check if the user exists
if ($user -eq $null) {
    Write-Host "User with UPN '$userPrincipalName' not found in Active Directory."
    exit
}

# Get group memberships and display
$groupMemberships = $user.MemberOf | Get-ADGroup | Select-Object -ExpandProperty Name
Write-Host "Group Memberships for ${userPrincipalName}: $($groupMemberships -join ', ')"

# Export to CSV if user chooses to
if ($exportToCsv) {
    try {
        $groupMemberships | ForEach-Object { [PSCustomObject]@{ GroupName = $_ } } | Export-Csv -Path $exportCsvPath -NoTypeInformation -ErrorAction Stop
        Write-Host "Group memberships exported to $exportCsvPath"
    } catch {
        Write-Host "Error exporting group memberships to CSV: $_"
    }
} else {
    Write-Host "CSV export skipped."
}
