name        : "Powershell"
publisher    : "Microsoft"
appVersion   : "7.2.6"
description  : "PowerShell Core is a automation and configuration tool/framework that works well with your existing tools and is optimized for dealing with structured data (e.g. JSON, CSV, XML, etc.), REST APIs, and object models. It includes a command-line shell, an associated scripting language and a framework for processing cmdlets."
installExp   : "system"
featured     : false
supersede    : "none"
assignments  : 
  Intune-Users-Developers:
  - intent   : "available"
dependencies : "none"
commandLine  :
- install    : 'MsiExec.exe /i PowerShell-7.2.6-win-x64.msi /qn'
- uninstall  : 'MsiExec.exe /x PowerShell-7.2.6-win-x64.msi /qn'