name         : "Azure CLI"
publisher    : "Microsoft"
appVersion   : "2.39.0"
description  : "Use the Azure CLI for Windows to connect to Azure and execute administrative commands on Azure resources."
installExp   : "system"
featured     : false
supersede    : "none"
assignments  : 
  Intune-Users-Developers:
  - intent   : "available"
dependencies : "none"
commandLine  :
- install    : 'Azure CLI.msi /quiet'
- uninstall  : 'Azure CLI.msi /uninstall'