name: Create package and upload to Intune

pr: none
trigger: none

parameters:
- name: appName
  displayName: Name of the application
  default: ""

- name: appVersion
  displayName: Application version to deploy
  default: ""

variables:
  - group: "jeragm-intune-apps"
  - template: /params/config.yml # global parameters that is the same across pipelines

stages:
- template: /pipelines/intune/01_stage/intune_build.yml
  parameters:
    appName    : "${{ parameters.appName }}"
    appVersion : "${{ parameters.appVersion }}"