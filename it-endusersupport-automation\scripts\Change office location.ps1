# Import the required modules
Import-Module ActiveDirectory

# Start logging
Start-Transcript -Path "C:\Temp\UpdateOfficeLocation.log" -Append

# Install the required feature
try {
    Install-WindowsFeature RSAT-AD-PowerShell -ErrorAction Stop
    Write-Host "Successfully installed the RSAT-AD-PowerShell feature."
} catch {
    Write-Host "Error installing the RSAT-AD-PowerShell feature: $_" -ForegroundColor Red
    exit
}

# Import the CSV file
try {
    $employeeList = Import-Csv 'C:\Temp\userlist.csv' -ErrorAction Stop
    Write-Host "Successfully imported the CSV file."
} catch {
    Write-Host "Error reading CSV file: $_" -ForegroundColor Red
    exit
}

# Define the function to get the office location
function Get-EmployeeOfficeLocation {
    param(
        [string]$officePrompt = "Select an office location:"
    )

    $officeOptions = @("Singapore", "London", "Tokyo")

    Write-Host $officePrompt
    for ($i = 0; $i -lt $officeOptions.Count; $i++) {
        Write-Host "$($i+1). $($officeOptions[$i])"
    }

    $selection = Read-Host "Enter your selection (1-3)"
    while (![int]::TryParse($selection, [ref]$null) -or $selection -lt 1 -or $selection -gt 3) {
        Write-Host "Invalid selection. Please enter a number between 1 and 3." -ForegroundColor Red
        $selection = Read-Host "Enter your selection (1-3)"
    }

    return $officeOptions[$selection-1]
}

# Get the new office location
$newOfficeLocation = Get-EmployeeOfficeLocation

# Update the office location for each employee
$totalEmployees = $employeeList.Count
$employeeList | ForEach-Object -Begin {
    $currentEmployee = 0
} -Process {
    try {
        $adUser = Get-ADUser -Filter "EmailAddress -eq '$($_.EmployeeID)'" -Properties Office -ErrorAction Stop
        $adUser | Set-ADUser -Office $newOfficeLocation -ErrorAction Stop
        Write-Host "Successfully updated office location for $($adUser.Name)"
    } catch {
        Write-Host "Error updating office location for $($_.EmployeeID): $_" -ForegroundColor Red
    }
    $currentEmployee++
    Write-Progress -Activity "Updating office locations" -Status "Processing employee $currentEmployee of $totalEmployees" -PercentComplete ($currentEmployee / $totalEmployees * 100)
} -End {
    Write-Progress -Activity "Updating office locations" -Status "Completed" -Completed
}

# Check for errors
if ($Error.Count -gt 0) {
    Write-Host "One or more errors occurred during the execution of the script. Please check the error messages for more details." -ForegroundColor Red
}

# Stop logging
Stop-Transcript
