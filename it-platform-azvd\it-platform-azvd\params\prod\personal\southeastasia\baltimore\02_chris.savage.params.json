{"p_hosts": {"value": [{"properties": {"adminUserName": "jeragmadm", "availabilityZone": true, "azvdType": "Personal", "hostNumber": 2, "hostpoolName": "baltimore-personal", "hostSku": "Standard_D8s_v3", "userName": "<EMAIL>"}, "storageProfile": {"osDisk": {"createOption": "FromImage", "imageId": "", "type": "Premium_LRS"}, "imageReference": {"offer": "windows-ent-cpc", "publisher": "MicrosoftWindowsDesktop", "sku": "win10-21h2-ent-cpc-m365-g2", "version": "latest"}}, "networking": {"resourceGroup": "sea-prd-ssv-networking-rg", "subnetName": "azure-virtual-desktop-snet", "virtualNetworkName": "sea-prd-ssv-spoke-vnet"}}]}}