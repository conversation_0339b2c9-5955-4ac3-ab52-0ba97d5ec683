{"p_appGroups": {"value": [{"applicationGroupType": "Desktop", "description": "Desktop Session for RDP - Pooled.", "friendlyName": "Desktop Session for RDP - RDP (Pooled)", "appgroupName": "rdp-desktop", "aadUserGroup": "AZVD Users - RDP - Pooled", "hostpoolName": "rdp-pooled"}, {"applicationGroupType": "RemoteApp", "description": "RemoteApps for RDP - Pooled.", "friendlyName": "RemoteApps for RDP - RDP (Pooled)", "appgroupName": "rdp-remoteapp", "aadUserGroup": "AZVD Users - RDP - Pooled", "hostpoolName": "rdp-pooled"}]}, "p_remoteApps": {"value": [{"applicationName": "remote-desktop", "description": "Remote Desktop Connection", "friendlyName": "Remote Desktop Connection", "filePath": "C:\\Windows\\System32\\mstsc.exe", "hostpoolName": "rdp-pooled"}]}, "p_hostPools": {"value": [{"customRdpProperty": "drivestoredirect:s:;audiomode:i:0;videoplaybackmode:i:1;redirectclipboard:i:0;redirectprinters:i:0;devicestoredirect:s:;redirectcomports:i:0;redirectsmartcards:i:0;usbdevicestoredirect:s:;enablecredsspsupport:i:1;use multimon:i:1;autoreconnection enabled:i:1;bandwidthautodetect:i:1;networkautodetect:i:1;compression:i:1;audiocapturemode:i:1;encode redirected video capture:i:1;redirected video capture encoding quality:i:2;camerastoredirect:s:*;selectedmonitors:s:;maximizetocurrentdisplays:i:1;singlemoninwindowedmode:i:1;screen mode id:i:1;smart sizing:i:1;dynamic resolution:i:1;desktopscalefactor:i:100;targetisaadjoined:i:1;enablerdsaadauth:i:1;redirectwebauthn:i:1;", "description": "Azure Virtual Desktop Host Pool for RDP - Pooled.", "friendlyName": "JERAGM Host Pool - RDP (Pooled)", "hostPoolType": "<PERSON>d", "loadBalancerType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maxSessionLimit": 4, "personalDesktopAssignmentType": "Direct", "preferredAppGroupType": "Desktop", "startVMOnConnect": false, "hostpoolName": "rdp-pooled", "hostpoolNameShort": "azvdrdp", "validationEnvironment": false, "aadUserGroup": "AZVD Users - RDP - Pooled", "fslogixEnabled": false}]}, "p_hosts": {"value": [{"adminUserName": "jeragmadm", "diskType": "Standard_LRS", "hostpoolName": "rdp-pooled", "hostSku": "Standard_D2s_v3", "offer": "office-365", "publisher": "microsoftwindowsdesktop", "sku": "win10-22h2-avd-g2", "version": "latest", "resourceGroup": "uks-prd-ssv-networking-rg", "virtualNetworkName": "uks-prd-ssv-spoke-vnet", "subnetName": "azure-virtual-desktop-snet"}]}, "p_scalingPlans": {"value": [{"enabled": false, "hostpoolName": "rdp-pooled"}]}}