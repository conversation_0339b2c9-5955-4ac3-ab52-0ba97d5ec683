[CmdletBinding()]
param (
    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$appName,

    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$storageAccount,

    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$storageSAS
)

#Set-PSDebug -Trace 2 #Enable to see debug log
$ErrorActionPreference = "Stop" 

Write-Host "Connect to Microsoft Graph API"
Connect-MSIntuneGraph -TenantID $env:tenantId -ClientID $env:servicePrincipalId -ClientSecret $env:servicePrincipalKey

# Application base path
Write-Host "Set options for $appName"
$basePath          = $pwd.Path + "\applications"
$outputFolder      = "$basePath\output"
$detectionRuleFile = "$outputFolder\detectionRule.ps1"
$intunewinFile     = (Get-ChildItem -Filter *.intunewin -Path $outputFolder -Recurse).Name

Write-Host "Show folder content"
Get-ChildItem $outputFolder

# Import parameters from config.yml
Write-Host "Get application config"
$appConfig = Get-Content "$outputFolder\config.yml" | ConvertFrom-Yaml        

$detectionRule = New-IntuneWin32AppDetectionRuleScript -ScriptFile $detectionRuleFile -EnforceSignatureCheck $false -RunAs32Bit $false

Write-Host "Get all apps matching $appName and sort them in descending order, this puts the latest version on top"
$listVersions = Get-IntuneWin32App -DisplayName $appName | Sort-Object -Property displayVersion -Descending
$appVersions  = $listVersions.displayVersion -join ', '
Write-Host "The following versions already exist," $appVersions 

Write-Host "Create $appName Icon"
$iconFile = (Get-ChildItem -Path $outputFolder -Include *.jpg,*.jpeg,*.png -Recurse).Name
$icon     = New-IntuneWin32AppIcon -FilePath "$outputFolder\$iconFile"

$appVersion = $appConfig.appVersion
Write-Host "Upload $appName $appVersion to Intune"

$paramsIntuneWin32App = @{
  FilePath                 = "$outputFolder\$intunewinFile"
  DisplayName              = $appConfig.name
  DetectionRule            = $detectionRule
  Icon                     = $icon
  Description              = $appConfig.description
  Publisher                = $appConfig.publisher
  InstallCommandLine       = $appConfig.commandLine.install
  UninstallCommandLine     = $appConfig.commandLine.uninstall
  AppVersion               = $appConfig.appVersion
  InstallExperience        = $appConfig.installExp
  RestartBehavior          = "suppress"
  CompanyPortalFeaturedApp = $appConfig.featured
}

# Check for duplicate before pushing app to Intune
Write-Host "Get all apps matching $appName and sort them in descending order, this puts the latest version on top"
$listVersions = Get-IntuneWin32App -DisplayName $appName | Sort-Object -Property displayVersion -Descending
$appVersion = $appConfig.appVersion

if ($listVersions.displayVersion -contains $appConfig.appVersion) {
    Write-Output "##vso[task.logissue type=warning] Version $appVersion already exists"
    Write-Host   "##vso[task.complete result=Failed;]DONE"
}

# Test if the file requirementRule.ps1 is actually present
$requirementRuleExists = Test-Path -Path "$outputFolder\requirementRule.ps1" -PathType Leaf

if ($requirementRuleExists) { 
  $requirementRule = New-IntuneWin32AppRequirementRuleScript `
    -StringOutputDataType `
    -ScriptFile "$outputFolder\requirementRule.ps1" `
    -ScriptContext "system" `
    -StringComparisonOperator "equal" `
    -StringValue "Detected" `
    -EnforceSignatureCheck $false
   
  Add-IntuneWin32App @paramsIntuneWin32App -AdditionalRequirementRule $requirementRule
}
else {
  Add-IntuneWin32App @paramsIntuneWin32App
}

