parameters:
  subscriptions : ""
  svcConnection : ""
  location      : ""
  primaryRegion : ""

steps:
- task: AzureCLI@2
  displayName     : Assign Primary Device
  timeoutInMinutes: 15
  inputs:
    addSpnToEnvironment            : true
    azureSubscription              :  ${{ parameters.svcConnection }}
    powerShellErrorActionPreference: stop
    scriptLocation                 : scriptPath
    scriptPath                     : $(System.DefaultWorkingDirectory)/scripts/config_primaryDevice.ps1
    scriptType                     : pscore
    workingDirectory               : $(System.DefaultWorkingDirectory)/scripts
    scriptArguments:
      -location       "${{ parameters.location }}"
      -subscriptions  "${{ parameters.subscriptions }}"
      -primaryRegion  "${{ parameters.primaryRegion }}"
      -deploymentName "deployment-$(Build.BuildId)"
