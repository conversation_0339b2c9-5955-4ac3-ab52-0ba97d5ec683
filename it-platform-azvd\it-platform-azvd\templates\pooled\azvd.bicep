// PARAMETERS

param p_locationShort    string
param p_environmentShort string
param p_tags             object
param p_appGroups        array
param p_hostPool         object
param p_scalingPlan      object
param p_azvdLocation     string
param p_expirationTime   string = dateTimeAdd(utcNow('u'), 'P7D', 'yyyy-MM-dd THH:mm:ss zzzz')
param p_remoteApp        array
param p_primaryRegion    string
param p_location         string
param p_serviceCode      string
//VARIABLES


//RESOURCES

// Create an host pool
resource hostPool 'Microsoft.DesktopVirtualization/hostPools@2022-09-09' = {
    name      : '${p_locationShort}-${p_environmentShort}-${p_serviceCode}-azvd-${p_hostPool.hostpoolName}-vdpool' // Example: azvd-ukw-core-contino-hpl
    location  : p_azvdLocation
    properties: {
        customRdpProperty            : p_hostPool.customRdpProperty
        description                  : p_hostPool.description
        friendlyName                 : p_hostPool.friendlyName
        hostPoolType                 : p_hostPool.hostPoolType
        loadBalancerType             : p_hostPool.loadBalancerType
        maxSessionLimit              : p_hostPool.maxSessionLimit
        preferredAppGroupType        : p_hostPool.preferredAppGroupType
        startVMOnConnect             : p_hostPool.startVMOnConnect
        validationEnvironment        : p_hostPool.validationEnvironment
        personalDesktopAssignmentType: p_hostPool.personalDesktopAssignmentType
        registrationInfo     : {
            expirationTime            : p_expirationTime
            registrationTokenOperation: 'Update'
        }
    }
    tags: union(p_tags, { RBAC_UserGroup: p_hostPool.aadUserGroup })
}

// Create an application group
resource applicationGroup 'Microsoft.DesktopVirtualization/applicationGroups@2022-09-09' = [for appGroup in p_appGroups: {
    name      : '${p_locationShort}-${p_environmentShort}-${p_serviceCode}-azvd-${appGroup.appGroupName}-vdag'  // Example: uks-npr-${p_serviceCode}-azvd-vdag
    location  : p_azvdLocation
    kind      : appGroup.applicationGroupType
    properties: {
        applicationGroupType: appGroup.applicationGroupType
        description         : appGroup.description
        friendlyName        : appGroup.friendlyName
        hostPoolArmPath     : resourceId('Microsoft.DesktopVirtualization/hostpools', '${hostPool.name}')
    }
    tags: union(p_tags, { RBAC_UserGroup: appGroup.aadUserGroup })
}]

// Add applications to the application group for RemoteApps
resource application 'Microsoft.DesktopVirtualization/applicationGroups/applications@2022-09-09' = [for remoteApp in p_remoteApp: if ((empty(remoteApp.applicationName) == false) && (p_location == p_primaryRegion)) {
    parent: applicationGroup[1]
    name  : remoteApp.applicationName
    properties: {
        description         : remoteApp.description
        friendlyName        : remoteApp.friendlyName
        filePath            : remoteApp.filePath
        iconIndex           : 0
        iconPath            : remoteApp.filePath
        commandLineSetting  : 'DoNotAllow'
        showInPortal        : true
        applicationType     : 'InBuilt'
    }
}]

// Create a Scaling Plan

resource scalingPlan 'Microsoft.DesktopVirtualization/scalingPlans@2022-09-09' = if (p_scalingPlan.enabled == true) {
    name: '${p_locationShort}-${p_environmentShort}-${p_serviceCode}-azvd-${p_hostPool.hostpoolName}-vdscaling'
    location  : p_azvdLocation
    properties: {
        timeZone: p_scalingPlan.timeZone
        hostPoolType: 'Pooled'
        friendlyName: 'Scaling Plan for ${p_hostPool.hostpoolName}'
        hostPoolReferences: [
            {
                hostPoolArmPath: resourceId('Microsoft.DesktopVirtualization/hostpools', '${hostPool.name}')
                scalingPlanEnabled: true
            }
        ]
        schedules: p_scalingPlan.schedules
    }
    tags: p_tags
}

// Outputs the registration key for the hostpool to main.bicep
#disable-next-line use-recent-api-versions // Newer API does not work
output o_hostPoolRegKey string = reference(hostPool.id,'2021-01-14-preview').registrationInfo.token
// Outputs the name of the host Pool to main.bicep
output o_hostPoolName         string = hostPool.name
output o_hostPoolNameShort    string = p_hostPool.hostpoolNameShort
output o_applicationGroupName array = [for i in range(0, length(p_appGroups)): {
  name: applicationGroup[i].name
}]

output o_applicationGroupTags array = [for appgroup in p_appGroups: {
  RBAC_UserGroup: appgroup.aadUserGroup
}]
