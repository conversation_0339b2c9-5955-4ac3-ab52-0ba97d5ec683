[CmdletBinding()]
param (
    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$appName,

    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$appVersion
)

#Set-PSDebug -Trace 2 #Enable to see debug log
$ErrorActionPreference = "Stop"

# Import parameters from config.yml
Write-Host "Get application config"
$basePath  = $pwd.Path + "\applications"
$appFolder = "$basePath\$appName"

$appConfig = Get-Content "$appFolder\$appVersion\config.yml" | ConvertFrom-Yaml -Ordered

$assignments = $appConfig.assignments

if ($assignments -ne "none") {
    Write-Host "Connect to Microsoft Graph API"
    Connect-MSIntuneGraph -TenantID $env:tenantId -ClientID $env:servicePrincipalId -ClientSecret $env:servicePrincipalKey

    $appId   = (get-intuneWin32App | Where-Object {$_.DisplayName -eq $appConfig.name -and $_.DisplayVersion -eq $appVersion}).id

    foreach ($assignment in $assignments.GetEnumerator()) {            
        $groupName        = "$($assignment.Name)"
        $assignmentIntent = "$($assignment.Value.intent)"

        Write-Host "Making $appName $appVersion $assignmentIntent for $groupName"

        Write-Host "Get group ID for $groupName"
        $groupId = (az ad group list --filter "(displayName eq '$groupName')" --query "[].id" --output tsv)
        if ($null -eq $groupId) {
            Write-Output "##vso[task.logissue type=warning] No group found with name $groupName"
        }
        else {
            Write-Host "Assigning the group $groupName to $appName $appVersion"
            Add-IntuneWin32AppAssignmentGroup -ID $appId -GroupID $groupId -Intent $assignmentIntent -Notification hideAll -Include
        }
    }
}