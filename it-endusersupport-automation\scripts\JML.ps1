#Requires -Version 5.1
#Requires -Modules ActiveDirectory, JiraPS

<#
.SYNOPSIS
JML (<PERSON><PERSON>, Mover, Leaver) Admin Account Management Script - Enterprise Edition

.DESCRIPTION
This script provides comprehensive admin account lifecycle management for Active Directory 
environments with enterprise-grade security, audit trails, and Jira integration.

The script is organized into modular components while maintaining single-file deployment:
- JML-Configuration: Smart configuration management with auto-generation
- JML-Security: Credential storage, data redaction, and audit trails  
- JML-Logging: Enhanced logging with comprehensive data protection
- JML-ActiveDirectory: Optimized AD operations with caching and performance tuning
- JML-Jira: Modern Jira integration with ADF formatting and retry logic
- JML-Email: Robust email notifications with retry mechanisms
- JML-Utilities: Common helper functions and validation

Key Features:
- Zero-configuration deployment with intelligent defaults
- Comprehensive data redaction and security controls
- Modern Jira integration with rich formatting
- Performance-optimized AD operations with caching
- Robust error handling with exponential backoff retry
- Enterprise-grade audit trails and compliance logging

.PARAMETER ConfigPath
Path to the configuration file. Auto-generates if not found.

.PARAMETER LogLevel
Logging level: DEBUG, INFO, WARNING, ERROR, CRITICAL. Defaults to INFO.

.PARAMETER SkipJiraIntegration
Switch to skip Jira integration for testing or offline scenarios.

.PARAMETER ModularMode
Switch to enable modular mode (loads separate .psm1 files if available).

.EXAMPLE
.\JML.ps1
Runs with intelligent defaults and auto-configuration.

.EXAMPLE
.\JML.ps1 -LogLevel DEBUG -SkipJiraIntegration
Runs in debug mode without Jira integration.

.EXAMPLE
.\JML.ps1 -ConfigPath "C:\Config\Production.psd1" -ModularMode
Runs with custom config and modular architecture.

.NOTES
Script Name:    JML.ps1 (Joiner, Mover, Leaver)
Version:        3.0 - Modular Enterprise Edition
Author:         Emmanuel Akinjomo
Creation Date:  2025-01-09
Last Modified:  2025-01-09
Architecture:   Hybrid Single-File/Modular Design
Security Level: Enterprise with comprehensive data protection

Module Structure:
- JML-Configuration: Configuration management and auto-generation
- JML-Security: Security functions, credential storage, data protection
- JML-Logging: Enhanced logging system with audit trails
- JML-ActiveDirectory: AD operations with performance optimization
- JML-Jira: Modern Jira integration with rich formatting
- JML-Email: Email notifications with retry logic
- JML-Utilities: Common utilities and helper functions

Required Permissions:
- Active Directory: User creation/modification rights in target OUs
- SMTP: Send email permissions (if email notifications enabled)
- File System: Read/Write access to log directory
- Jira: API access with comment and attachment permissions

Dependencies (Auto-installed):
- PowerShell 5.1 or higher
- ActiveDirectory module
- JiraPS module
- Microsoft.PowerShell.SecretManagement module (recommended)
- Microsoft.PowerShell.SecretStore module (recommended)

Compliance:
- SOX audit trail requirements
- GDPR data protection (automatic PII redaction)
- Enterprise security standards
- Change management integration via Jira
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigPath,
    
    [Parameter(Mandatory = $false)]
    [ValidateSet('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')]
    [string]$LogLevel = 'INFO',
    
    [Parameter(Mandatory = $false)]
    [switch]$SkipJiraIntegration,
    
    [Parameter(Mandatory = $false)]
    [switch]$ModularMode
)

#region JML-Configuration Module

<#
.SYNOPSIS
JML Configuration Management Module

.DESCRIPTION
Provides intelligent configuration management with auto-generation capabilities,
environment detection, and seamless fallback to defaults. Supports both
single-file and modular deployment modes.

Module Dependencies: None (Core module)
Module Version: 3.0
Module Exports: Initialize-SmartConfiguration, New-DefaultConfiguration, Save-ConfigurationFile

.NOTES
This module enables zero-configuration deployment while maintaining full customization capabilities.
#>

# Global script configuration and state
$script:Config = $null
$script:CurrentLogPath = $null
$script:DefaultConfig = $null
$script:SecretManagementAvailable = $false
$script:ADUserCache = @{}
$script:ExecutionContext = @{
    StartTime = Get-Date
    ExecutingUser = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name
    ComputerName = $env:COMPUTERNAME
    PowerShellVersion = $PSVersionTable.PSVersion.ToString()
    ScriptPath = $PSCommandPath
    ScriptDirectory = $PSScriptRoot
    ModularMode = $ModularMode.IsPresent
}

<#
.SYNOPSIS
Creates a default configuration for the script when no config file exists.

.DESCRIPTION
Generates intelligent defaults based on the environment and common organizational
patterns. This ensures the script works out-of-the-box without requiring
manual configuration.

.OUTPUTS
Hashtable containing the default configuration structure.

.EXAMPLE
$defaultConfig = New-DefaultConfiguration

.NOTES
This function enables zero-configuration deployment while maintaining
full customization capabilities.
#>
function New-DefaultConfiguration {
    [CmdletBinding()]
    [OutputType([hashtable])]
    param()
    
    try {
        Write-Verbose "Generating default configuration based on environment detection"
        
        # Detect domain from current user context
        $currentDomain = try {
            ([System.DirectoryServices.ActiveDirectory.Domain]::GetCurrentDomain()).Name
        } catch {
            "domain.com"  # Fallback
        }
        
        # Detect common log directories
        $logDirectory = @(
            "C:\Temp\Scripts\Desktop Support\Logs",
            "C:\Logs\AdminScript",
            "$env:TEMP\AdminScript\Logs"
        ) | Where-Object { 
            try { 
                Test-Path (Split-Path $_ -Parent) -PathType Container 
            } catch { 
                $false 
            } 
        } | Select-Object -First 1
        
        if (-not $logDirectory) {
            $logDirectory = "$env:TEMP\AdminScript\Logs"
        }
        
        # Generate intelligent defaults
        $defaultConfig = @{
            # Script metadata
            ConfigVersion = "3.0"
            GeneratedOn = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            GeneratedBy = $script:ExecutionContext.ExecutingUser
            
            # General settings with environment detection
            ScriptSettings = @{
                DefaultDomain = $currentDomain
                MaxRetryAttempts = 3
                BaseRetryDelay = 2
                MaxRetryDelay = 30
                OperationTimeout = 300
                ShowProgress = $true
                EnableAuditLogging = $true
                AutoCreateMissingOUs = $false
                ModularMode = $script:ExecutionContext.ModularMode
            }
            
            # Logging configuration
            Logging = @{
                LogDirectory = $logDirectory
                LogRetentionDays = 30
                MaxLogFileSizeMB = 10
                EnableLogRotation = $true
                FileLogLevels = @('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')
                ConsoleLogLevels = @('INFO', 'WARNING', 'ERROR', 'CRITICAL')
                EnableDataRedaction = $true
                DataRedaction = @{
                    RedactUPNs = $true
                    RedactEmailAddresses = $true
                    RedactDistinguishedNames = $true
                    RedactServerNames = $true
                    HashAlgorithm = "SHA256"
                    HashSalt = "JML_AdminScript_$(Get-Random -Minimum 1000 -Maximum 9999)"
                }
            }
        }
        
        Write-Verbose "Default configuration generated successfully"
        return $defaultConfig
    }
    catch {
        Write-Warning "Failed to generate default configuration: $($_.Exception.Message)"
        throw
    }
}

<#
.SYNOPSIS
Initializes configuration with intelligent defaults and auto-generation capabilities.

.DESCRIPTION
Loads configuration from file if available, or generates intelligent defaults based on
environment detection. Creates missing configuration files automatically and provides
self-healing capabilities for corrupted or incomplete configurations.

.PARAMETER ConfigPath
Optional path to the configuration file (.psd1 format). If not provided or file doesn't exist,
intelligent defaults will be generated and optionally saved.

.PARAMETER CreateConfigFile
Switch to automatically create a configuration file with detected defaults.

.EXAMPLE
Initialize-SmartConfiguration
Loads configuration with intelligent defaults.

.EXAMPLE
Initialize-SmartConfiguration -ConfigPath ".\MyConfig.psd1" -CreateConfigFile
Loads from specific path or creates it with defaults.

.NOTES
This function enables zero-configuration deployment while maintaining full customization capabilities.
Security: Only loads data files, never executable code.
#>
function Initialize-SmartConfiguration {
    [CmdletBinding()]
    [OutputType([bool])]
    param(
        [Parameter(Mandatory = $false)]
        [string]$ConfigPath,

        [Parameter(Mandatory = $false)]
        [switch]$CreateConfigFile
    )

    try {
        Write-Progress -Activity "Initializing JML Script" -Status "Setting up configuration..." -PercentComplete 10

        # Generate default configuration first
        $script:DefaultConfig = New-DefaultConfiguration

        # Determine config file path
        if (-not $ConfigPath) {
            $ConfigPath = Join-Path $script:ExecutionContext.ScriptDirectory "JMLConfig.psd1"
        }

        $configLoaded = $false
        $config = $null

        # Try to load existing configuration
        if (Test-Path $ConfigPath -PathType Leaf) {
            try {
                Write-Verbose "Loading configuration from: $ConfigPath"
                $config = Import-PowerShellDataFile -Path $ConfigPath -ErrorAction Stop

                # Validate and merge with defaults
                $config = Merge-ConfigurationWithDefaults -UserConfig $config -DefaultConfig $script:DefaultConfig
                $configLoaded = $true

                Write-Host "Configuration loaded from: $ConfigPath" -ForegroundColor Green
            }
            catch {
                Write-Warning "Failed to load configuration file '$ConfigPath': $($_.Exception.Message)"
                Write-Host "Using intelligent defaults instead." -ForegroundColor Yellow
            }
        }

        # Use defaults if no config loaded
        if (-not $configLoaded) {
            Write-Host "No configuration file found. Using intelligent defaults based on environment detection." -ForegroundColor Cyan
            $config = $script:DefaultConfig

            # Offer to create configuration file
            if ($CreateConfigFile -or (Test-InteractiveSession)) {
                $createFile = if ($CreateConfigFile) {
                    $true
                } else {
                    $response = Read-Host "Would you like to create a configuration file for future customization? (Y/N)"
                    $response -eq 'Y' -or $response -eq 'y'
                }

                if ($createFile) {
                    try {
                        Save-ConfigurationFile -Config $config -Path $ConfigPath
                        Write-Host "Configuration file created: $ConfigPath" -ForegroundColor Green
                        Write-Host "You can customize this file for your environment." -ForegroundColor Cyan
                    }
                    catch {
                        Write-Warning "Failed to create configuration file: $($_.Exception.Message)"
                    }
                }
            }
        }

        # Expand environment variables in paths
        if ($config.Logging.LogDirectory) {
            $config.Logging.LogDirectory = [System.Environment]::ExpandEnvironmentVariables($config.Logging.LogDirectory)
        }

        # Set global configuration
        $script:Config = $config

        Write-Progress -Activity "Initializing JML Script" -Status "Configuration ready" -PercentComplete 20
        Write-Verbose "Configuration initialized successfully"
        return $true
    }
    catch {
        Write-Error "Failed to initialize configuration: $($_.Exception.Message)"
        throw
    }
}

<#
.SYNOPSIS
Merges user configuration with intelligent defaults.

.DESCRIPTION
Combines user-provided configuration with generated defaults, ensuring all required
sections exist while preserving user customizations.

.PARAMETER UserConfig
User-provided configuration hashtable.

.PARAMETER DefaultConfig
Default configuration hashtable.

.OUTPUTS
Hashtable containing merged configuration.

.EXAMPLE
$merged = Merge-ConfigurationWithDefaults -UserConfig $userConfig -DefaultConfig $defaults

.NOTES
Performs deep merge to preserve nested user settings while filling gaps with defaults.
#>
function Merge-ConfigurationWithDefaults {
    [CmdletBinding()]
    [OutputType([hashtable])]
    param(
        [Parameter(Mandatory = $true)]
        [hashtable]$UserConfig,

        [Parameter(Mandatory = $true)]
        [hashtable]$DefaultConfig
    )

    try {
        $merged = $DefaultConfig.Clone()

        foreach ($section in $UserConfig.Keys) {
            if ($merged.ContainsKey($section)) {
                if ($UserConfig[$section] -is [hashtable] -and $merged[$section] -is [hashtable]) {
                    # Deep merge for hashtable sections
                    foreach ($key in $UserConfig[$section].Keys) {
                        $merged[$section][$key] = $UserConfig[$section][$key]
                    }
                } else {
                    # Direct replacement for non-hashtable values
                    $merged[$section] = $UserConfig[$section]
                }
            } else {
                # Add new sections from user config
                $merged[$section] = $UserConfig[$section]
            }
        }

        return $merged
    }
    catch {
        Write-Warning "Failed to merge configurations, using defaults: $($_.Exception.Message)"
        return $DefaultConfig
    }
}

<#
.SYNOPSIS
Saves configuration to a PowerShell data file.

.DESCRIPTION
Exports configuration hashtable to a properly formatted .psd1 file with
comments and structure for easy customization.

.PARAMETER Config
Configuration hashtable to save.

.PARAMETER Path
Path where to save the configuration file.

.EXAMPLE
Save-ConfigurationFile -Config $config -Path ".\MyConfig.psd1"

.NOTES
Creates human-readable configuration files with helpful comments.
#>
function Save-ConfigurationFile {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [hashtable]$Config,

        [Parameter(Mandatory = $true)]
        [string]$Path
    )

    try {
        $configContent = @"
# JML Admin Account Management Script Configuration
# Generated on: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
# Version: 3.0 - Modular Enterprise Edition
#
# This file contains configuration settings for the JML Admin Account Management Script.
# Customize the values below to match your environment.
#
# IMPORTANT: This file contains only data, no executable code.
# It is safe to edit and version control.

@{
$(ConvertTo-ConfigString -Object $Config -IndentLevel 1)
}
"@

        # Ensure directory exists
        $directory = Split-Path $Path -Parent
        if ($directory -and -not (Test-Path $directory)) {
            New-Item -ItemType Directory -Path $directory -Force | Out-Null
        }

        # Save configuration file
        Set-Content -Path $Path -Value $configContent -Encoding UTF8 -Force

        Write-Verbose "Configuration saved to: $Path"
    }
    catch {
        Write-Error "Failed to save configuration file: $($_.Exception.Message)"
        throw
    }
}

<#
.SYNOPSIS
Tests if the current session is interactive.

.DESCRIPTION
Determines if the PowerShell session is interactive (user can respond to prompts)
or non-interactive (automated/scheduled execution).

.OUTPUTS
Boolean indicating if session is interactive.

.EXAMPLE
if (Test-InteractiveSession) { $response = Read-Host "Continue?" }

.NOTES
Used to determine when to prompt users for configuration options.
#>
function Test-InteractiveSession {
    [CmdletBinding()]
    [OutputType([bool])]
    param()

    try {
        # Check if running in ISE, VS Code, or other interactive environments
        $isInteractive = [Environment]::UserInteractive -and
                        -not [Console]::IsInputRedirected -and
                        -not [Console]::IsOutputRedirected

        return $isInteractive
    }
    catch {
        # Default to non-interactive if detection fails
        return $false
    }
}

<#
.SYNOPSIS
Converts hashtable to formatted PowerShell data string.

.DESCRIPTION
Recursively converts hashtable to properly formatted PowerShell data file content
with appropriate indentation and comments.

.PARAMETER Object
Object to convert (hashtable, array, or primitive).

.PARAMETER IndentLevel
Current indentation level for formatting.

.OUTPUTS
String containing formatted PowerShell data representation.

.EXAMPLE
$formatted = ConvertTo-ConfigString -Object $hashtable -IndentLevel 1

.NOTES
Internal helper function for configuration file generation.
#>
function ConvertTo-ConfigString {
    [CmdletBinding()]
    [OutputType([string])]
    param(
        [Parameter(Mandatory = $true)]
        $Object,

        [Parameter(Mandatory = $false)]
        [int]$IndentLevel = 0
    )

    $indent = "    " * $IndentLevel
    $result = ""

    if ($Object -is [hashtable]) {
        foreach ($key in $Object.Keys | Sort-Object) {
            $value = $Object[$key]
            $result += "$indent$key = "

            if ($value -is [hashtable]) {
                $result += "@{`n"
                $result += ConvertTo-ConfigString -Object $value -IndentLevel ($IndentLevel + 1)
                $result += "$indent}`n"
            }
            elseif ($value -is [array]) {
                $result += "@("
                $arrayItems = $value | ForEach-Object {
                    if ($_ -is [string]) { "'$_'" } else { $_ }
                }
                $result += $arrayItems -join ", "
                $result += ")`n"
            }
            elseif ($value -is [string]) {
                $result += "'$value'`n"
            }
            elseif ($value -is [bool]) {
                $result += "`$$value`n"
            }
            else {
                $result += "$value`n"
            }
        }
    }

    return $result
}

#endregion

#region JML-Security Module

<#
.SYNOPSIS
JML Security Management Module

.DESCRIPTION
Provides comprehensive security functions including credential storage, data redaction,
input validation, and audit trail management. Implements enterprise-grade security
controls with multiple fallback mechanisms.

Module Dependencies: JML-Configuration
Module Version: 3.0
Module Exports: Get-SecureCredential, Protect-SensitiveData, Get-StringHash, Initialize-SecureCredentialStorage

.NOTES
This module implements defense-in-depth security principles with comprehensive data protection.
#>

<#
.SYNOPSIS
Retrieves credentials securely using multiple storage methods with fallback.

.DESCRIPTION
Attempts to retrieve credentials using PowerShell SecretManagement, Windows Credential Manager,
encrypted files, or secure prompts as fallback. Implements comprehensive error handling
and security best practices.

.PARAMETER CredentialName
The name/identifier for the credential to retrieve.

.PARAMETER Purpose
Description of what the credential will be used for (for user prompts).

.PARAMETER AllowPrompt
Switch to allow prompting user if credential not found in storage.

.OUTPUTS
PSCredential object or string depending on credential type.

.EXAMPLE
$cred = Get-SecureCredential -CredentialName "JiraApiToken" -Purpose "Jira API access" -AllowPrompt

.NOTES
Implements secure credential retrieval with multiple fallback mechanisms.
#>
function Get-SecureCredential {
    [CmdletBinding()]
    [OutputType([PSCredential], [string])]
    param(
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$CredentialName,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$Purpose,

        [Parameter(Mandatory = $false)]
        [switch]$AllowPrompt
    )

    try {
        Write-Verbose "Attempting to retrieve credential: $CredentialName"

        # Method 1: PowerShell SecretManagement (preferred)
        if ($script:SecretManagementAvailable) {
            try {
                $vaultName = if ($script:Config) {
                    $script:Config.Security.CredentialStorage.SecretVaultName
                } else {
                    "AdminAccountVault"
                }

                $secret = Get-Secret -Name $CredentialName -Vault $vaultName -ErrorAction SilentlyContinue
                if ($secret) {
                    Write-Verbose "Retrieved credential from SecretManagement vault"
                    return $secret
                }
            }
            catch {
                Write-Verbose "SecretManagement retrieval failed: $($_.Exception.Message)"
            }
        }

        # Method 2: Windows Credential Manager
        try {
            $storedCred = Get-StoredCredential -Target $CredentialName -ErrorAction SilentlyContinue
            if ($storedCred) {
                Write-Verbose "Retrieved credential from Windows Credential Manager"
                return $storedCred
            }
        }
        catch {
            Write-Verbose "Credential Manager retrieval failed: $($_.Exception.Message)"
        }

        # Method 3: Encrypted file storage
        try {
            $encryptedPath = if ($script:Config) {
                $script:Config.Security.CredentialStorage.EncryptedFileSettings.FilePath
            } else {
                ".\SecureCredentials.xml"
            }

            if (Test-Path $encryptedPath) {
                $encryptedData = Import-Clixml -Path $encryptedPath -ErrorAction SilentlyContinue
                if ($encryptedData -and $encryptedData.ContainsKey($CredentialName)) {
                    Write-Verbose "Retrieved credential from encrypted file"
                    return $encryptedData[$CredentialName]
                }
            }
        }
        catch {
            Write-Verbose "Encrypted file retrieval failed: $($_.Exception.Message)"
        }

        # Method 4: Secure prompt (if allowed)
        if ($AllowPrompt -and (Test-InteractiveSession)) {
            Write-Host "Credential '$CredentialName' not found in secure storage." -ForegroundColor Yellow
            Write-Host "Purpose: $Purpose" -ForegroundColor Cyan

            $credential = Get-Credential -Message "Enter credentials for: $Purpose" -UserName $CredentialName
            if ($credential) {
                Write-Verbose "Credential obtained via secure prompt"

                # Offer to save for future use
                $save = Read-Host "Would you like to save this credential for future use? (Y/N)"
                if ($save -eq 'Y' -or $save -eq 'y') {
                    try {
                        Set-SecureCredential -CredentialName $CredentialName -Credential $credential
                        Write-Host "Credential saved securely." -ForegroundColor Green
                    }
                    catch {
                        Write-Warning "Failed to save credential: $($_.Exception.Message)"
                    }
                }

                return $credential
            }
        }

        Write-Warning "Could not retrieve credential '$CredentialName' from any source"
        return $null
    }
    catch {
        Write-Error "Failed to retrieve secure credential '$CredentialName': $($_.Exception.Message)"
        return $null
    }
}

<#
.SYNOPSIS
Stores credentials securely using the best available method.

.DESCRIPTION
Attempts to store credentials using PowerShell SecretManagement, Windows Credential Manager,
or encrypted file storage as fallback. Implements comprehensive error handling.

.PARAMETER CredentialName
The name/identifier for the credential to store.

.PARAMETER Credential
The credential object to store securely.

.OUTPUTS
Boolean indicating success of storage operation.

.EXAMPLE
$success = Set-SecureCredential -CredentialName "JiraApiToken" -Credential $cred

.NOTES
Implements secure credential storage with multiple fallback mechanisms.
#>
function Set-SecureCredential {
    [CmdletBinding()]
    [OutputType([bool])]
    param(
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$CredentialName,

        [Parameter(Mandatory = $true)]
        [ValidateNotNull()]
        $Credential
    )

    try {
        Write-Verbose "Attempting to store credential: $CredentialName"

        # Method 1: PowerShell SecretManagement (preferred)
        if ($script:SecretManagementAvailable) {
            try {
                $vaultName = if ($script:Config) {
                    $script:Config.Security.CredentialStorage.SecretVaultName
                } else {
                    "AdminAccountVault"
                }

                Set-Secret -Name $CredentialName -Secret $Credential -Vault $vaultName -ErrorAction Stop
                Write-Verbose "Stored credential in SecretManagement vault"
                return $true
            }
            catch {
                Write-Verbose "SecretManagement storage failed: $($_.Exception.Message)"
            }
        }

        # Method 2: Windows Credential Manager
        try {
            if ($Credential -is [PSCredential]) {
                New-StoredCredential -Target $CredentialName -UserName $Credential.UserName -Password $Credential.Password -ErrorAction Stop
            } else {
                # For string tokens, create a generic credential
                $secureString = ConvertTo-SecureString -String $Credential -AsPlainText -Force
                $tempCred = New-Object PSCredential($CredentialName, $secureString)
                New-StoredCredential -Target $CredentialName -UserName $tempCred.UserName -Password $tempCred.Password -ErrorAction Stop
            }
            Write-Verbose "Stored credential in Windows Credential Manager"
            return $true
        }
        catch {
            Write-Verbose "Credential Manager storage failed: $($_.Exception.Message)"
        }

        # Method 3: Encrypted file storage
        try {
            $encryptedPath = if ($script:Config) {
                $script:Config.Security.CredentialStorage.EncryptedFileSettings.FilePath
            } else {
                ".\SecureCredentials.xml"
            }

            # Load existing data or create new
            $encryptedData = if (Test-Path $encryptedPath) {
                Import-Clixml -Path $encryptedPath -ErrorAction SilentlyContinue
            } else {
                @{}
            }

            if (-not $encryptedData) { $encryptedData = @{} }

            # Store the credential
            $encryptedData[$CredentialName] = $Credential

            # Ensure directory exists
            $directory = Split-Path $encryptedPath -Parent
            if ($directory -and -not (Test-Path $directory)) {
                New-Item -ItemType Directory -Path $directory -Force | Out-Null
            }

            # Save encrypted data
            Export-Clixml -Path $encryptedPath -InputObject $encryptedData -Force
            Write-Verbose "Stored credential in encrypted file"
            return $true
        }
        catch {
            Write-Verbose "Encrypted file storage failed: $($_.Exception.Message)"
        }

        Write-Warning "Failed to store credential '$CredentialName' using any method"
        return $false
    }
    catch {
        Write-Error "Failed to store secure credential '$CredentialName': $($_.Exception.Message)"
        return $false
    }
}

<#
.SYNOPSIS
Protects sensitive data by applying comprehensive redaction rules.

.DESCRIPTION
Applies configurable data redaction rules to protect sensitive information in logs,
Jira comments, and other outputs. Supports UPN masking, DN hashing, and server name redaction.

.PARAMETER InputString
The string containing potentially sensitive data to protect.

.PARAMETER RedactionType
Type of redaction to apply: UPN, Email, DN, Server, or All.

.PARAMETER PreserveFormat
Switch to maintain original string format while redacting sensitive parts.

.OUTPUTS
String with sensitive data redacted according to configuration.

.EXAMPLE
$protected = Protect-SensitiveData -InputString "<EMAIL>" -RedactionType "UPN"

.NOTES
Implements GDPR-compliant data protection with configurable redaction rules.
#>
function Protect-SensitiveData {
    [CmdletBinding()]
    [OutputType([string])]
    param(
        [Parameter(Mandatory = $true)]
        [AllowEmptyString()]
        [string]$InputString,

        [Parameter(Mandatory = $false)]
        [ValidateSet('UPN', 'Email', 'DN', 'Server', 'All')]
        [string]$RedactionType = 'All',

        [Parameter(Mandatory = $false)]
        [switch]$PreserveFormat
    )

    if ([string]::IsNullOrEmpty($InputString)) {
        return $InputString
    }

    try {
        $result = $InputString
        $redactionEnabled = if ($script:Config) {
            $script:Config.Logging.EnableDataRedaction
        } else {
            $true
        }

        if (-not $redactionEnabled) {
            return $result
        }

        $redactionConfig = if ($script:Config) {
            $script:Config.Logging.DataRedaction
        } else {
            @{
                RedactUPNs = $true
                RedactEmailAddresses = $true
                RedactDistinguishedNames = $true
                RedactServerNames = $true
            }
        }

        # UPN/Email redaction
        if (($RedactionType -eq 'UPN' -or $RedactionType -eq 'Email' -or $RedactionType -eq 'All') -and
            ($redactionConfig.RedactUPNs -or $redactionConfig.RedactEmailAddresses)) {

            # Pattern: <EMAIL> -> user***@domain.com
            $result = $result -replace '([a-zA-Z0-9._-]+)@([a-zA-Z0-9.-]+)', '$1***@$2'
        }

        # Distinguished Name redaction
        if (($RedactionType -eq 'DN' -or $RedactionType -eq 'All') -and $redactionConfig.RedactDistinguishedNames) {
            # Hash DN components while preserving structure
            $result = $result -replace 'CN=([^,]+)', {
                "CN=" + (Get-StringHash -InputString $_.Groups[1].Value).Substring(0, 8) + "***"
            }
        }

        # Server name redaction
        if (($RedactionType -eq 'Server' -or $RedactionType -eq 'All') -and $redactionConfig.RedactServerNames) {
            # Replace server names and URLs
            $result = $result -replace 'https?://[a-zA-Z0-9.-]+', '[SERVER]'
            $result = $result -replace '\\\\[a-zA-Z0-9.-]+', '[SERVER]'
            $result = $result -replace '[a-zA-Z0-9.-]+\.atlassian\.net', '[JIRA-INSTANCE]'
            $result = $result -replace 'smtp\.[a-zA-Z0-9.-]+', '[SMTP-SERVER]'
        }

        return $result
    }
    catch {
        Write-Warning "Data redaction failed: $($_.Exception.Message)"
        return "[REDACTED]"
    }
}

<#
.SYNOPSIS
Generates secure hash of input string with optional salt.

.DESCRIPTION
Creates SHA256 hash of input string with configurable salt for secure
data protection and audit trail generation.

.PARAMETER InputString
The string to hash.

.PARAMETER UseSalt
Switch to include configured salt in hash generation.

.OUTPUTS
String containing the hexadecimal hash value.

.EXAMPLE
$hash = Get-StringHash -InputString "sensitive data" -UseSalt

.NOTES
Used for secure audit trails and data protection in compliance scenarios.
#>
function Get-StringHash {
    [CmdletBinding()]
    [OutputType([string])]
    param(
        [Parameter(Mandatory = $true)]
        [AllowEmptyString()]
        [string]$InputString,

        [Parameter(Mandatory = $false)]
        [switch]$UseSalt
    )

    try {
        if ([string]::IsNullOrEmpty($InputString)) {
            return ""
        }

        $algorithm = if ($script:Config) {
            $script:Config.Logging.DataRedaction.HashAlgorithm
        } else {
            "SHA256"
        }

        $salt = if ($UseSalt -and $script:Config) {
            $script:Config.Logging.DataRedaction.HashSalt
        } else {
            ""
        }

        $stringToHash = $InputString + $salt
        $hasher = [System.Security.Cryptography.HashAlgorithm]::Create($algorithm)
        $bytes = [System.Text.Encoding]::UTF8.GetBytes($stringToHash)
        $hashBytes = $hasher.ComputeHash($bytes)

        return [System.BitConverter]::ToString($hashBytes) -replace '-', ''
    }
    catch {
        Write-Warning "Hash generation failed: $($_.Exception.Message)"
        return "HASH_ERROR"
    }
    finally {
        if ($hasher) {
            $hasher.Dispose()
        }
    }
}

<#
.SYNOPSIS
Initializes secure credential storage system.

.DESCRIPTION
Sets up PowerShell SecretManagement vault and validates credential storage
capabilities. Provides fallback configuration for environments without
SecretManagement module.

.OUTPUTS
Boolean indicating successful initialization.

.EXAMPLE
$initialized = Initialize-SecureCredentialStorage

.NOTES
Called during script initialization to set up secure credential infrastructure.
#>
function Initialize-SecureCredentialStorage {
    [CmdletBinding()]
    [OutputType([bool])]
    param()

    try {
        Write-Verbose "Initializing secure credential storage"

        # Check for SecretManagement module
        $secretMgmtModule = Get-Module -Name Microsoft.PowerShell.SecretManagement -ListAvailable -ErrorAction SilentlyContinue
        if ($secretMgmtModule) {
            try {
                Import-Module Microsoft.PowerShell.SecretManagement -Force -ErrorAction Stop

                $vaultName = if ($script:Config) {
                    $script:Config.Security.CredentialStorage.SecretVaultName
                } else {
                    "AdminAccountVault"
                }

                # Check if vault exists, create if not
                $vault = Get-SecretVault -Name $vaultName -ErrorAction SilentlyContinue
                if (-not $vault) {
                    # Try to create a SecretStore vault
                    $secretStoreModule = Get-Module -Name Microsoft.PowerShell.SecretStore -ListAvailable -ErrorAction SilentlyContinue
                    if ($secretStoreModule) {
                        Import-Module Microsoft.PowerShell.SecretStore -Force -ErrorAction Stop
                        Register-SecretVault -Name $vaultName -ModuleName Microsoft.PowerShell.SecretStore -DefaultVault -ErrorAction Stop
                        Write-Verbose "Created SecretStore vault: $vaultName"
                    }
                }

                $script:SecretManagementAvailable = $true
                Write-Verbose "SecretManagement initialized successfully"
                return $true
            }
            catch {
                Write-Verbose "SecretManagement initialization failed: $($_.Exception.Message)"
                $script:SecretManagementAvailable = $false
            }
        } else {
            Write-Verbose "SecretManagement module not available"
            $script:SecretManagementAvailable = $false
        }

        # Fallback to other methods
        Write-Verbose "Using fallback credential storage methods"
        return $true
    }
    catch {
        Write-Warning "Failed to initialize secure credential storage: $($_.Exception.Message)"
        return $false
    }
}

#endregion

#region JML-Logging Module

<#
.SYNOPSIS
JML Enhanced Logging Module

.DESCRIPTION
Provides comprehensive logging capabilities with data redaction, audit trails,
log rotation, and performance monitoring. Implements enterprise-grade logging
standards with configurable output levels and formats.

Module Dependencies: JML-Security, JML-Configuration
Module Version: 3.0
Module Exports: Write-SecureLog, Initialize-SecureLogging, Start-LogRotation, Write-AuditTrail

.NOTES
This module implements enterprise logging standards with comprehensive data protection.
#>

<#
.SYNOPSIS
Writes log entries with automatic data redaction and audit trail support.

.DESCRIPTION
Enhanced logging function that automatically redacts sensitive data, supports
multiple output targets, and maintains comprehensive audit trails for compliance.

.PARAMETER Message
The log message to write.

.PARAMETER LogLevel
The severity level: DEBUG, INFO, WARNING, ERROR, CRITICAL.

.PARAMETER AuditTrail
Optional hashtable containing audit trail data.

.PARAMETER Category
Optional category for log organization.

.PARAMETER WriteToConsole
Switch to force console output regardless of configuration.

.EXAMPLE
Write-SecureLog -Message "User account created" -LogLevel "INFO" -AuditTrail @{UserUPN="<EMAIL>"}

.NOTES
Automatically applies data redaction based on configuration settings.
#>
function Write-SecureLog {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$Message,

        [Parameter(Mandatory = $false)]
        [ValidateSet('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')]
        [string]$LogLevel = 'INFO',

        [Parameter(Mandatory = $false)]
        [hashtable]$AuditTrail,

        [Parameter(Mandatory = $false)]
        [string]$Category = 'General',

        [Parameter(Mandatory = $false)]
        [switch]$WriteToConsole
    )

    try {
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss.fff"
        $user = $script:ExecutionContext.ExecutingUser
        $computer = $script:ExecutionContext.ComputerName

        # Apply data redaction to message
        $redactedMessage = Protect-SensitiveData -InputString $Message -RedactionType 'All'

        # Build log entry
        $logEntry = @{
            Timestamp = $timestamp
            Level = $LogLevel
            Category = $Category
            User = $user
            Computer = $computer
            Message = $redactedMessage
            ProcessId = $PID
            ThreadId = [System.Threading.Thread]::CurrentThread.ManagedThreadId
        }

        # Add audit trail if provided
        if ($AuditTrail) {
            $redactedAuditTrail = @{}
            foreach ($key in $AuditTrail.Keys) {
                $redactedAuditTrail[$key] = Protect-SensitiveData -InputString $AuditTrail[$key].ToString() -RedactionType 'All'
            }
            $logEntry.AuditTrail = $redactedAuditTrail
        }

        # Format log entry for file output
        $fileLogEntry = "$timestamp [$LogLevel] [$Category] [$user@$computer] $redactedMessage"
        if ($AuditTrail) {
            $auditString = ($redactedAuditTrail.GetEnumerator() | ForEach-Object { "$($_.Key)=$($_.Value)" }) -join "; "
            $fileLogEntry += " | Audit: $auditString"
        }

        # Write to file if configured
        $fileLogLevels = if ($script:Config) {
            $script:Config.Logging.FileLogLevels
        } else {
            @('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')
        }

        if ($LogLevel -in $fileLogLevels -and $script:CurrentLogPath) {
            try {
                Add-Content -Path $script:CurrentLogPath -Value $fileLogEntry -Encoding UTF8 -ErrorAction Stop
            }
            catch {
                Write-Warning "Failed to write to log file: $($_.Exception.Message)"
            }
        }

        # Write to console if configured or forced
        $consoleLogLevels = if ($script:Config) {
            $script:Config.Logging.ConsoleLogLevels
        } else {
            @('INFO', 'WARNING', 'ERROR', 'CRITICAL')
        }

        if ($WriteToConsole -or ($LogLevel -in $consoleLogLevels)) {
            $consoleColors = if ($script:Config) {
                $script:Config.UserExperience.ConsoleOutput.Colors
            } else {
                @{
                    DEBUG = 'Gray'
                    INFO = 'Cyan'
                    WARNING = 'Yellow'
                    ERROR = 'Red'
                    CRITICAL = 'Magenta'
                }
            }

            $color = $consoleColors[$LogLevel]
            if (-not $color) { $color = 'White' }

            $consoleMessage = "[$LogLevel] $redactedMessage"
            Write-Host $consoleMessage -ForegroundColor $color
        }

        # Store in memory for potential retrieval
        if (-not $script:LogEntries) {
            $script:LogEntries = @()
        }
        $script:LogEntries += $logEntry

        # Maintain memory log size
        if ($script:LogEntries.Count -gt 1000) {
            $script:LogEntries = $script:LogEntries[-500..-1]
        }

    }
    catch {
        Write-Warning "Logging failed: $($_.Exception.Message)"
        # Fallback to basic Write-Host
        Write-Host "[$LogLevel] $Message" -ForegroundColor Red
    }
}

<#
.SYNOPSIS
Initializes the secure logging system with rotation and retention.

.DESCRIPTION
Sets up log file paths, creates directories, configures log rotation,
and validates logging permissions. Implements enterprise logging standards.

.PARAMETER LogDirectory
Optional override for log directory path.

.PARAMETER LogFileName
Optional override for log file name.

.OUTPUTS
Boolean indicating successful initialization.

.EXAMPLE
$initialized = Initialize-SecureLogging -LogDirectory "C:\Logs\JML"

.NOTES
Called during script initialization to set up logging infrastructure.
#>
function Initialize-SecureLogging {
    [CmdletBinding()]
    [OutputType([bool])]
    param(
        [Parameter(Mandatory = $false)]
        [string]$LogDirectory,

        [Parameter(Mandatory = $false)]
        [string]$LogFileName
    )

    try {
        Write-Verbose "Initializing secure logging system"

        # Determine log directory
        if (-not $LogDirectory) {
            $LogDirectory = if ($script:Config) {
                $script:Config.Logging.LogDirectory
            } else {
                "$env:TEMP\JML\Logs"
            }
        }

        # Expand environment variables
        $LogDirectory = [System.Environment]::ExpandEnvironmentVariables($LogDirectory)

        # Create log directory if it doesn't exist
        if (-not (Test-Path $LogDirectory)) {
            try {
                New-Item -ItemType Directory -Path $LogDirectory -Force | Out-Null
                Write-Verbose "Created log directory: $LogDirectory"
            }
            catch {
                Write-Warning "Failed to create log directory '$LogDirectory': $($_.Exception.Message)"
                # Fallback to temp directory
                $LogDirectory = "$env:TEMP\JML\Logs"
                New-Item -ItemType Directory -Path $LogDirectory -Force | Out-Null
            }
        }

        # Determine log file name
        if (-not $LogFileName) {
            $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
            $LogFileName = "JML_AdminAccount_$timestamp.log"
        }

        # Set global log path
        $script:CurrentLogPath = Join-Path $LogDirectory $LogFileName

        # Test write permissions
        try {
            Add-Content -Path $script:CurrentLogPath -Value "# JML Admin Account Management Log" -Encoding UTF8 -ErrorAction Stop
            Add-Content -Path $script:CurrentLogPath -Value "# Started: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -Encoding UTF8 -ErrorAction Stop
            Add-Content -Path $script:CurrentLogPath -Value "# User: $($script:ExecutionContext.ExecutingUser)" -Encoding UTF8 -ErrorAction Stop
            Add-Content -Path $script:CurrentLogPath -Value "# Computer: $($script:ExecutionContext.ComputerName)" -Encoding UTF8 -ErrorAction Stop
            Add-Content -Path $script:CurrentLogPath -Value "# PowerShell Version: $($script:ExecutionContext.PowerShellVersion)" -Encoding UTF8 -ErrorAction Stop
            Add-Content -Path $script:CurrentLogPath -Value "" -Encoding UTF8 -ErrorAction Stop
        }
        catch {
            Write-Warning "Failed to initialize log file '$script:CurrentLogPath': $($_.Exception.Message)"
            $script:CurrentLogPath = $null
            return $false
        }

        # Set up log rotation if configured
        $enableRotation = if ($script:Config) {
            $script:Config.Logging.EnableLogRotation
        } else {
            $true
        }

        if ($enableRotation) {
            Start-LogRotation -LogDirectory $LogDirectory
        }

        Write-Verbose "Secure logging initialized successfully: $script:CurrentLogPath"
        return $true
    }
    catch {
        Write-Warning "Failed to initialize secure logging: $($_.Exception.Message)"
        return $false
    }
}

<#
.SYNOPSIS
Starts log rotation and cleanup based on retention policies.

.DESCRIPTION
Implements log file rotation based on size and age limits. Removes old log files
according to retention policies and compresses archived logs if configured.

.PARAMETER LogDirectory
The directory containing log files to rotate.

.EXAMPLE
Start-LogRotation -LogDirectory "C:\Logs\JML"

.NOTES
Runs as background job to avoid blocking main script execution.
#>
function Start-LogRotation {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [ValidateScript({Test-Path $_ -PathType Container})]
        [string]$LogDirectory
    )

    try {
        Write-Verbose "Starting log rotation for directory: $LogDirectory"

        $retentionDays = if ($script:Config) {
            $script:Config.Logging.LogRetentionDays
        } else {
            30
        }

        $maxSizeMB = if ($script:Config) {
            $script:Config.Logging.MaxLogFileSizeMB
        } else {
            10
        }

        # Get all log files
        $logFiles = Get-ChildItem -Path $LogDirectory -Filter "*.log" -ErrorAction SilentlyContinue

        foreach ($logFile in $logFiles) {
            try {
                # Check file age
                $fileAge = (Get-Date) - $logFile.CreationTime
                if ($fileAge.TotalDays -gt $retentionDays) {
                    Write-Verbose "Removing old log file: $($logFile.Name) (Age: $([Math]::Round($fileAge.TotalDays, 1)) days)"
                    Remove-Item -Path $logFile.FullName -Force -ErrorAction SilentlyContinue
                    continue
                }

                # Check file size
                $fileSizeMB = $logFile.Length / 1MB
                if ($fileSizeMB -gt $maxSizeMB) {
                    Write-Verbose "Archiving large log file: $($logFile.Name) (Size: $([Math]::Round($fileSizeMB, 2)) MB)"

                    # Create archive name
                    $archiveName = $logFile.BaseName + "_archived_" + (Get-Date -Format "yyyyMMdd_HHmmss") + ".log"
                    $archivePath = Join-Path $LogDirectory $archiveName

                    # Move to archive
                    Move-Item -Path $logFile.FullName -Destination $archivePath -ErrorAction SilentlyContinue
                }
            }
            catch {
                Write-Verbose "Failed to process log file '$($logFile.Name)': $($_.Exception.Message)"
            }
        }

        Write-Verbose "Log rotation completed"
    }
    catch {
        Write-Warning "Log rotation failed: $($_.Exception.Message)"
    }
}

<#
.SYNOPSIS
Writes audit trail entries for compliance and security monitoring.

.DESCRIPTION
Creates detailed audit trail entries with user identity, operation context,
and security-relevant information for compliance reporting and security monitoring.

.PARAMETER Operation
The operation being audited.

.PARAMETER Details
Hashtable containing operation details.

.PARAMETER SecurityContext
Optional security context information.

.EXAMPLE
Write-AuditTrail -Operation "AdminAccountCreated" -Details @{UserUPN="<EMAIL>"; AdminAccount="user-a"}

.NOTES
Audit trails are automatically included in secure logging with data redaction.
#>
function Write-AuditTrail {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$Operation,

        [Parameter(Mandatory = $true)]
        [hashtable]$Details,

        [Parameter(Mandatory = $false)]
        [hashtable]$SecurityContext
    )

    try {
        $auditEntry = @{
            Operation = $Operation
            Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss.fff"
            User = $script:ExecutionContext.ExecutingUser
            Computer = $script:ExecutionContext.ComputerName
            ProcessId = $PID
            ScriptVersion = "3.0"
        }

        # Add operation details
        foreach ($key in $Details.Keys) {
            $auditEntry[$key] = $Details[$key]
        }

        # Add security context if provided
        if ($SecurityContext) {
            $auditEntry.SecurityContext = $SecurityContext
        }

        # Write to secure log with audit trail
        Write-SecureLog -Message "AUDIT: $Operation" -LogLevel "INFO" -AuditTrail $auditEntry -Category "Audit"

        Write-Verbose "Audit trail written for operation: $Operation"
    }
    catch {
        Write-Warning "Failed to write audit trail: $($_.Exception.Message)"
    }
}

#endregion

#region JML-Utilities Module

<#
.SYNOPSIS
JML Utilities Module

.DESCRIPTION
Provides common utility functions including password generation, input validation,
string manipulation, and helper functions used across other modules.

Module Dependencies: JML-Security, JML-Configuration
Module Version: 3.0
Module Exports: New-SecurePassword, Test-InputValidation, New-StandardUPN, Format-DisplayName

.NOTES
This module contains reusable utility functions with comprehensive parameter validation.
#>

<#
.SYNOPSIS
Generates a secure password meeting complexity requirements.

.DESCRIPTION
Creates cryptographically secure passwords with configurable complexity requirements
including length, special characters, and character set restrictions.

.PARAMETER Length
The desired password length (minimum 8, maximum 128).

.PARAMETER MinSpecialChars
Minimum number of special characters required.

.PARAMETER ExcludeAmbiguous
Switch to exclude ambiguous characters (0, O, l, 1, etc.).

.PARAMETER CustomCharacterSet
Optional custom character set to use for password generation.

.OUTPUTS
SecureString containing the generated password.

.EXAMPLE
$password = New-SecurePassword -Length 16 -MinSpecialChars 3 -ExcludeAmbiguous

.NOTES
Uses cryptographically secure random number generation for password creation.
#>
function New-SecurePassword {
    [CmdletBinding()]
    [OutputType([SecureString])]
    param(
        [Parameter(Mandatory = $false)]
        [ValidateRange(8, 128)]
        [int]$Length = 12,

        [Parameter(Mandatory = $false)]
        [ValidateRange(0, 10)]
        [int]$MinSpecialChars = 2,

        [Parameter(Mandatory = $false)]
        [switch]$ExcludeAmbiguous,

        [Parameter(Mandatory = $false)]
        [string]$CustomCharacterSet
    )

    try {
        Write-Verbose "Generating secure password with length $Length and $MinSpecialChars special characters"

        # Get password policy from configuration
        $passwordPolicy = if ($script:Config) {
            $script:Config.ActiveDirectory.PasswordPolicy
        } else {
            @{
                MinLength = 12
                MinSpecialChars = 2
                RequireComplexity = $true
            }
        }

        # Ensure minimum requirements
        $Length = [Math]::Max($Length, $passwordPolicy.MinLength)
        $MinSpecialChars = [Math]::Max($MinSpecialChars, $passwordPolicy.MinSpecialChars)

        # Define character sets
        $upperCase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        $lowerCase = "abcdefghijklmnopqrstuvwxyz"
        $numbers = "0123456789"
        $specialChars = "!@#$%^&*()_+-=[]{}|;:,.<>?"

        # Exclude ambiguous characters if requested
        if ($ExcludeAmbiguous) {
            $upperCase = $upperCase -replace "[O]", ""
            $lowerCase = $lowerCase -replace "[l]", ""
            $numbers = $numbers -replace "[01]", ""
            $specialChars = $specialChars -replace "[|]", ""
        }

        # Use custom character set if provided
        if ($CustomCharacterSet) {
            $allChars = $CustomCharacterSet
        } else {
            $allChars = $upperCase + $lowerCase + $numbers + $specialChars
        }

        # Generate password ensuring complexity requirements
        $password = ""
        $rng = [System.Security.Cryptography.RNGCryptoServiceProvider]::new()

        try {
            # Ensure at least one character from each required set
            if (-not $CustomCharacterSet) {
                $password += Get-RandomCharacter -CharacterSet $upperCase -RNG $rng
                $password += Get-RandomCharacter -CharacterSet $lowerCase -RNG $rng
                $password += Get-RandomCharacter -CharacterSet $numbers -RNG $rng

                # Add required special characters
                for ($i = 0; $i -lt $MinSpecialChars; $i++) {
                    $password += Get-RandomCharacter -CharacterSet $specialChars -RNG $rng
                }
            }

            # Fill remaining length with random characters
            $remainingLength = $Length - $password.Length
            for ($i = 0; $i -lt $remainingLength; $i++) {
                $password += Get-RandomCharacter -CharacterSet $allChars -RNG $rng
            }

            # Shuffle the password to randomize character positions
            $passwordArray = $password.ToCharArray()
            for ($i = $passwordArray.Length - 1; $i -gt 0; $i--) {
                $j = Get-SecureRandomNumber -Maximum ($i + 1) -RNG $rng
                $temp = $passwordArray[$i]
                $passwordArray[$i] = $passwordArray[$j]
                $passwordArray[$j] = $temp
            }

            $finalPassword = -join $passwordArray

            # Convert to SecureString
            $securePassword = ConvertTo-SecureString -String $finalPassword -AsPlainText -Force

            # Clear sensitive variables
            $password = $null
            $finalPassword = $null
            $passwordArray = $null

            Write-Verbose "Secure password generated successfully"
            return $securePassword
        }
        finally {
            if ($rng) {
                $rng.Dispose()
            }
        }
    }
    catch {
        Write-Error "Failed to generate secure password: $($_.Exception.Message)"
        throw
    }
}

<#
.SYNOPSIS
Gets a random character from a character set using cryptographic RNG.

.DESCRIPTION
Helper function that uses cryptographically secure random number generation
to select a character from the provided character set.

.PARAMETER CharacterSet
The character set to select from.

.PARAMETER RNG
The RNG instance to use.

.OUTPUTS
Single character from the character set.

.EXAMPLE
$char = Get-RandomCharacter -CharacterSet "ABC123" -RNG $rng

.NOTES
Internal helper function for secure password generation.
#>
function Get-RandomCharacter {
    [CmdletBinding()]
    [OutputType([char])]
    param(
        [Parameter(Mandatory = $true)]
        [string]$CharacterSet,

        [Parameter(Mandatory = $true)]
        [System.Security.Cryptography.RNGCryptoServiceProvider]$RNG
    )

    $index = Get-SecureRandomNumber -Maximum $CharacterSet.Length -RNG $RNG
    return $CharacterSet[$index]
}

<#
.SYNOPSIS
Generates a cryptographically secure random number.

.DESCRIPTION
Uses RNGCryptoServiceProvider to generate secure random numbers within
the specified range for password generation and other security purposes.

.PARAMETER Maximum
The maximum value (exclusive) for the random number.

.PARAMETER RNG
The RNG instance to use.

.OUTPUTS
Integer containing the secure random number.

.EXAMPLE
$number = Get-SecureRandomNumber -Maximum 100 -RNG $rng

.NOTES
Internal helper function for cryptographically secure random generation.
#>
function Get-SecureRandomNumber {
    [CmdletBinding()]
    [OutputType([int])]
    param(
        [Parameter(Mandatory = $true)]
        [int]$Maximum,

        [Parameter(Mandatory = $true)]
        [System.Security.Cryptography.RNGCryptoServiceProvider]$RNG
    )

    $bytes = New-Object byte[] 4
    $RNG.GetBytes($bytes)
    $value = [System.BitConverter]::ToUInt32($bytes, 0)
    return [int]($value % $Maximum)
}

<#
.SYNOPSIS
Validates input against security and format requirements.

.DESCRIPTION
Performs comprehensive input validation including length limits, pattern matching,
HTML/script tag detection, and security threat assessment.

.PARAMETER InputString
The string to validate.

.PARAMETER ValidationRules
Hashtable containing validation rules to apply.

.PARAMETER AllowEmpty
Switch to allow empty strings.

.OUTPUTS
PSCustomObject with IsValid, ErrorMessage, and SanitizedInput properties.

.EXAMPLE
$validation = Test-InputValidation -InputString "<EMAIL>" -ValidationRules @{Pattern="^[\w\.-]+@[\w\.-]+\.\w+$"}

.NOTES
Implements comprehensive security validation with configurable rules.
#>
function Test-InputValidation {
    [CmdletBinding()]
    [OutputType([PSCustomObject])]
    param(
        [Parameter(Mandatory = $true)]
        [AllowEmptyString()]
        [string]$InputString,

        [Parameter(Mandatory = $false)]
        [hashtable]$ValidationRules = @{},

        [Parameter(Mandatory = $false)]
        [switch]$AllowEmpty
    )

    try {
        $result = [PSCustomObject]@{
            IsValid = $true
            ErrorMessage = ""
            SanitizedInput = $InputString
            ValidationDetails = @{}
        }

        # Check for empty input
        if ([string]::IsNullOrWhiteSpace($InputString)) {
            if (-not $AllowEmpty) {
                $result.IsValid = $false
                $result.ErrorMessage = "Input cannot be empty"
                return $result
            } else {
                return $result
            }
        }

        # Get validation configuration
        $validationConfig = if ($script:Config) {
            $script:Config.Security.InputValidation
        } else {
            @{
                EnableStrictValidation = $true
                MaxInputLength = 256
                EnableSanitization = $true
                RemoveHtmlTags = $true
                RemoveScriptTags = $true
            }
        }

        # Length validation
        $maxLength = if ($ValidationRules.MaxLength) {
            $ValidationRules.MaxLength
        } else {
            $validationConfig.MaxInputLength
        }

        if ($InputString.Length -gt $maxLength) {
            $result.IsValid = $false
            $result.ErrorMessage = "Input exceeds maximum length of $maxLength characters"
            return $result
        }

        # Pattern validation
        if ($ValidationRules.Pattern) {
            if ($InputString -notmatch $ValidationRules.Pattern) {
                $result.IsValid = $false
                $result.ErrorMessage = "Input does not match required pattern"
                $result.ValidationDetails.PatternMatch = $false
                return $result
            }
            $result.ValidationDetails.PatternMatch = $true
        }

        # Security validation
        if ($validationConfig.EnableStrictValidation) {
            # Check for potential script injection
            $scriptPatterns = @(
                '<script[^>]*>.*?</script>',
                'javascript:',
                'vbscript:',
                'onload=',
                'onerror=',
                'onclick='
            )

            foreach ($pattern in $scriptPatterns) {
                if ($InputString -match $pattern) {
                    $result.IsValid = $false
                    $result.ErrorMessage = "Input contains potentially malicious content"
                    $result.ValidationDetails.SecurityThreat = $true
                    return $result
                }
            }
        }

        # Sanitization
        if ($validationConfig.EnableSanitization) {
            $sanitized = $InputString

            if ($validationConfig.RemoveHtmlTags) {
                $sanitized = $sanitized -replace '<[^>]+>', ''
            }

            if ($validationConfig.RemoveScriptTags) {
                $sanitized = $sanitized -replace '<script[^>]*>.*?</script>', ''
            }

            # Remove potentially dangerous characters
            $sanitized = $sanitized -replace '[<>"'']', ''

            $result.SanitizedInput = $sanitized
        }

        Write-Verbose "Input validation completed successfully"
        return $result
    }
    catch {
        Write-Warning "Input validation failed: $($_.Exception.Message)"
        return [PSCustomObject]@{
            IsValid = $false
            ErrorMessage = "Validation error: $($_.Exception.Message)"
            SanitizedInput = ""
            ValidationDetails = @{}
        }
    }
}

#endregion

#region JML-ActiveDirectory Module

<#
.SYNOPSIS
JML Active Directory Operations Module

.DESCRIPTION
Provides optimized Active Directory operations with caching, performance tuning,
and comprehensive error handling. Implements enterprise-grade AD management
with security best practices.

Module Dependencies: JML-Security, JML-Logging, JML-Utilities
Module Version: 3.0
Module Exports: Get-OptimizedADUserDetails, Get-ValidatedUPN, New-AdminAccount, New-StandardUPN

.NOTES
This module implements AD operations with performance optimization and comprehensive validation.
#>

<#
.SYNOPSIS
Retrieves user details from Active Directory with optimized queries and caching.

.DESCRIPTION
Performs efficient Active Directory queries with configurable property filtering,
result caching, and comprehensive error handling. Implements performance
optimizations for large domain environments.

.PARAMETER UserUPN
The User Principal Name to search for.

.PARAMETER Properties
Array of properties to retrieve. Uses optimized defaults if not specified.

.PARAMETER UseCache
Switch to enable result caching for improved performance.

.PARAMETER CacheExpirationMinutes
Cache expiration time in minutes.

.EXAMPLE
$user = Get-OptimizedADUserDetails -UserUPN "<EMAIL>" -UseCache

.NOTES
Implements AD query best practices with performance optimizations and caching.
#>
function Get-OptimizedADUserDetails {
    [CmdletBinding()]
    [OutputType([Microsoft.ActiveDirectory.Management.ADUser])]
    param(
        [Parameter(Mandatory = $true)]
        [ValidatePattern('^[\w][\w\.-]{1,62}[\w]@[\w][\w-]{1,62}\.[a-zA-Z]{2,}$')]
        [string]$UserUPN,

        [Parameter(Mandatory = $false)]
        [string[]]$Properties,

        [Parameter(Mandatory = $false)]
        [switch]$UseCache,

        [Parameter(Mandatory = $false)]
        [ValidateRange(1, 1440)]
        [int]$CacheExpirationMinutes = 15
    )

    try {
        Write-SecureLog -Message "Retrieving AD user details" -LogLevel "INFO" -AuditTrail @{
            Operation = "ADUserQuery"
            UserUPN = Get-StringHash -InputString $UserUPN -UseSalt
            CacheEnabled = $UseCache.IsPresent
        }

        # Use configured properties or defaults
        if (-not $Properties) {
            $Properties = if ($script:Config -and $script:Config.ActiveDirectory.QueryOptimization.UserProperties) {
                $script:Config.ActiveDirectory.QueryOptimization.UserProperties
            } else {
                @('GivenName', 'Surname', 'DisplayName', 'SamAccountName', 'UserPrincipalName', 'EmailAddress', 'Enabled', 'DistinguishedName', 'Office')
            }
        }

        # Check cache if enabled
        if ($UseCache -and $script:ADUserCache) {
            $cacheKey = "$UserUPN|$($Properties -join ',')"
            $cachedResult = $script:ADUserCache[$cacheKey]

            if ($cachedResult -and $cachedResult.Timestamp -gt (Get-Date).AddMinutes(-$CacheExpirationMinutes)) {
                Write-Verbose "Returning cached AD user result for $UserUPN"
                return $cachedResult.Data
            }
        }

        # Configure query parameters
        $queryParams = @{
            Filter = "UserPrincipalName -eq '$UserUPN'"
            Properties = $Properties
            ErrorAction = 'Stop'
        }

        # Add result limit if configured
        if ($script:Config -and $script:Config.ActiveDirectory.QueryOptimization.MaxResults) {
            $queryParams.ResultSetSize = $script:Config.ActiveDirectory.QueryOptimization.MaxResults
        }

        # Execute query with timeout
        $user = $null
        $queryJob = Start-Job -ScriptBlock {
            param($QueryParams)
            Import-Module ActiveDirectory -Force
            Get-ADUser @QueryParams
        } -ArgumentList $queryParams

        $timeout = if ($script:Config -and $script:Config.ActiveDirectory.QueryOptimization.QueryTimeout) {
            $script:Config.ActiveDirectory.QueryOptimization.QueryTimeout
        } else {
            30
        }

        try {
            if (Wait-Job -Job $queryJob -Timeout $timeout) {
                $user = Receive-Job -Job $queryJob -ErrorAction Stop
            } else {
                throw "AD query timeout after $timeout seconds"
            }
        }
        finally {
            Remove-Job -Job $queryJob -Force -ErrorAction SilentlyContinue
        }

        if (-not $user) {
            Write-SecureLog -Message "User not found in Active Directory" -LogLevel "WARNING" -AuditTrail @{
                Operation = "ADUserNotFound"
                UserUPN = Get-StringHash -InputString $UserUPN -UseSalt
            }
            return $null
        }

        # Cache result if enabled
        if ($UseCache) {
            if (-not $script:ADUserCache) {
                $script:ADUserCache = @{}
            }

            $cacheKey = "$UserUPN|$($Properties -join ',')"
            $script:ADUserCache[$cacheKey] = @{
                Data = $user
                Timestamp = Get-Date
            }
        }

        Write-SecureLog -Message "Successfully retrieved AD user details" -LogLevel "INFO" -AuditTrail @{
            Operation = "ADUserQuerySuccess"
            UserUPN = Get-StringHash -InputString $UserUPN -UseSalt
            PropertiesCount = $Properties.Count
        }

        return $user
    }
    catch [Microsoft.ActiveDirectory.Management.ADIdentityNotFoundException] {
        Write-SecureLog -Message "User not found in Active Directory" -LogLevel "WARNING" -AuditTrail @{
            Operation = "ADUserNotFound"
            UserUPN = Get-StringHash -InputString $UserUPN -UseSalt
        }
        return $null
    }
    catch [System.TimeoutException] {
        Write-SecureLog -Message "AD query timeout: $($_.Exception.Message)" -LogLevel "ERROR"
        throw
    }
    catch {
        Write-SecureLog -Message "Failed to retrieve AD user details: $($_.Exception.Message)" -LogLevel "ERROR" -AuditTrail @{
            Operation = "ADUserQueryError"
            ErrorType = $_.Exception.GetType().Name
        }
        throw
    }
}

<#
.SYNOPSIS
Validates and retrieves a User Principal Name with enhanced input validation.

.DESCRIPTION
Provides comprehensive UPN validation with pattern matching, domain verification,
and existence checking. Supports different validation modes for create, delete,
and reset operations.

.PARAMETER Domain
The domain to validate against.

.PARAMETER ForDeletion
Switch indicating validation is for deletion operation.

.PARAMETER ForReset
Switch indicating validation is for reset operation.

.PARAMETER MaxAttempts
Maximum number of input attempts before failing.

.EXAMPLE
$upn = Get-ValidatedUPN -Domain "domain.com" -MaxAttempts 3

.NOTES
Implements comprehensive input validation with security best practices.
#>
function Get-ValidatedUPN {
    [CmdletBinding()]
    [OutputType([string])]
    param(
        [Parameter(Mandatory = $true)]
        [ValidatePattern('^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')]
        [string]$Domain,

        [Parameter(Mandatory = $false)]
        [switch]$ForDeletion,

        [Parameter(Mandatory = $false)]
        [switch]$ForReset,

        [Parameter(Mandatory = $false)]
        [ValidateRange(1, 10)]
        [int]$MaxAttempts = 3
    )

    $attempt = 1
    $validUPN = $null

    while ($attempt -le $MaxAttempts -and -not $validUPN) {
        try {
            Write-Host "`nAttempt $attempt of $MaxAttempts" -ForegroundColor Cyan

            # Provide context-specific prompts
            $promptMessage = switch ($true) {
                $ForDeletion { "Enter the UPN of the user whose admin account you want to DELETE" }
                $ForReset { "Enter the UPN of the user whose admin account you want to RESET" }
                default { "Enter the UPN of the user for whom you want to create an admin account" }
            }

            $upn = Read-Host $promptMessage

            # Input validation using utility function
            $validation = Test-InputValidation -InputString $upn -ValidationRules @{
                Pattern = '^[\w][\w\.-]{1,62}[\w]@[\w][\w-]{1,62}\.[a-zA-Z]{2,}$'
                MaxLength = 254
            }

            if (-not $validation.IsValid) {
                Write-Host "Invalid UPN: $($validation.ErrorMessage)" -ForegroundColor Red
                $attempt++
                continue
            }

            $upn = $validation.SanitizedInput.Trim().ToLower()

            # Domain validation
            $upnDomain = $upn.Split('@')[1]
            if ($upnDomain -ne $Domain.ToLower()) {
                Write-Host "UPN domain '$upnDomain' does not match expected domain '$Domain'." -ForegroundColor Red
                $attempt++
                continue
            }

            # Check if user exists in AD
            $existingUser = Get-OptimizedADUserDetails -UserUPN $upn -UseCache
            if (-not $existingUser) {
                Write-Host "User '$upn' not found in Active Directory." -ForegroundColor Red
                $attempt++
                continue
            }

            # Check admin account existence based on operation type
            if ($ForDeletion -or $ForReset) {
                $adminSamAccountName = "$($existingUser.SamAccountName)-a"
                $adminAccountExists = try {
                    Get-ADUser -Filter "SamAccountName -eq '$adminSamAccountName'" -ErrorAction Stop
                } catch {
                    $null
                }

                if (-not $adminAccountExists) {
                    Write-Host "No admin account exists for this user." -ForegroundColor Red
                    $attempt++
                    continue
                }
            }

            # All validations passed
            $validUPN = $upn

            Write-SecureLog -Message "UPN validation successful" -LogLevel "INFO" -AuditTrail @{
                Operation = "UPNValidation"
                ValidationMode = if ($ForDeletion) { "Delete" } elseif ($ForReset) { "Reset" } else { "Create" }
                Attempts = $attempt
                UserHash = Get-StringHash -InputString $upn -UseSalt
            }

        }
        catch {
            Write-Host "Error validating UPN: $($_.Exception.Message)" -ForegroundColor Red
            Write-SecureLog -Message "UPN validation error: $($_.Exception.Message)" -LogLevel "ERROR"
            $attempt++
        }
    }

    if (-not $validUPN) {
        $errorMsg = "Failed to get valid UPN after $MaxAttempts attempts."
        Write-SecureLog -Message $errorMsg -LogLevel "ERROR"
        throw $errorMsg
    }

    return $validUPN
}

<#
.SYNOPSIS
Creates a standardized User Principal Name based on user details.

.DESCRIPTION
Generates a standardized UPN format based on user's first name, last name,
and domain, with conflict resolution and validation.

.PARAMETER FirstName
User's first name.

.PARAMETER LastName
User's last name.

.PARAMETER Domain
Target domain for the UPN.

.PARAMETER PreferredFormat
Preferred UPN format: FirstLast, FirstDotLast, FirstInitialLast.

.OUTPUTS
String containing the standardized UPN.

.EXAMPLE
$upn = New-StandardUPN -FirstName "John" -LastName "Doe" -Domain "domain.com"

.NOTES
Implements organizational UPN standards with conflict resolution.
#>
function New-StandardUPN {
    [CmdletBinding()]
    [OutputType([string])]
    param(
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$FirstName,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$LastName,

        [Parameter(Mandatory = $true)]
        [ValidatePattern('^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')]
        [string]$Domain,

        [Parameter(Mandatory = $false)]
        [ValidateSet('FirstLast', 'FirstDotLast', 'FirstInitialLast')]
        [string]$PreferredFormat = 'FirstDotLast'
    )

    try {
        # Sanitize input names
        $cleanFirstName = ($FirstName -replace '[^a-zA-Z]', '').ToLower()
        $cleanLastName = ($LastName -replace '[^a-zA-Z]', '').ToLower()

        if ([string]::IsNullOrEmpty($cleanFirstName) -or [string]::IsNullOrEmpty($cleanLastName)) {
            throw "Invalid characters in name fields after sanitization"
        }

        # Generate UPN based on preferred format
        $upnPrefix = switch ($PreferredFormat) {
            'FirstLast' { "$cleanFirstName$cleanLastName" }
            'FirstDotLast' { "$cleanFirstName.$cleanLastName" }
            'FirstInitialLast' { "$($cleanFirstName[0])$cleanLastName" }
            default { "$cleanFirstName.$cleanLastName" }
        }

        $standardUPN = "$upnPrefix@$Domain"

        Write-SecureLog -Message "Generated standard UPN" -LogLevel "INFO" -AuditTrail @{
            Operation = "StandardUPNGeneration"
            Format = $PreferredFormat
            Domain = $Domain
            GeneratedUPN = Get-StringHash -InputString $standardUPN -UseSalt
        }

        return $standardUPN.ToLower()
    }
    catch {
        Write-SecureLog -Message "Failed to generate standard UPN: $($_.Exception.Message)" -LogLevel "ERROR"
        throw
    }
}

# Backward compatibility aliases
function Get-ValidUPN {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Domain,

        [Parameter(Mandatory = $false)]
        [switch]$ForDeletion,

        [Parameter(Mandatory = $false)]
        [switch]$ForReset
    )

    return Get-ValidatedUPN -Domain $Domain -ForDeletion:$ForDeletion -ForReset:$ForReset
}

function Get-ADUserDetails {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$UserUPN
    )

    return Get-OptimizedADUserDetails -UserUPN $UserUPN -UseCache
}

#endregion

#region JML-Jira Module

<#
.SYNOPSIS
JML Jira Integration Module

.DESCRIPTION
Provides modern Jira integration with Atlassian Document Format (ADF) support,
exponential backoff retry logic, file validation, and comprehensive error handling.
Implements enterprise-grade Jira API best practices.

Module Dependencies: JML-Security, JML-Logging, JML-Utilities
Module Version: 3.0
Module Exports: Initialize-JiraConnection, Add-EnhancedJiraComment, Add-EnhancedJiraAttachment, Format-JiraCommentADF

.NOTES
This module implements modern Jira API integration with comprehensive retry logic and rich formatting.
#>

<#
.SYNOPSIS
Initializes Jira connection with enhanced authentication and validation.

.DESCRIPTION
Establishes connection to Jira using modern authentication methods with
comprehensive validation, retry logic, and connection pooling.

.PARAMETER JiraUrl
The Jira server URL.

.PARAMETER JiraUsername
Username for Jira authentication.

.PARAMETER JiraApiToken
API token for Jira authentication.

.PARAMETER TestConnection
Switch to test the connection after initialization.

.OUTPUTS
Boolean indicating successful connection.

.EXAMPLE
$connected = Initialize-JiraConnection -JiraUrl "https://company.atlassian.net" -JiraUsername "user" -JiraApiToken $token

.NOTES
Implements modern Jira authentication with comprehensive error handling.
#>
function Initialize-JiraConnection {
    [CmdletBinding()]
    [OutputType([bool])]
    param(
        [Parameter(Mandatory = $true)]
        [ValidatePattern('^https?://[a-zA-Z0-9.-]+')]
        [string]$JiraUrl,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$JiraUsername,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$JiraApiToken,

        [Parameter(Mandatory = $false)]
        [switch]$TestConnection
    )

    try {
        Write-SecureLog -Message "Initializing Jira connection" -LogLevel "INFO" -AuditTrail @{
            Operation = "JiraConnectionInit"
            JiraUrl = Protect-SensitiveData -InputString $JiraUrl -RedactionType "Server"
            Username = Protect-SensitiveData -InputString $JiraUsername -RedactionType "UPN"
        }

        # Create credential object
        $secureToken = ConvertTo-SecureString -String $JiraApiToken -AsPlainText -Force
        $jiraCredential = New-Object PSCredential($JiraUsername, $secureToken)

        # Set Jira session with retry logic
        $connectionResult = Invoke-JiraOperationWithRetry -Operation {
            Set-JiraConfigServer -Server $JiraUrl -ErrorAction Stop
            New-JiraSession -Credential $jiraCredential -ErrorAction Stop
        } -OperationName "JiraConnection" -MaxAttempts 3

        if (-not $connectionResult.Success) {
            Write-SecureLog -Message "Failed to establish Jira connection: $($connectionResult.ErrorMessage)" -LogLevel "ERROR"
            return $false
        }

        # Test connection if requested
        if ($TestConnection) {
            $testResult = Test-JiraConnection
            if (-not $testResult) {
                Write-SecureLog -Message "Jira connection test failed" -LogLevel "ERROR"
                return $false
            }
        }

        Write-SecureLog -Message "Jira connection established successfully" -LogLevel "INFO" -AuditTrail @{
            Operation = "JiraConnectionSuccess"
            JiraUrl = Protect-SensitiveData -InputString $JiraUrl -RedactionType "Server"
        }

        return $true
    }
    catch {
        Write-SecureLog -Message "Jira connection initialization failed: $($_.Exception.Message)" -LogLevel "ERROR" -AuditTrail @{
            Operation = "JiraConnectionError"
            ErrorType = $_.Exception.GetType().Name
        }
        return $false
    }
}

<#
.SYNOPSIS
Tests Jira connection and API accessibility.

.DESCRIPTION
Performs comprehensive Jira connection testing including API accessibility,
authentication validation, and basic operations.

.OUTPUTS
Boolean indicating connection health.

.EXAMPLE
$healthy = Test-JiraConnection

.NOTES
Internal function for connection validation.
#>
function Test-JiraConnection {
    [CmdletBinding()]
    [OutputType([bool])]
    param()

    try {
        # Test basic API access
        $testResult = Invoke-JiraOperationWithRetry -Operation {
            Get-JiraServerInformation -ErrorAction Stop
        } -OperationName "JiraConnectionTest" -MaxAttempts 2

        return $testResult.Success
    }
    catch {
        Write-Verbose "Jira connection test failed: $($_.Exception.Message)"
        return $false
    }
}

<#
.SYNOPSIS
Executes Jira operations with exponential backoff retry logic.

.DESCRIPTION
Provides robust retry mechanism for Jira API operations with exponential backoff,
jitter, and comprehensive error categorization for different failure types.

.PARAMETER Operation
ScriptBlock containing the Jira operation to execute.

.PARAMETER OperationName
Descriptive name for the operation (for logging).

.PARAMETER IssueKey
Jira issue key for context.

.PARAMETER MaxAttempts
Maximum number of retry attempts.

.OUTPUTS
PSCustomObject with Success, Data, ErrorMessage, and ErrorCategory properties.

.EXAMPLE
$result = Invoke-JiraOperationWithRetry -Operation { Get-JiraIssue -Key "PROJ-123" } -OperationName "GetIssue"

.NOTES
Implements modern retry patterns with intelligent backoff and error categorization.
#>
function Invoke-JiraOperationWithRetry {
    [CmdletBinding()]
    [OutputType([PSCustomObject])]
    param(
        [Parameter(Mandatory = $true)]
        [ScriptBlock]$Operation,

        [Parameter(Mandatory = $true)]
        [string]$OperationName,

        [Parameter(Mandatory = $false)]
        [string]$IssueKey = "Unknown",

        [Parameter(Mandatory = $false)]
        [ValidateRange(1, 10)]
        [int]$MaxAttempts = 5
    )

    $attempt = 1
    $baseDelay = if ($script:Config -and $script:Config.Jira.ApiSettings.RetrySettings.BaseDelay) {
        $script:Config.Jira.ApiSettings.RetrySettings.BaseDelay
    } else {
        1
    }
    $maxDelay = if ($script:Config -and $script:Config.Jira.ApiSettings.RetrySettings.MaxDelay) {
        $script:Config.Jira.ApiSettings.RetrySettings.MaxDelay
    } else {
        32
    }
    $jitterEnabled = if ($script:Config -and $script:Config.Jira.ApiSettings.RetrySettings.JitterEnabled) {
        $script:Config.Jira.ApiSettings.RetrySettings.JitterEnabled
    } else {
        $true
    }

    while ($attempt -le $MaxAttempts) {
        try {
            Write-Verbose "Executing Jira operation '$OperationName' (attempt $attempt/$MaxAttempts)"

            $result = & $Operation

            # Success
            return [PSCustomObject]@{
                Success = $true
                Data = $result
                ErrorMessage = $null
                ErrorCategory = $null
                Attempts = $attempt
            }
        }
        catch {
            $errorCategory = Get-JiraErrorCategory -Exception $_.Exception
            $shouldRetry = Test-JiraErrorRetryable -ErrorCategory $errorCategory

            Write-SecureLog -Message "Jira operation '$OperationName' failed (attempt $attempt/$MaxAttempts): $($_.Exception.Message)" -LogLevel "WARNING" -AuditTrail @{
                Operation = $OperationName
                IssueKey = $IssueKey
                Attempt = $attempt
                ErrorCategory = $errorCategory
                ShouldRetry = $shouldRetry
            }

            if ($attempt -eq $MaxAttempts -or -not $shouldRetry) {
                # Final failure or non-retryable error
                return [PSCustomObject]@{
                    Success = $false
                    Data = $null
                    ErrorMessage = $_.Exception.Message
                    ErrorCategory = $errorCategory
                    Attempts = $attempt
                }
            }

            # Calculate delay with exponential backoff and optional jitter
            $delay = [Math]::Min($baseDelay * [Math]::Pow(2, $attempt - 1), $maxDelay)

            if ($jitterEnabled) {
                # Add jitter (±25% of delay)
                $jitter = $delay * 0.25 * (Get-Random -Minimum -1.0 -Maximum 1.0)
                $delay = [Math]::Max(1, $delay + $jitter)
            }

            Write-Verbose "Retrying Jira operation '$OperationName' in $([Math]::Round($delay, 2)) seconds..."
            Start-Sleep -Seconds $delay

            $attempt++
        }
    }
}

<#
.SYNOPSIS
Categorizes Jira API errors for appropriate retry logic.

.DESCRIPTION
Analyzes exception details to categorize errors as network, authentication,
rate limiting, server errors, or client errors for intelligent retry decisions.

.PARAMETER Exception
The exception object to categorize.

.OUTPUTS
String indicating the error category.

.EXAMPLE
$category = Get-JiraErrorCategory -Exception $_.Exception

.NOTES
Enables intelligent retry logic based on error type.
#>
function Get-JiraErrorCategory {
    [CmdletBinding()]
    [OutputType([string])]
    param(
        [Parameter(Mandatory = $true)]
        [System.Exception]$Exception
    )

    $message = $Exception.Message.ToLower()
    $exceptionType = $Exception.GetType().Name

    # Network-related errors (retryable)
    if ($message -match "timeout|connection|network|dns|socket" -or
        $exceptionType -match "WebException|HttpRequestException|SocketException") {
        return "Network"
    }

    # Authentication errors (not retryable without credential refresh)
    if ($message -match "unauthorized|authentication|401|403" -or
        $exceptionType -match "UnauthorizedAccessException") {
        return "Authentication"
    }

    # Rate limiting (retryable with longer delay)
    if ($message -match "rate limit|429|too many requests") {
        return "RateLimit"
    }

    # Server errors (retryable)
    if ($message -match "internal server error|502|503|504|500" -or
        $exceptionType -match "HttpException") {
        return "ServerError"
    }

    # Client errors (not retryable)
    if ($message -match "bad request|404|400|422|not found") {
        return "ClientError"
    }

    # Unknown errors (retryable with caution)
    return "Unknown"
}

<#
.SYNOPSIS
Determines if a Jira error should be retried.

.DESCRIPTION
Makes retry decisions based on error category, implementing best practices
for different types of API failures.

.PARAMETER ErrorCategory
The categorized error type.

.OUTPUTS
Boolean indicating if the operation should be retried.

.EXAMPLE
$shouldRetry = Test-JiraErrorRetryable -ErrorCategory "Network"

.NOTES
Implements intelligent retry logic to avoid unnecessary API calls.
#>
function Test-JiraErrorRetryable {
    [CmdletBinding()]
    [OutputType([bool])]
    param(
        [Parameter(Mandatory = $true)]
        [string]$ErrorCategory
    )

    switch ($ErrorCategory) {
        "Network" { return $true }      # Network issues are retryable
        "ServerError" { return $true }  # Server errors are retryable
        "RateLimit" { return $true }    # Rate limits are retryable with delay
        "Unknown" { return $true }      # Unknown errors get one retry
        "Authentication" { return $false } # Auth errors need credential refresh
        "ClientError" { return $false }    # Client errors are not retryable
        default { return $false }
    }
}

#endregion
