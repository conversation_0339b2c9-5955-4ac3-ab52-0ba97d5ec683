# [CmdletBinding()]
param (
    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$deploymentName,

    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$subscriptions,

    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$primaryRegion,

    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$location
)

# LOCATION SWITCH
switch ($location)
{
    southeastasia { 
        $deploymentLocation = "South East Asia"
        $subscription = $subscriptions.Split(',') | Where-Object { $_ -match "sea" }
    }
    eastasia      { 
        $deploymentLocation = "East Asia"
        $subscription = $subscriptions.Split(',') | Where-Object { $_ -match "eaa" }
    }
    uksouth       { 
        $deploymentLocation = "UK South" 
        $subscription = $subscriptions.Split(',') | Where-Object { $_ -match "uks" }
    }
    ukwest        { 
        $deploymentLocation = "UK West" 
        $subscription = $subscriptions.Split(',') | Where-Object { $_ -match "ukw" }
    }
}

# Set the subscription Context
Write-Host "`n`n##[command]`tSet-AzContext -Subscription $($subscription) 🚶"
Set-AzContext -Subscription $subscription

$tenantId         = $(Get-AzContext).tenant.id
$subscriptionId   = $(Get-AzContext).subscription.id

Write-Host "##[debug]🐞`tTenant ID          : $tenantId"
Write-Host "##[debug]🐞`tSubscription       : $subscription"
Write-Host "##[debug]🐞`tSubscription ID    : $subscriptionId"
Write-Host "##[debug]🐞`tDeployment Location: $deploymentLocation"
Write-host "##[debug]🐞`tDeployment Name    : $deploymentName"

# Get the outputs from the deployment
$outputs                 = (Get-AzDeployment -Name $deploymentName).Outputs

$resourceGroup           = $outputs["o_resourceGroupName"].Value
$applicationGroupName    = $outputs["o_applicationGroupName"].Value
$hostPoolName            = $outputs["o_hostPoolName"].Value
$fileShareId             = $outputs["o_fileShareId"].Value

Write-Host "`n`n##[debug]🐞`tTemplate Outputs 🌟"
Write-Host "##[debug]🐞`tResource Group    : $resourceGroup"
Write-Host "##[debug]🐞`tApplication Groups: $applicationGroupName"
Write-Host "##[debug]🐞`tHostPool Name     : $hostPoolName"
Write-Host "##[debug]🐞`tFileShare ID      : $fileShareId"

########## STORAGE ACCOUNT ASSIGNMENTS

$hostPool  = Get-AzWvdHostPool -Name $hostPoolName -ResourceGroupName $resourceGroup
$userGroup = (Get-AzTag -ResourceId $hostPool.Id).Properties.TagsProperty.RBAC_UserGroup

# For some reason the azure resource id is default/shares but the RBAC assignment id is default/fileshares...
$fileShareId = $fileShareId.replace('default/shares','default/fileshares')

if ( $fileShareId ) {
    $avdUserID        = $(Get-AzADGroup -DisplayName $userGroup).id
    $avdUserRbacRoles = @(
        "Storage File Data SMB Share Contributor"
    )
    # User Role Assignments
    Write-Host "`n`n##[debug]🐞`tFile Share Config 🏗️"
    Write-Host "##[debug]🐞`tFile Share ID     : $fileShareId"
    Write-Host "##[debug]🐞`tAVD Group         : $userGroup"
    Write-Host "##[debug]🐞`tAVD Group ID      : $avdUserID"
    Write-Host "##[debug]🐞`tAVD User RBAC Role: $avdUserRbacRoles"

    foreach ( $role in $avdUserRbacRoles) {
        $assignment = Get-AzRoleAssignment `
            -RoleDefinitionName $role `
            -Scope $fileShareId `
            -ObjectId $avdUserID

        if ($assignment) {
            Write-Host "##[debug]🐞`tRole Assignment Exists: $role"
            $assignment
        } else {
            Write-Host "##[debug]🐞`tCreating Role Assignment: $role"
            New-AzRoleAssignment `
                -RoleDefinitionName $role `
                -Scope $fileShareId `
                -ObjectId $avdUserID
        }
    }
} 

########## ONLY APPLY RBAC TO PRIMARY REGION FOR THE RESOURCES BELOW

if ( $location -eq $primaryRegion) {
    ########## APPLICATION GROUP ROLES

    # User Group - Gets Assigned To Application Group
    forEach ($appGroup in $applicationGroupName) {
        $applicationGroup = $(Get-AzWvdApplicationGroup -ResourceGroup $resourceGroup -Name $appGroup["name"].Value)
        $avdUserGroup     = $applicationGroup.Tag.AdditionalProperties.RBAC_UserGroup
        $avdUserGroupID   = $(Get-AzADGroup -DisplayName $avdUserGroup).id
        Write-Host "`n`n##[debug]🐞`tApplication Group Config 🏗️"
        Write-Host "##[debug]🐞`tApplication Group: $applicationGroup"
        Write-Host "##[debug]🐞`tAVD User Group   : $avdUserGroup"
        Write-Host "##[debug]🐞`tAVD User Group ID: $avdUserGroupID"


        # Assign Roles - Application Group
        $assignment = Get-AzRoleAssignment `
            -RoleDefinitionName "Desktop Virtualization User" `
            -Scope $applicationGroup.id `
            -ObjectId $avdUserGroupID

        if ($assignment) {
            Write-Host "##[debug]🐞`tRole Assignment Exists: Desktop Virtualization User"
            $assignment
        } else {
            Write-Host "##[debug]🐞`tCreating Role Assignment: Desktop Virtualization User"
            New-AzRoleAssignment `
                -RoleDefinitionName "Desktop Virtualization User" `
                -Scope $applicationGroup.id `
                -ObjectId $avdUserGroupID
        }
    }

    ########## VIRTUAL MACHINE ASSIGNMENTS

    $avdHosts = $(Get-AzVM -ResourceGroupName $resourceGroup)

    foreach ( $avdHost in $avdHosts ) {
        switch ($avdHost.Tags.AVD_Type) {
            "Pooled"    {$avdUserID = $(Get-AzADGroup -DisplayName $avdHost.Tags.RBAC_User).id}
            "Personal"  {$avdUserID = $(Get-AzADUser  -UserPrincipalName $avdHost.Tags.RBAC_User).id}
        }
        $avdUserRbacRoles       = @(
            "Virtual Machine User Login"
        )
        # User Role Assignments
        Write-Host "`n`n##[debug]🐞`tVirtual Machine Config 🏗️"
        Write-Host "##[debug]🐞`tAVD Host ID       : $($avdHost.id)"
        Write-Host "##[debug]🐞`tAVD User/Group    : $($avdHost.Tags.RBAC_User)"
        Write-Host "##[debug]🐞`tAVD User/Group ID : $avdUserID"
        Write-Host "##[debug]🐞`tAVD User RBAC Role: $avdUserRbacRoles"

        foreach ( $role in $avdUserRbacRoles) {
            $assignment = Get-AzRoleAssignment `
                -RoleDefinitionName $role `
                -Scope $avdHost.id `
                -ObjectId $avdUserID

            if ($assignment) {
                Write-Host "##[debug]🐞`tRole Assignment Exists: $role"
                $assignment
            } else {
                Write-Host "##[debug]🐞`tCreating Role Assignment: $role"
                New-AzRoleAssignment `
                    -RoleDefinitionName $role `
                    -Scope $avdHost.id `
                    -ObjectId $avdUserID `
                    -ErrorAction SilentlyContinue
            }
        }
    }


    ########## HOST POOL ASSIGNMENTS FOR PERSONAL POOLS

    $avdHosts = $(Get-AzVM -ResourceGroupName $resourceGroup)

    if ($avdHosts.Tags.AVD_Type -eq "Personal") {
        foreach ( $avdHost in $avdHosts ) {

        $avdUserUPN  = $($avdHost.Tags.RBAC_User)
        $avdHostName = $($avdHost.Name)

        # User Role Assignments
        Write-Host "`n`n##[debug]🐞`tHost Pool Config 🏗️"
        Write-Host "##[debug]🐞`tAVD Host Name : $avdHostName"
        Write-Host "##[debug]🐞`tAVD User UPN  : $avdUserUPN"

        $assignment = (Get-AzWvdSessionHost `
            -HostPoolName $hostPoolName `
            -ResourceGroupName $resourceGroup `
            -Name $avdHostName).AssignedUser

            if ($assignment -eq $avdUserUPN) {
                Write-Host "##[debug]🐞`tRole Assignment Exists: $role"
                $assignment
            } else {
                Write-Host "##[debug]🐞`tCreating Role Assignment: $role"
                Update-AzWvdSessionHost `
                    -HostPoolName $hostPoolName `
                    -ResourceGroupName $resourceGroup `
                    -Name $avdHostName `
                    -AssignedUser $avdUserUPN
            }
        }
    }
}