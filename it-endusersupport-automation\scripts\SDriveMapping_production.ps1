<#
.DESCRIPTION
	This script performs network drive mappings with PowerShell and is auto generated by the intune-drive-mapping-generator (https://intunedrivemapping.azurewebsites.net).
	When executed under SYSTEM authority a scheduled task is created to ensure recurring script execution on each user logon.

.NOTES
	Author: <PERSON>, ni<PERSON><PERSON><PERSON> tech: https://tech.nicolonsky.ch
#>

[CmdletBinding()]
Param()

###########################################################################################
# Start transcript for logging
###########################################################################################

Start-Transcript -Path $(Join-Path $env:temp "DriveMapping.log")

###########################################################################################
# Input values from generator
###########################################################################################

$ipAddresses = $(Get-NetIPConfiguration -All).IPv4Address.IPAddress

# SDP (Remote): 10.90.x.x
# London      : 10.89.x.x
# Singapore   : 10.88.x.x
# Baltimore   : 10.91.x.x
# Rietlanden  : 10.92.x.x
# Tokyo       : 10.93.x.x

# Array of servers to ping to determine the nearest server
$servers = @("ukshare.jeragm.com", "jpshare.jeragm.com", "share.jeragm.com")

foreach ( $ipAddress in $ipAddresses ) {
	# Define the share path based on the IP address using regex matching
	switch -regex ($ipAddress) {
		"10.89.\d{1,3}.\d{1,3}$" { $sharePath = "ukshare.jeragm.com"; break } # London Office
		"10.88.\d{1,3}.\d{1,3}$" { $sharePath = "share.jeragm.com"  ; break } # Singapore Office
		"10.91.\d{1,3}.\d{1,3}$" { $sharePath = "share.jeragm.com"  ; break } # Baltimore Office
		"10.92.\d{1,3}.\d{1,3}$" { $sharePath = "ukshare.jeragm.com"; break } # Rietlanden Office
		"10.93.\d{1,3}.\d{1,3}$" { $sharePath = "jpshare.jeragm.com"; break } # Japan Office
	}
}

if ( -not $sharePath ) {
	$pingResults = @{}

	foreach ($server in $servers) {
		$ping = (Test-NetConnection -ComputerName $server).PingReplyDetails.RoundtripTime
		$pingResults[$server] = $ping
		Write-Output "Ping to $server $ping ms"
	}

	$nearestServer = $pingResults.GetEnumerator() | Sort-Object Value | Select-Object -First 1
	$sharePath = $nearestServer.Name
}

Write-Host "Mounting $($sharePath)..."

$driveMappingConfig = @(
    [PSCustomObject]@{
        Path        = "\\$($sharePath)\Share"
        DriveLetter = "S"
        Label       = "Common"
        Id          = 0
        GroupFilter = $null
    },
    [PSCustomObject]@{
        Path        = "\\$($sharePath)\Share"
        DriveLetter = "T"
        Label       = "Common"
        Id          = 1
        GroupFilter = $null
    }
)
$driveMappingConfig

# Override with your Active Directory Domain Name e.g. 'ds.nicolonsky.ch' if you haven't configured the domain name as DHCP option
$searchRoot = ""

# If enabled all mounted PSdrives from filesystem except os drives get disconnected if not specified in drivemapping config
$removeStaleDrives = $false

###########################################################################################
# Helper function to determine a users group membership
###########################################################################################

# Kudos for Tobias RenstrÃ¶m who showed me this!
function Get-ADGroupMembership {
	param(
		[parameter(Mandatory = $true)]
		[string]$UserPrincipalName
	)

	process {

		try {

			if ([string]::IsNullOrEmpty($env:USERDNSDOMAIN) -and [string]::IsNullOrEmpty($searchRoot)) {
				Write-Error "Security group filtering won't work because `$env:USERDNSDOMAIN is not available!"
				Write-Warning "You can override your AD Domain in the `$overrideUserDnsDomain variable"
			}
			else {

				# if no domain specified fallback to PowerShell environment variable
				if ([string]::IsNullOrEmpty($searchRoot)) {
					$searchRoot = $env:USERDNSDOMAIN
				}

				$searcher = New-Object -TypeName System.DirectoryServices.DirectorySearcher
				$searcher.Filter = "(&(userprincipalname=$UserPrincipalName))"
				$searcher.SearchRoot = "LDAP://$searchRoot"
				$distinguishedName = $searcher.FindOne().Properties.distinguishedname
				$searcher.Filter = "(member:1.2.840.113556.1.4.1941:=$distinguishedName)"

				[void]$searcher.PropertiesToLoad.Add("name")

				$list = [System.Collections.Generic.List[String]]@()

				$results = $searcher.FindAll()

				foreach ($result in $results) {
					$resultItem = $result.Properties
					[void]$List.add($resultItem.name)
				}

				$list
			}
		}
		catch {
			#Nothing we can do
			Write-Warning $_.Exception.Message
		}
	}
}

#check if running as system
function Test-RunningAsSystem {
	[CmdletBinding()]
	param()
	process {
		return [bool]($(whoami -user) -match "S-1-5-18")
	}
}


#Testing if groupmembership is given for user
function Test-GroupMembership {
    [CmdletBinding()]
    param (
        $driveMappingConfig,
        $groupMemberships
    )
    try {
        $obj = foreach ($d in $driveMappingConfig) {
            if (-not ([string]::IsNullOrEmpty($($d.GroupFilter)))) {
                foreach ($filter in $($d.GroupFilter)) {
                    if ($groupMemberships -contains $filter) {
                        $d
                    }
                    else {
                        #no match for group
                    }
                }
            }
            else {
                $d 
            }
        }
        $obj
    }
    catch {
        Write-Error "Unknown error testing group memberships: $($_.Exception.Message)"
    }
}

###########################################################################################
# Get current group membership for the group filter capabilities
###########################################################################################

Write-Output "Running as SYSTEM: $(Test-RunningAsSystem)"

if ($driveMappingConfig.GroupFilter) {
	try {
		#check if running as user and not system
		if (-not (Test-RunningAsSystem)) {

			$groupMemberships = Get-ADGroupMembership -UserPrincipalName $(whoami -upn)
		}
	}
	catch {
		#nothing we can do
	}
}
###########################################################################################
# Mapping network drives
###########################################################################################
#Get PowerShell drives and rename properties

if (-not (Test-RunningAsSystem)) {

	$psDrives = Get-PSDrive | Where-Object { $_.Provider.Name -eq "FileSystem" -and $_.Root -notin @("$env:SystemDrive\", "D:\") } `
	| Select-Object @{N = "DriveLetter"; E = { $_.Name } }, @{N = "Path"; E = { $_.DisplayRoot } }

    if ($(nltest /dclist:JERAGM.COM) -eq "The command completed successfully") {
        Write-Output "Kerberos tickets was obtained successfully"
    }
    else {
        Write-Error "Unable to retrieve Kerberos ticket from Domain Controllers"
    }

	# only map drives where group membership applicable
	$driveMappingConfig = Test-GroupMembership -driveMappingConfig $driveMappingConfig -groupMemberships $groupMemberships

	#iterate through all network drive configuration entries
	foreach ($drive in $driveMappingConfig) {
		try {
			#check if variable in unc path exists, e.g. for $env:USERNAME -> resolving
			if ($drive.Path -match '\$env:') {
				$drive.Path = $ExecutionContext.InvokeCommand.ExpandString($drive.Path)
			}

			#if label is null we need to set it to empty in order to avoid error
			if ($null -eq $drive.Label) {
				$drive.Label = ""
			}

			$check = $psDrives | Where-Object { $_.DriveLetter -eq $drive.DriveLetter }
			$mount = $true

			if ( $check ) {
				Write-Output "Drive '$($drive.DriveLetter):\' already assigned, checking path..."

				if ( $drive.Path -eq $check.Path ) {
					Write-Output "Drive '$($drive.DriveLetter):\' '$($drive.Path)' already exists with correct Drive Letter and Path"
					$mount = $false

				} else {
					# Mapped with wrong config -> Delete it
                    Write-Output "Drive '$($drive.DriveLetter):\' has '$($check.Path)' mounted... removing..."
                    $driveMap = Get-PSDrive | Where-Object { $_.Name -eq $drive.DriveLetter }
                    Remove-PSDrive $driveMap -PSProvider FileSystem -Scope Global -erroraction SilentlyContinue | Out-Null
					Remove-SMBMapping $driveMap -Force -erroraction SilentlyContinue | Out-Null
					$mount = $true
				}
			}

			if ( $mount ) {
				Write-Output "Mapping network drive '$($drive.Path)' to '$($drive.DriveLetter):\'"
				$null = New-PSDrive -PSProvider FileSystem -Name $drive.DriveLetter -Root $drive.Path -Description $drive.Label -Persist -Scope global -EA Stop
				(New-Object -ComObject Shell.Application).NameSpace("$($drive.DriveLetter):").Self.Name = $drive.Label
			}
			$regPath      = Get-ChildItem 'HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\MountPoints2'
		    	$propertyName = '_LabelFromReg'
		    	foreach( $child in $regPath ) {
		        	if( $child.PSChildName -eq "$($drive.Path.Replace("\","#"))" ) {
		            		if( !$child.Property.Contains( $propertyName ) ) {
		                		New-ItemProperty $child.PSPath -Name $propertyName -PropertyType String | Out-Null
		            		}
		            		Set-ItemProperty -Path $child.PSPath -Name $propertyName -Value $drive.Label | Out-Null
		        	}
		    	}
		}
		catch {
			$available = Test-Path $($drive.Path)
			if (-not $available) {
				Write-Error "Unable to access path '$($drive.Path)' verify permissions and authentication!"
			}
			else {
				Write-Error $_.Exception.Message
			}
		}
	}

	# Remove unassigned drives
	if ($removeStaleDrives -and $null -ne $psDrives) {
		$diff = Compare-Object -ReferenceObject $driveMappingConfig -DifferenceObject $psDrives -Property "DriveLetter" -PassThru | Where-Object { $_.SideIndicator -eq "=>" }
		foreach ($unassignedDrive in $diff) {
			Write-Warning "Drive '$($unassignedDrive.DriveLetter)' has not been assigned - removing it..."
			Remove-SmbMapping -LocalPath "$($unassignedDrive.DriveLetter):" -Force -UpdateProfile
		}
	}

	# Fix to ensure drives are mapped as persistent!
	$null = Get-ChildItem -Path HKCU:\Network -ErrorAction SilentlyContinue | ForEach-Object { New-ItemProperty -Name ConnectionType -Value 1 -Path $_.PSPath -Force -ErrorAction SilentlyContinue }
}

###########################################################################################
# End & finish transcript
###########################################################################################

Stop-transcript

###########################################################################################
# Done
###########################################################################################

#!SCHTASKCOMESHERE!#

###########################################################################################
# If this script is running under system (IME) scheduled task is created  (recurring)
###########################################################################################

if (Test-RunningAsSystem) {

	Start-Transcript -Path $(Join-Path -Path $env:temp -ChildPath "IntuneDriveMappingScheduledTask.log")
	Write-Output "Running as System --> creating scheduled task which will run on user logon"

	###########################################################################################
	# Get the current script path and content and save it to the client
	###########################################################################################

	$currentScript = Get-Content -Path $($PSCommandPath)

	$schtaskScript = $currentScript[(0) .. ($currentScript.IndexOf("#!SCHTASKCOMESHERE!#") - 1)]

	$scriptSavePath = $(Join-Path -Path $env:ProgramData -ChildPath "intune-drive-mapping-generator")

	if (-not (Test-Path $scriptSavePath)) {

		New-Item -ItemType Directory -Path $scriptSavePath -Force
	}

	$scriptSavePathName = "DriveMapping.ps1"

	$scriptPath = $(Join-Path -Path $scriptSavePath -ChildPath $scriptSavePathName)

	$schtaskScript | Out-File -FilePath $scriptPath -Force

	###########################################################################################
	# Create dummy vbscript to hide PowerShell Window popping up at logon
	###########################################################################################

	$vbsDummyScript = "
	Dim shell,fso,file

	Set shell=CreateObject(`"WScript.Shell`")
	Set fso=CreateObject(`"Scripting.FileSystemObject`")

	strPath=WScript.Arguments.Item(0)

	If fso.FileExists(strPath) Then
		set file=fso.GetFile(strPath)
		strCMD=`"powershell -nologo -executionpolicy ByPass -command `" & Chr(34) & `"&{`" &_
		file.ShortPath & `"}`" & Chr(34)
		shell.Run strCMD,0
	End If
	"

	$scriptSavePathName = "IntuneDriveMapping-VBSHelper.vbs"

	$dummyScriptPath = $(Join-Path -Path $scriptSavePath -ChildPath $scriptSavePathName)

	$vbsDummyScript | Out-File -FilePath $dummyScriptPath -Force

	$wscriptPath = Join-Path $env:SystemRoot -ChildPath "System32\wscript.exe"

	###########################################################################################
	# Register a scheduled task to run for all users and execute the script on logon
	###########################################################################################

	$schtaskName = "IntuneDriveMapping"
	$schtaskDescription = "Map network drives from intune-drive-mapping-generator."

	$trigger = New-ScheduledTaskTrigger -AtLogOn

	$class = Get-cimclass MSFT_TaskEventTrigger root/Microsoft/Windows/TaskScheduler
	$trigger2 = $class | New-CimInstance -ClientOnly
	$trigger2.Enabled = $True
	$trigger2.Subscription = '<QueryList><Query Id="0" Path="Microsoft-Windows-NetworkProfile/Operational"><Select Path="Microsoft-Windows-NetworkProfile/Operational">*[System[Provider[@Name=''Microsoft-Windows-NetworkProfile''] and EventID=10002]]</Select></Query></QueryList>'

	$trigger3 = $class | New-CimInstance -ClientOnly
	$trigger3.Enabled = $True
	$trigger3.Subscription = '<QueryList><Query Id="0" Path="Microsoft-Windows-NetworkProfile/Operational"><Select Path="Microsoft-Windows-NetworkProfile/Operational">*[System[Provider[@Name=''Microsoft-Windows-NetworkProfile''] and EventID=4004]]</Select></Query></QueryList>'

	#Execute task in users context
	$principal= New-ScheduledTaskPrincipal -GroupId "S-1-5-32-545" -Id "Author"

	#call the vbscript helper and pass the PosH script as argument
	$action = New-ScheduledTaskAction -Execute $wscriptPath -Argument "`"$dummyScriptPath`" `"$scriptPath`""

	$settings= New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries

	$null=Register-ScheduledTask -TaskName $schtaskName -Trigger $trigger,$trigger2,$trigger3 -Action $action  -Principal $principal -Settings $settings -Description $schtaskDescription -Force

	Start-ScheduledTask -TaskName $schtaskName
	Stop-Transcript
}

###########################################################################################
# Done
###########################################################################################