#Requires -Version 5.1

<#
.SYNOPSIS
JML Email Management Module

.DESCRIPTION
This module provides email functionality for the JML (Jo<PERSON>, Mover, Leaver) 
admin account management script. It handles email notifications, templates,
retry logic, and SMTP operations with comprehensive error handling.

.NOTES
Version:        2.0
Author:         <PERSON>
Creation Date:  2025-01-09
Security Level: Enhanced with comprehensive data protection

Dependencies:
- PowerShell 5.1 or higher
- JML-Configuration module
- JML-Security module
- JML-Logging module
- JML-Utilities module
#>

# Import required modules
Import-Module (Join-Path $PSScriptRoot "JML-Configuration.psm1") -Force
Import-Module (Join-Path $PSScriptRoot "JML-Security.psm1") -Force
Import-Module (Join-Path $PSScriptRoot "JML-Logging.psm1") -Force
Import-Module (Join-Path $PSScriptRoot "JML-Utilities.psm1") -Force

<#
.SYNOPSIS
Sends email with retry logic and comprehensive error handling.

.DESCRIPTION
Implements robust email sending with exponential backoff retry logic,
SSL fallback capabilities, and detailed logging for troubleshooting.

.PARAMETER From
The sender email address.

.PARAMETER To
The recipient email address.

.PARAMETER Subject
The email subject.

.PARAMETER Body
The email body content.

.PARAMETER SmtpServer
The SMTP server to use.

.PARAMETER Attachments
Optional array of file paths to attach.

.PARAMETER UseSSL
Switch to enable SSL/TLS encryption.

.PARAMETER Port
SMTP port number.

.EXAMPLE
Send-EmailWithRetry -From "<EMAIL>" -To "<EMAIL>" -Subject "Test" -Body "Test message" -SmtpServer "smtp.domain.com"

.NOTES
Implements enterprise-grade email delivery with comprehensive error handling.
#>
function Send-EmailWithRetry {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$From,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$To,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$Subject,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$Body,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$SmtpServer,

        [string[]]$Attachments,

        [switch]$UseSSL,

        [int]$Port = 25
    )
    
    # Get configuration
    $config = Get-ModuleConfiguration
    
    $maxRetries = if ($config) { $config.Email.RetrySettings.MaxAttempts } else { 3 }
    $retryDelay = if ($config) { $config.Email.RetrySettings.BaseDelay } else { 2 }
    
    for ($i = 1; $i -le $maxRetries; $i++) {
        try {
            $params = @{
                From       = $From
                To         = $To
                Subject    = $Subject
                Body      = $Body
                SmtpServer = $SmtpServer
                Port      = $Port
                UseSSL    = $UseSSL.IsPresent
            }
            
            if ($Attachments) { 
                $params.Attachments = $Attachments 
            }
            
            # Add error handling for email parameters
            Write-SecureLog -Message "Attempting to send email (Attempt $i of $maxRetries)" -LogLevel "DEBUG"
            Write-SecureLog -Message "Email parameters: To=$To, From=$From, Subject=$Subject, SmtpServer=$SmtpServer" -LogLevel "DEBUG"
            
            Send-MailMessage @params -ErrorAction Stop
            
            Write-SecureLog -Message "Email sent successfully to $To" -LogLevel "INFO"
            return $true
        }
        catch {
            $errorMessage = "Email attempt $i failed: $($_.Exception.Message)"
            Write-SecureLog -Message $errorMessage -LogLevel "WARNING"
            
            if ($_.Exception.Message -match "secure connections") {
                Write-SecureLog -Message "SSL connection failed, attempting without SSL..." -LogLevel "WARNING"
                $params.UseSSL = $false
                try {
                    Send-MailMessage @params -ErrorAction Stop
                    Write-SecureLog -Message "Email sent successfully without SSL to $To" -LogLevel "INFO"
                    return $true
                }
                catch {
                    Write-SecureLog -Message "Failed to send email without SSL: $($_.Exception.Message)" -LogLevel "ERROR"
                }
            }
            
            if ($i -lt $maxRetries) {
                $waitTime = if ($config -and $config.Email.RetrySettings.EnableExponentialBackoff) {
                    $retryDelay * [Math]::Pow(2, $i-1)
                } else {
                    $retryDelay
                }
                Write-SecureLog -Message "Waiting $waitTime seconds before retry..." -LogLevel "INFO"
                Start-Sleep -Seconds $waitTime
            }
        }
    }
    
    Write-SecureLog -Message "Failed to send email after $maxRetries attempts" -LogLevel "ERROR"
    return $false
}

<#
.SYNOPSIS
Sends an email notification with details about the admin account creation.

.DESCRIPTION
Creates and sends formatted email notifications to both support team and end user
with comprehensive logging and audit trail information.

.PARAMETER LogPath
Path to the log file to attach.

.PARAMETER AdminUserUPN
The UPN of the created admin account.

.PARAMETER Password
The generated password for the admin account.

.PARAMETER StandardUserEmail
Email address of the standard user.

.PARAMETER EmailFrom
Optional sender email address override.

.PARAMETER SmtpServer
Optional SMTP server override.

.EXAMPLE
Send-EmailNotification -LogPath "C:\Logs\admin.log" -AdminUserUPN "<EMAIL>" -Password "SecurePass123!" -StandardUserEmail "<EMAIL>"

.NOTES
Implements secure email notifications with comprehensive audit trails.
#>
function Send-EmailNotification {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$LogPath,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$AdminUserUPN,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$Password,

        [Parameter(Mandatory = $true)]
        [ValidatePattern('^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')]
        [string]$StandardUserEmail,

        [ValidateNotNullOrEmpty()]
        [string]$EmailFrom,

        [ValidateNotNullOrEmpty()]
        [string]$SmtpServer
    )

    # Get configuration
    $config = Get-ModuleConfiguration

    # Use configuration defaults if parameters not provided
    if (-not $EmailFrom) {
        $EmailFrom = if ($config) { $config.Email.DefaultFrom } else { "<EMAIL>" }
    }
    if (-not $SmtpServer) {
        $SmtpServer = if ($config) { $config.Email.SmtpServer } else { "smtp.domain.com" }
    }

    # Retrieve the full name and username of the current user
    $currentUserFullName = Get-CurrentUserName
    $currentUserSamAccountName = whoami

    # Get log content
    try {
        $logContent = Get-Content -Path $LogPath -Raw
        if ([string]::IsNullOrEmpty($logContent)) {
            Write-SecureLog -Message "Warning: Log file is empty" -LogLevel "WARNING"
            $logContent = "No log content available."
        }
    }
    catch {
        Write-SecureLog -Message "Error reading log file: $($_.Exception.Message)" -LogLevel "ERROR"
        $logContent = "Error reading log content."
    }

    # Prepare shared email parameters
    $emailParams = @{
        From       = $EmailFrom
        SmtpServer = $SmtpServer
    }

    # Send notification to support team
    $supportEmail = if ($config) { $config.Email.SupportEmail } else { "<EMAIL>" }
    $supportEmailParams = $emailParams.Clone()
    $supportEmailParams += @{
        To         = $supportEmail
        Subject    = "Admin Account Creation for $AdminUserUPN"
        Body       = @"
An admin account has been created for $AdminUserUPN by $currentUserFullName ($currentUserSamAccountName).

DETAILED LOG:
=============
$logContent

This is an automated notification.
"@
        Attachments = $LogPath
        UseSSL     = if ($config) { $config.Email.UseSSL } else { $false }
        Port       = if ($config) { $config.Email.SmtpPort } else { 25 }
    }

    # Send credentials to standard user
    $userEmailParams = $emailParams.Clone()
    $userEmailParams += @{
        To      = $StandardUserEmail
        Subject = "Your Admin Account Credentials"
        Body    = @"
Hello,

Your admin account has been created with the following credentials:

Username: $AdminUserUPN
Password: $Password

Please change this password upon your first login.

CREATION LOG:
============
$logContent

Best regards,
IT Support Team
"@
        UseSSL  = if ($config) { $config.Email.UseSSL } else { $false }
        Port    = if ($config) { $config.Email.SmtpPort } else { 25 }
    }

    # Send emails with retry and capture results
    $results = @(
        Send-EmailWithRetry @supportEmailParams
        Send-EmailWithRetry @userEmailParams
    )

    # Report results
    if ($results -notcontains $false) {
        $message = "Email notifications sent successfully."
    }
    else {
        $message = "Failed to send one or more email notifications."
    }
    
    Write-Host $message
    Write-SecureLog -Message $message -LogLevel "INFO"
}

<#
.SYNOPSIS
Sends an email notification about admin account deletion.

.DESCRIPTION
Creates and sends formatted email notifications to the support team about
admin account deletion with comprehensive logging and audit trail information.

.PARAMETER LogPath
Path to the log file to attach.

.PARAMETER AdminUserUPN
The UPN of the deleted admin account.

.PARAMETER StandardUserEmail
Email address of the standard user.

.PARAMETER EmailFrom
Optional sender email address override.

.PARAMETER SmtpServer
Optional SMTP server override.

.EXAMPLE
Send-DeletionEmailNotification -LogPath "C:\Logs\admin.log" -AdminUserUPN "<EMAIL>" -StandardUserEmail "<EMAIL>"

.NOTES
Implements secure email notifications for account deletion operations.
#>
function Send-DeletionEmailNotification {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$LogPath,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$AdminUserUPN,

        [Parameter(Mandatory = $true)]
        [ValidatePattern('^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')]
        [string]$StandardUserEmail,

        [ValidateNotNullOrEmpty()]
        [string]$EmailFrom,

        [ValidateNotNullOrEmpty()]
        [string]$SmtpServer
    )

    # Get configuration
    $config = Get-ModuleConfiguration

    # Use configuration defaults if parameters not provided
    if (-not $EmailFrom) {
        $EmailFrom = if ($config) { $config.Email.DefaultFrom } else { "<EMAIL>" }
    }
    if (-not $SmtpServer) {
        $SmtpServer = if ($config) { $config.Email.SmtpServer } else { "smtp.domain.com" }
    }

    # Retrieve the full name and username of the current user
    $currentUserFullName = Get-CurrentUserName
    $currentUserSamAccountName = whoami

    # Get log content
    try {
        $logContent = Get-Content -Path $LogPath -Raw
        if ([string]::IsNullOrEmpty($logContent)) {
            Write-SecureLog -Message "Warning: Log file is empty" -LogLevel "WARNING"
            $logContent = "No log content available."
        }
    }
    catch {
        Write-SecureLog -Message "Error reading log file: $($_.Exception.Message)" -LogLevel "ERROR"
        $logContent = "Error reading log content."
    }

    # Send notification to support team
    $supportEmail = if ($config) { $config.Email.SupportEmail } else { "<EMAIL>" }

    $emailParams = @{
        From        = $EmailFrom
        To          = $supportEmail
        Subject     = "Admin Account Deletion for $AdminUserUPN"
        Body        = @"
An admin account has been deleted for $AdminUserUPN by $currentUserFullName ($currentUserSamAccountName).

DETAILED LOG:
=============
$logContent

This is an automated notification.
"@
        SmtpServer  = $SmtpServer
        Attachments = $LogPath
        UseSSL      = if ($config) { $config.Email.UseSSL } else { $false }
        Port        = if ($config) { $config.Email.SmtpPort } else { 25 }
    }

    # Send email with retry
    $result = Send-EmailWithRetry @emailParams

    # Report result
    $message = if ($result) {
        "Deletion email notification sent successfully."
    } else {
        "Failed to send deletion email notification."
    }

    Write-Host $message
    Write-SecureLog -Message $message -LogLevel "INFO"
}

<#
.SYNOPSIS
Sends an email notification about admin account password reset.

.DESCRIPTION
Creates and sends formatted email notifications to both support team and end user
about admin account password reset with comprehensive logging and audit trail information.

.PARAMETER LogPath
Path to the log file to attach.

.PARAMETER AdminUserUPN
The UPN of the reset admin account.

.PARAMETER NewPassword
The new password for the admin account.

.PARAMETER StandardUserEmail
Email address of the standard user.

.PARAMETER EmailFrom
Optional sender email address override.

.PARAMETER SmtpServer
Optional SMTP server override.

.EXAMPLE
Send-ResetEmailNotification -LogPath "C:\Logs\admin.log" -AdminUserUPN "<EMAIL>" -NewPassword "NewPass123!" -StandardUserEmail "<EMAIL>"

.NOTES
Implements secure email notifications for password reset operations.
#>
function Send-ResetEmailNotification {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$LogPath,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$AdminUserUPN,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$NewPassword,

        [Parameter(Mandatory = $true)]
        [ValidatePattern('^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')]
        [string]$StandardUserEmail,

        [ValidateNotNullOrEmpty()]
        [string]$EmailFrom,

        [ValidateNotNullOrEmpty()]
        [string]$SmtpServer
    )

    # Get configuration
    $config = Get-ModuleConfiguration

    # Use configuration defaults if parameters not provided
    if (-not $EmailFrom) {
        $EmailFrom = if ($config) { $config.Email.DefaultFrom } else { "<EMAIL>" }
    }
    if (-not $SmtpServer) {
        $SmtpServer = if ($config) { $config.Email.SmtpServer } else { "smtp.domain.com" }
    }

    # Retrieve the full name and username of the current user
    $currentUserFullName = Get-CurrentUserName
    $currentUserSamAccountName = whoami

    # Get log content
    try {
        $logContent = Get-Content -Path $LogPath -Raw
        if ([string]::IsNullOrEmpty($logContent)) {
            Write-SecureLog -Message "Warning: Log file is empty" -LogLevel "WARNING"
            $logContent = "No log content available."
        }
    }
    catch {
        Write-SecureLog -Message "Error reading log file: $($_.Exception.Message)" -LogLevel "ERROR"
        $logContent = "Error reading log content."
    }

    # Prepare shared email parameters
    $emailParams = @{
        From       = $EmailFrom
        SmtpServer = $SmtpServer
    }

    # Send notification to support team
    $supportEmail = if ($config) { $config.Email.SupportEmail } else { "<EMAIL>" }
    $supportEmailParams = $emailParams.Clone()
    $supportEmailParams += @{
        To         = $supportEmail
        Subject    = "Admin Account Password Reset for $AdminUserUPN"
        Body       = @"
An admin account password has been reset for $AdminUserUPN by $currentUserFullName ($currentUserSamAccountName).

DETAILED LOG:
=============
$logContent

This is an automated notification.
"@
        Attachments = $LogPath
        UseSSL     = if ($config) { $config.Email.UseSSL } else { $false }
        Port       = if ($config) { $config.Email.SmtpPort } else { 25 }
    }

    # Send new credentials to standard user
    $userEmailParams = $emailParams.Clone()
    $userEmailParams += @{
        To      = $StandardUserEmail
        Subject = "Your Admin Account Password Has Been Reset"
        Body    = @"
Hello,

Your admin account password has been reset. Your new credentials are:

Username: $AdminUserUPN
New Password: $NewPassword

Please change this password upon your next login.

RESET LOG:
==========
$logContent

Best regards,
IT Support Team
"@
        UseSSL  = if ($config) { $config.Email.UseSSL } else { $false }
        Port    = if ($config) { $config.Email.SmtpPort } else { 25 }
    }

    # Send emails with retry and capture results
    $results = @(
        Send-EmailWithRetry @supportEmailParams
        Send-EmailWithRetry @userEmailParams
    )

    # Report results
    if ($results -notcontains $false) {
        $message = "Reset email notifications sent successfully."
    }
    else {
        $message = "Failed to send one or more reset email notifications."
    }

    Write-Host $message
    Write-SecureLog -Message $message -LogLevel "INFO"
}

# Export functions
Export-ModuleMember -Function Send-EmailWithRetry, Send-EmailNotification, Send-DeletionEmailNotification, Send-ResetEmailNotification
