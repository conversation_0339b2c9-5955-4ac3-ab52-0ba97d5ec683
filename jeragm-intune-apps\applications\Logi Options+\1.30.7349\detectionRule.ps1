$AppName = "Logi Options+" # DisplayName in Add/Remove Programs
$AppVersion = "1.30.7349" # DisplayVersion of the App in Add/Remove Programs

# Gather all the apps in the Add/Remove Programs Registry Keys
$Apps = (Get-ChildItem HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\) | Get-ItemProperty | select DisplayName, DisplayVersion, WindowsInstaller
$Apps += (Get-ChildItem HKLM:\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\) | Get-ItemProperty | select DisplayName, DisplayVersion, WindowsInstaller

# Check is the App DisplayName is found and the version in the registry is greater than or equal to the specified AppVersion
$AppFound = $Apps | Where-Object {
	($_.DisplayName -like $AppName) -and ([version]$_.DisplayVersion -ge [version]$AppVersion)
}

# Post some output if the app is found
if ($AppFound) {
	Write-Output "Detected"
	Exit 0
} else {
    Exit 1
}