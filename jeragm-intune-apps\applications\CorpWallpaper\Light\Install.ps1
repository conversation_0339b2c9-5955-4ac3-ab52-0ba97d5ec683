function Set-Wallpaper($MyWallpaper){
$code = @' 
using System.Runtime.InteropServices; 
namespace Win32{ 
    
        public class Wallpaper{ 
        [DllImport("user32.dll", CharSet=CharSet.Auto)] 
            static extern int SystemParametersInfo (int uAction , int uParam , string lpvParam , int fuWinIni) ; 
            
            public static void SetWallpaper(string thePath){ 
            SystemParametersInfo(20,0,thePath,3); 
            }
    }
    } 
'@

add-type $code 
[Win32.Wallpaper]::SetWallpaper($MyWallpaper)
}

$Url = "https://jgseaprodeucsa.blob.core.windows.net/public/JERAGM_Light.png"
$DownloadedSourceImage = "C:\Users\<USER>\LightWallPaper.png"

$SourceImage = New-Object System.Net.WebClient
$SourceImage.DownloadFile($Url, $DownloadedSourceImage)

Set-Wallpaper($DownloadedSourceImage)