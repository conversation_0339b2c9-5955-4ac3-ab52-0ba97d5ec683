$version = "2.39.0"

# Refresh System and User Paths
$Env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")  

Try {
    $azureCliVersion = (-join (az version) | convertFrom-Json).'azure-cli'
    If ($azureCliVersion -like "*$version*"){
        Write-Output "Detected"
       Exit 0
    } 
    Exit 1
} 
Catch {
    Exit 1
}