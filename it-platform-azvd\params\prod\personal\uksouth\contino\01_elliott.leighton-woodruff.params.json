{
    "p_hosts": {
        "value": [
            {
                "properties": {
                    "adminUserName"   : "jeragmadm",
                    "availabilityZone": true,
                    "azvdType"        : "Personal",
                    "hostNumber"      : 1,
                    "hostpoolName"    : "consultants-personal",
                    "hostSku"         : "Standard_D4s_v3",
                    "userName"        : "<EMAIL>"
                },
                "storageProfile": {
                    "osDisk": {
                        "createOption": "FromImage", // FromImage creates via imageReference below, <PERSON><PERSON><PERSON> creates via imageId
                        "imageId"     : "",
                        "type"        : "Premium_LRS"
                    },
                    "imageReference": {
                        "offer"    : "office-365",
                        "publisher": "microsoftwindowsdesktop",
                        "sku"      : "21h1-evd-o365pp",
                        "version"  : "latest"
                    }
                },
                "networking": {
                    "resourceGroup"     : "uks-prd-ssv-networking-rg",
                    "subnetName"        : "azure-virtual-desktop-snet",
                    "virtualNetworkName": "uks-prd-ssv-spoke-vnet"
                }
            }
        ]
    }
}