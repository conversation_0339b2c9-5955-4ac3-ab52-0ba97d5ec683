# [CmdletBinding()]
param (
    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$deploymentName,

    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$subscriptions,

    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$primaryRegion,

    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$location
)

Unregister-PSRepository -Name PSGallery
Register-PSRepository -Default
Set-PSRepository -Name PSGallery -InstallationPolicy Trusted

Write-Host "Install Az PowerShell modules"
Install-Module -Name Az.Compute -AllowClobber -Confirm:$false -Force
Install-Module -Name Az.Resources -AllowClobber -Confirm:$false -Force

$body = @{
    Grant_Type    = "client_credentials"
    resource      = "https://graph.microsoft.com"
    client_id     = $env:servicePrincipalId
    client_secret = $env:servicePrincipalKey
}

$graph = Invoke-RestMethod `
    -Uri "https://login.microsoft.com/$($env:tenantId)/oauth2/token?api-version=1.0" `
    -Method POST `
    -Body $body

$accessToken = ($graph.access_token |ConvertTo-SecureString -AsPlainText -Force)

Connect-MgGraph -AccessToken $accessToken

# LOCATION SWITCH
switch ($location)
{
    southeastasia {
        $deploymentLocation = "South East Asia"
        $subscription = $subscriptions.Split(',') | Where-Object { $_ -match "sea" }
    }
    eastasia      {
        $deploymentLocation = "East Asia"
        $subscription = $subscriptions.Split(',') | Where-Object { $_ -match "eaa" }
    }
    uksouth       {
        $deploymentLocation = "UK South"
        $subscription = $subscriptions.Split(',') | Where-Object { $_ -match "uks" }
    }
    ukwest        {
        $deploymentLocation = "UK West"
        $subscription = $subscriptions.Split(',') | Where-Object { $_ -match "ukw" }
    }
}

    ########## INTUNE PRIMARY DEVICE FOR PERSONAL POOLS

    Write-Host "`n`n##[command]`tSet-AzContext -Subscription $($subscription) 🚶"
    Set-AzContext -Subscription $subscription

    $tenantId         = $(Get-AzContext).tenant.id
    $subscriptionId   = $(Get-AzContext).subscription.id

    Write-Host "##[debug]🐞`tTenant ID          : $tenantId"
    Write-Host "##[debug]🐞`tSubscription       : $subscription"
    Write-Host "##[debug]🐞`tSubscription ID    : $subscriptionId"
    Write-Host "##[debug]🐞`tDeployment Location: $deploymentLocation"
    Write-host "##[debug]🐞`tDeployment Name    : $deploymentName"

    # Get the outputs from the deployment
    $outputs                 = (Get-AzDeployment -Name $deploymentName).Outputs

    $resourceGroup           = $outputs["o_resourceGroupName"].Value

    $avdHosts = $(Get-AzVM -ResourceGroupName $resourceGroup)

    if ($avdHosts.Tags.AVD_Type -eq "Personal") {
        foreach ( $avdHost in $avdHosts ) {

        $avdUserUPN  = $($avdHost.Tags.RBAC_User)
        $avdHostName = $($avdHost.Name)
        #Get the Intune Device ID
        $IntuneDeviceID = (Get-MgDeviceManagementManagedDevice -filter "deviceName eq '${avdHostName}'" -all).id
        $Userid = (Get-MgUser -filter "userPrincipalName eq '$avdUserUPN'" -all).id

        # Primary Device Assignments
        Write-Host "`n`n##[debug]🐞`tHost Pool Config 🏗️"
        Write-Host "##[debug]🐞`tAVD Host Name     : $avdHostName"
        Write-Host "##[debug]🐞`tAVD User UPN      : $avdUserUPN"
        Write-Host "##[debug]🐞`tIntune Device ID  : $IntuneDeviceID"

        $uri = "https://graph.microsoft.com/beta/deviceManagement/managedDevices('$IntuneDeviceID')/users/`$ref"
        $Body = @{ "@odata.id" = "https://graph.microsoft.com/beta/users/$Userid" } | ConvertTo-Json
        $Method = "POST"

        $ErrorActionPreference = 'SilentlyContinue'

        Invoke-MgGraphRequest -Method $Method -uri $uri -body $Body

        Write-Host "User $Userid has been assigned to device $IntuneDeviceID"
        }
    }