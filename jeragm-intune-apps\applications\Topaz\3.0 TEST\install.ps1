# Set the environment and URL
$environment = "Topaz TEST"
$version     = "3, 0, 0, 0"
$topazUrl    = "'https://topaz-test.jeragm.internal/getdown'"

# Get the current user's AppData\local folder
$localAppData = [System.Environment]::GetFolderPath("LocalApplicationData")

# Add Topaz and the environment to the path
$installationPath = $localAppData + "\$environment"

# Install Topaz
Start-Process -FilePath ".\Topaz-Installer-3.0.exe" `
    -ArgumentList "/S /URL=$topazURL /XLARCH=64 /D=$installationPath" -Wait

# Intune can't check data in the user's profile, so we'll create a file in the public profile
# Check if Topaz installed correctly
$fileLocation = "$installationPath" + "\Client\topaz.exe"

Try {
    $getVersion = (Get-Item $fileLocation).VersionInfo.ProductVersion
    If ($getVersion -like "*$version*"){
        $topazInstalledFile = "C:\Users\<USER>\${environment} ${version} Installed successfully.txt"
        Write-Host "Topaz installed successfully"
        New-Item -Path $topazInstalledFile -ItemType File -Force
       Exit 0
    } 
    Exit 1
} 
Catch {
    Exit 1
}