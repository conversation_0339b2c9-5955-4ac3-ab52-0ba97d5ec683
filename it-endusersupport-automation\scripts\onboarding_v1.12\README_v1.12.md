# JML Admin Account Management System v1.12

## Overview

The JML (<PERSON><PERSON>, Mover, Leaver) Admin Account Management System is a comprehensive, enterprise-grade PowerShell solution for managing admin accounts in Active Directory environments. Version 1.12 introduces a fully modular architecture with enhanced security, comprehensive audit trails, and zero-configuration deployment capabilities.

## Key Features

### 🏗️ **Modular Architecture**
- **8 specialized modules** for focused functionality
- **Clear separation of concerns** for maintainability
- **Dependency management** with proper load order
- **Reusable components** across multiple scripts

### 🔒 **Enterprise Security**
- **Comprehensive data redaction** (UPNs, emails, DNs, servers)
- **Multi-method credential storage** (SecretManagement, CredentialManager, EncryptedFile)
- **Input validation and sanitization** to prevent injection attacks
- **SHA256 hashing with salt** for audit trail purposes
- **Secure logging** with automatic PII protection

### 🎫 **Full Jira Integration**
- **Automated ticket processing** with validation
- **Rich comment formatting** (ADF and Wiki markup)
- **Secure file attachments** with validation
- **Comprehensive error handling** and retry logic
- **Rate limiting and jitter** for API stability

### 📧 **Robust Email Notifications**
- **Retry logic with exponential backoff**
- **SSL fallback capabilities**
- **Template-based notifications** for all operations
- **Comprehensive error handling**
- **Audit trail integration**

### 📝 **Advanced Logging**
- **Secure logging with data redaction**
- **Comprehensive audit trails** for compliance
- **Configurable log levels** and outputs
- **Log rotation and retention** policies
- **Performance metrics** and timing

### ⚡ **Performance Optimizations**
- **AD query caching** for improved performance
- **Optimized property retrieval** for large domains
- **Timeout handling** and resource cleanup
- **Progress indicators** for long operations

### 🛠️ **Zero-Configuration Deployment**
- **Intelligent environment detection**
- **Auto-generation of configuration files**
- **Self-contained execution** with automatic dependency detection
- **Graceful fallback mechanisms**

## File Structure

```
it-endusersupport-automation\scripts\Onboarding_v2\
├── JML_v1.12.ps1                    # Main entry point script
├── AdminAccountConfig.psd1           # Configuration data file
├── README_v1.12.md                  # This documentation
├── MODULARIZATION_GUIDE.md          # Implementation guide
├── IMPLEMENTATION_SUMMARY.md        # Summary and next steps
└── Modules\                         # Module directory
    ├── JML-Configuration.psm1       # Configuration management
    ├── JML-Security.psm1            # Security and credential management
    ├── JML-Logging.psm1             # Secure logging with audit trails
    ├── JML-Utilities.psm1           # General utility functions
    ├── JML-ActiveDirectory.psm1     # AD operations and user management
    ├── JML-Email.psm1               # Email notifications and SMTP
    ├── JML-Jira.psm1                # Jira integration and API operations
    └── JML-Setup.psm1               # Setup and environment validation
```

## Quick Start

### 1. **First-Time Setup**
```powershell
# Run setup to validate environment and configure credentials
.\JML_v1.12.ps1 -RunSetup
```

### 2. **Basic Usage**
```powershell
# Interactive mode with full menu
.\JML_v1.12.ps1

# Show version information
.\JML_v1.12.ps1 -ShowVersion

# Skip Jira integration for testing
.\JML_v1.12.ps1 -SkipJiraIntegration
```

### 3. **Advanced Usage**
```powershell
# Custom configuration file
.\JML_v1.12.ps1 -ConfigPath ".\MyConfig.psd1"

# Debug logging
.\JML_v1.12.ps1 -LogLevel DEBUG

# Combined options
.\JML_v1.12.ps1 -LogLevel DEBUG -SkipJiraIntegration
```

## Module Overview

### **JML-Configuration.psm1**
- **Purpose**: Configuration management with intelligent defaults
- **Key Functions**: `Initialize-SmartConfiguration`, `New-DefaultConfiguration`
- **Features**: Zero-config deployment, environment detection, auto-generation

### **JML-Security.psm1**
- **Purpose**: Security functions and credential management
- **Key Functions**: `Protect-SensitiveData`, `Get-SecureCredential`, `Get-StringHash`
- **Features**: Data redaction, multi-method credential storage, input validation

### **JML-Logging.psm1**
- **Purpose**: Secure logging with comprehensive audit trails
- **Key Functions**: `Initialize-SecureLogging`, `Write-SecureLog`
- **Features**: Automatic data redaction, audit trails, log rotation

### **JML-Utilities.psm1**
- **Purpose**: General utility functions
- **Key Functions**: `New-SecurePassword`, `New-StandardUPN`, `Confirm-RequiredModule`
- **Features**: Secure password generation, UPN construction, module management

### **JML-ActiveDirectory.psm1**
- **Purpose**: Active Directory operations with performance optimizations
- **Key Functions**: `Get-OptimizedADUserDetails`, `Get-ValidatedUPN`, `Select-OU`
- **Features**: Query caching, comprehensive validation, OU mapping

### **JML-Email.psm1**
- **Purpose**: Email notifications with retry logic
- **Key Functions**: `Send-EmailWithRetry`, `Send-EmailNotification`
- **Features**: Exponential backoff, SSL fallback, template-based notifications

### **JML-Jira.psm1**
- **Purpose**: Jira integration with comprehensive API handling
- **Key Functions**: `Initialize-JiraConnection`, `Test-JiraTicketValidation`, `Add-EnhancedJiraComment`
- **Features**: Rich formatting, secure uploads, rate limiting, error categorization

### **JML-Setup.psm1**
- **Purpose**: Setup and environment validation
- **Key Functions**: `Start-JMLSetup`, `Test-Environment`, `Install-RequiredModule`
- **Features**: Automated setup, dependency installation, environment validation

## Configuration

The system uses a comprehensive configuration file (`AdminAccountConfig.psd1`) with the following sections:

- **ScriptSettings**: General behavior and defaults
- **Logging**: Log levels, retention, and data redaction
- **ActiveDirectory**: OU mappings, query optimization, password policy
- **Email**: SMTP settings, retry logic, notifications
- **Jira**: Server URL, custom fields, API settings, attachments
- **Security**: Credential storage, input validation, audit trails
- **UserExperience**: Console colors, progress indicators, prompts

## Security Features

### **Data Protection**
- **UPN Redaction**: `<EMAIL>` → `user***@domain.com`
- **DN Hashing**: Distinguished Names replaced with secure hashes
- **Server Redaction**: URLs replaced with `[SERVER]` placeholders
- **Audit Trail Hashing**: Sensitive data hashed for compliance

### **Credential Management**
1. **SecretManagement Module** (Primary)
2. **Windows Credential Manager** (Fallback)
3. **Encrypted XML Files** (Fallback)
4. **Secure Prompts** (Last resort)

### **Input Validation**
- **Pattern Matching**: Regex validation for UPNs, SAM accounts
- **Length Limits**: Configurable maximum input lengths
- **Sanitization**: HTML/script tag removal
- **Injection Prevention**: Comprehensive input cleaning

## Operations

### **Create Admin Account**
1. **User Validation**: Verify standard user exists in AD
2. **Jira Processing**: Extract details from ticket (optional)
3. **OU Selection**: Automatic or manual OU selection
4. **Account Creation**: Generate secure password and create account
5. **Notifications**: Email to user and support team
6. **Jira Update**: Add comment and attach log file

### **Delete Admin Account**
1. **Account Verification**: Confirm admin account exists
2. **Safety Checks**: Require explicit confirmation
3. **Account Deletion**: Remove from Active Directory
4. **Notifications**: Email notification to support team
5. **Audit Logging**: Complete audit trail

### **Reset Admin Password**
1. **Account Verification**: Confirm admin account exists
2. **Password Generation**: Create new secure password
3. **Password Reset**: Update in Active Directory
4. **Notifications**: Email new credentials to user
5. **Jira Update**: Document password reset

## Troubleshooting

### **Common Issues**

**Module Loading Errors**
```powershell
# Validate environment
.\JML_v1.12.ps1 -RunSetup

# Check module status
.\JML_v1.12.ps1 -ShowVersion
```

**Configuration Issues**
```powershell
# Regenerate configuration
Remove-Item .\AdminAccountConfig.psd1
.\JML_v1.12.ps1  # Will auto-generate new config
```

**Credential Problems**
```powershell
# Reconfigure credentials
.\JML_v1.12.ps1 -RunSetup
# Select option 2 (Setup credentials only)
```

**Jira Connection Issues**
```powershell
# Test without Jira
.\JML_v1.12.ps1 -SkipJiraIntegration

# Verify credentials in setup
.\JML_v1.12.ps1 -RunSetup
```

### **Log Analysis**
- **Log Location**: Configured in `AdminAccountConfig.psd1`
- **Data Redaction**: Sensitive information automatically protected
- **Audit Trails**: Complete operation tracking with timestamps
- **Error Details**: Comprehensive error information with context

## Version History

### **v1.12 (2025-01-09)**
- **Modular Architecture**: Complete modularization into 8 specialized modules
- **Enhanced Security**: Comprehensive data redaction and credential management
- **Zero Configuration**: Intelligent defaults with auto-generation
- **Improved Performance**: AD query caching and optimizations
- **Better Error Handling**: Comprehensive retry logic and fallback mechanisms
- **Rich Jira Integration**: ADF formatting and enhanced API handling
- **Setup Utility**: Automated environment validation and configuration

## Support

For issues, questions, or feature requests:

1. **Check Logs**: Review log files for detailed error information
2. **Run Diagnostics**: Use `.\JML_v1.12.ps1 -ShowVersion` for system info
3. **Validate Environment**: Run `.\JML_v1.12.ps1 -RunSetup` to check setup
4. **Review Configuration**: Verify `AdminAccountConfig.psd1` settings

## License

This software is provided as-is for internal use within JERA Global Markets.
All rights reserved.
