﻿Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# Function to display a message box with OK button
function Show-MessageBox ($message, $title, $icon) {
    [System.Windows.Forms.MessageBox]::Show($message, $title, [System.Windows.Forms.MessageBoxButtons]::OK, $icon)
}

# Create the main form
$form = New-Object System.Windows.Forms.Form
$form.Text = "AD Group Memberships Tool"
$form.Size = New-Object System.Drawing.Size(400, 200)
$form.StartPosition = "CenterScreen"

# Create labels, textboxes, and buttons
$labelUPN = New-Object System.Windows.Forms.Label
$labelUPN.Text = "User Principal Name (UPN):"
$labelUPN.Location = New-Object System.Drawing.Point(20, 20)
$labelUPN.AutoSize = $true

$textboxUPN = New-Object System.Windows.Forms.TextBox
$textboxUPN.Location = New-Object System.Drawing.Point(200, 20)
$textboxUPN.Size = New-Object System.Drawing.Size(150, 20)

$checkboxExport = New-Object System.Windows.Forms.CheckBox
$checkboxExport.Text = "Export to CSV"
$checkboxExport.Location = New-Object System.Drawing.Point(20, 60)
$checkboxExport.AutoSize = $true

$buttonRun = New-Object System.Windows.Forms.Button
$buttonRun.Text = "Run"
$buttonRun.Location = New-Object System.Drawing.Point(20, 100)
$buttonRun.Add_Click({
    try {
        $userPrincipalName = $textboxUPN.Text
        $exportToCsv = $checkboxExport.Checked

        # Import the Active Directory module
        Import-Module ActiveDirectory -ErrorAction Stop

        # Get user information
        $user = Get-ADUser -Filter {UserPrincipalName -eq $userPrincipalName} -Properties MemberOf -ErrorAction Stop

        # Check if the user exists
        if ($user -eq $null) {
            Show-MessageBox "User with UPN '$userPrincipalName' not found in Active Directory." "Information" "Information"
            return
        }

        # Get group memberships
        $groupMemberships = $user.MemberOf | Get-ADGroup | Select-Object -ExpandProperty Name

        # Count the number of groups
        $groupCount = $groupMemberships.Count

        # Export to CSV if user chooses to
        if ($exportToCsv) {
            # Display a Save File Dialog
            $saveFileDialog = New-Object System.Windows.Forms.SaveFileDialog
            $saveFileDialog.Filter = "CSV Files (*.csv)|*.csv"
            $saveFileDialog.Title = "Save CSV File"
            
            # Show the dialog and check if the user clicked OK
            if ($saveFileDialog.ShowDialog() -eq 'OK') {
                $exportCsvPath = $saveFileDialog.FileName
                $groupMemberships | ForEach-Object { [PSCustomObject]@{ GroupName = $_ } } | Export-Csv -Path $exportCsvPath -NoTypeInformation -ErrorAction Stop
                Show-MessageBox "Group memberships exported to $exportCsvPath" "Success" "Information"
            } else {
                Show-MessageBox "CSV export canceled. Number of Group Memberships: $groupCount" "Information" "Information"
            }
        } else {
            Show-MessageBox "CSV export skipped. Number of Group Memberships: $groupCount" "Information" "Information"
        }
    } catch {
        Show-MessageBox "Error: $_" "Error" "Error"
    }
})

# Add controls to the form
$form.Controls.Add($labelUPN)
$form.Controls.Add($textboxUPN)
$form.Controls.Add($checkboxExport)
$form.Controls.Add($buttonRun)

# Show the form
$form.ShowDialog()
