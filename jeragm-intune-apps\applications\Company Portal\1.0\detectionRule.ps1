############################################################
# Uses the command line tool to output version
# Works with tools such as Azure CLI, Git and Python
############################################################

$appxPackage = "Microsoft.CompanyPortal_8wekyb3d8bbwe"

# Get all applications matching the appxPackage name
$installedApps = winget list --id $appxPackage --accept-source-agreements --exact

Try {
    If ($installedApps -match "Company Portal"){
        Write-Output "Detected"
        Exit 0
    } 
    else {
        Exit 1
        Write-Output "Not Detected"
    }
} 
Catch {
    Exit 1
}