name         : "OmniAgent"
publisher    : "WithSecure"
appVersion   : "7.19.17"
description  : "OmniAgent"
installExp   : "system"
fileHash     : "D6845999338D511294EC48CCC983525106C612533528561512AC9748C43A1ADB"
featured     : false
supersede    : "none"
assignments  : 
  Intune-Endpoint-Laptop-Autopilot:
  - intent   : "required"
  Intune-Endpoint-Desktop-Autopilot:
  - intent   : "required"
  Intune-Endpoint-AVD:
  - intent   : "required"
dependencies : "none"
commandLine  :
- install    : 'msiexec /i F-Secure_MDR-Installer.msi INSTALL_LICENSE=KDRG-T6DC-JMGP-XBGH-LQA7 INSTALL_PROXY=http://proxy.f-secure.com:443 /qn'
- uninstall  : 'C:\Program Files (x86)\F-Secure\MDR\fs_uninstall_32.exe --silent'