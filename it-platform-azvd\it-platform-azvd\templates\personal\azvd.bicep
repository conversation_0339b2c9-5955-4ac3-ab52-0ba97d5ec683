// PARAMETERS

param p_locationShort    string
param p_environmentShort string
param p_tags             object
param p_appGroups        array
param p_hostPool         object
param p_serviceCode      string
param p_azvdLocation     string
param p_expirationTime   string = dateTimeAdd(utcNow('u'), 'P7D', 'yyyy-MM-dd THH:mm:ss zzzz')

//VARIABLES


//RESOURCES

// Create an host pool
resource hostPool 'Microsoft.DesktopVirtualization/hostPools@2022-09-09' = {
    name      : '${p_locationShort}-${p_environmentShort}-${p_serviceCode}-azvd-${p_hostPool.hostpoolName}-vdpool' // Example: azvd-ukw-core-contino-hpl
    location  : p_azvdLocation
    properties: {
        customRdpProperty             : p_hostPool.customRdpProperty
        description                   : p_hostPool.description
        friendlyName                  : p_hostPool.friendlyName
        hostPoolType                  : p_hostPool.hostPoolType
        loadBalancerType              : p_hostPool.loadBalancerType
        maxSessionLimit               : p_hostPool.maxSessionLimit
        preferredAppGroupType         : p_hostPool.preferredAppGroupType
        startVMOnConnect              : p_hostPool.startVMOnConnect
        validationEnvironment         : p_hostPool.validationEnvironment
        personalDesktopAssignmentType : p_hostPool.personalDesktopAssignmentType
        registrationInfo              : {
            expirationTime              : p_expirationTime
            registrationTokenOperation  : 'Update'
        }
    }
    tags: union(p_tags, { RBAC_UserGroup: p_hostPool.aadUserGroup })
}

// Create an application group
resource applicationGroup 'Microsoft.DesktopVirtualization/applicationGroups@2022-09-09' = [for appGroup in p_appGroups: {
    name      : '${p_locationShort}-${p_environmentShort}-${p_serviceCode}-azvd-${appGroup.appGroupName}-vdag'  // Example: uks-npr-ssv-azvd-vdag
    location  : p_azvdLocation
    kind      : appGroup.kind
    properties: {
        applicationGroupType: appGroup.applicationGroupType
        description         : appGroup.description
        friendlyName        : appGroup.friendlyName
        hostPoolArmPath     : resourceId('Microsoft.DesktopVirtualization/hostpools', '${hostPool.name}')
    }
    tags: union(p_tags, { RBAC_UserGroup: appGroup.aadUserGroup })
}]

// Outputs the registration key for the hostpool to main.bicep
#disable-next-line use-recent-api-versions // Newer API does not work
output o_hostPoolRegKey string = reference(hostPool.id,'2021-01-14-preview').registrationInfo.token

// Outputs the name of the host Pool to main.bicep
output o_hostPoolName      string = hostPool.name
output o_hostPoolNameShort string = p_hostPool.hostpoolNameShort

output o_applicationGroupName array = [for i in range(0, length(p_appGroups)): {
    name: applicationGroup[i].name
}]

output o_applicationGroupTags array = [for appgroup in p_appGroups: {
    RBAC_UserGroup: appgroup.aadUserGroup
}]
