# [CmdletBinding()]
param (
    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$deploymentName,

    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$subscriptions,

    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$location
)

# LOCATION SWITCH
switch ($location)
{
    southeastasia { 
        $deploymentLocation = "South East Asia"
        $subscription = $subscriptions.Split(',') | Where-Object { $_ -match "sea" }
    }
    eastasia      { 
        $deploymentLocation = "East Asia"
        $subscription = $subscriptions.Split(',') | Where-Object { $_ -match "eaa" }
    }
    uksouth       { 
        $deploymentLocation = "UK South" 
        $subscription = $subscriptions.Split(',') | Where-Object { $_ -match "uks" }
    }
    ukwest        { 
        $deploymentLocation = "UK West" 
        $subscription = $subscriptions.Split(',') | Where-Object { $_ -match "ukw" }
    }
}

# Set the subscription Context
Write-Host "`n`n##[command]`tSet-AzContext -Subscription $($subscription) 🚶"
Set-AzContext -Subscription $subscription

$tenantId       = $(Get-AzContext).tenant.id
$subscriptionId = $(Get-AzContext).subscription.id

Write-Host "##[debug]🐞`tTenant ID           : $tenantId"
Write-Host "##[debug]🐞`tSubscription        : $subscription"
Write-Host "##[debug]🐞`tSubscription ID     : $subscriptionId"
Write-Host "##[debug]🐞`tDeployment Location : $deploymentLocation"
Write-Host "##[debug]🐞`tDeployment Name     : $deploymentName"

# Get the outputs from the deployment
$outputs               = (Get-AzDeployment -Name $deploymentName).Outputs

$resourceGroup         = $outputs["o_resourceGroupName"].Value
$hostname              = $outputs["o_hostname"].Value
$primaryStorageAccount = $outputs["o_storageAccountName"].Value
$containerName         = $outputs["o_containerName"].Value
$fslogixEnabled        = $outputs["o_fslogixEnabled"].Value

switch -Regex ($primaryStorageAccount) {
    "uks"  {
        $secondaryStorageAccount = $primaryStorageAccount.replace('uks','ukw')
    }
    "ukw"  {
        $secondaryStorageAccount = $primaryStorageAccount.replace('ukw','uks')
    }
    "sea"  {
        $secondaryStorageAccount = $primaryStorageAccount.replace('sea','eaa')
    }
    "eaa"  {
        $secondaryStorageAccount = $primaryStorageAccount.replace('eaa','sea')
    }
}

# If fslogixEnabled output is empty, set to true
if (-not $fslogixEnabled) {
    $fslogixEnabled = $true
}

Write-Host "`n`n##[debug]🐞`tTemplate Outputs 🌟"
Write-Host "##[debug]🐞`tResource Group           : $resourceGroup"
Write-Host "##[debug]🐞`tHostname                 : $hostname"
Write-Host "##[debug]🐞`tPrimary Storage Account  : $primaryStorageAccount"
Write-Host "##[debug]🐞`tSecondary Storage Account: $secondaryStorageAccount"
Write-Host "##[debug]🐞`tContainer Name           : $containerName"
Write-Host "##[debug]🐞`tFSLogix Enabled          : $fslogixEnabled"

# Set the local host settings
forEach ($item in $hostname) {
    Write-Host "`n`n##[command]`tInvoke-AzVMRunCommand -ResourceGroupName $resourceGroup -VMname $item -CommandId `"RunPowerShellScript`" -ScriptPath `"./localHostSettings.ps1`" -Parameter @{primaryStorageAccount = $primaryStorageAccount; secondaryStorageAccount = $secondaryStorageAccount; containerName = $containerName}🚶"
    $configHost = Invoke-AzVMRunCommand `
        -ResourceGroupName $resourceGroup `
        -VMname $item `
        -CommandId "RunPowerShellScript" `
        -ScriptPath "./localHostSettings.ps1" `
        -Parameter @{primaryStorageAccount = $primaryStorageAccount; secondaryStorageAccount = $secondaryStorageAccount; containerName = $containerName; fslogixEnabled = $fslogixEnabled}

    $configHost
}