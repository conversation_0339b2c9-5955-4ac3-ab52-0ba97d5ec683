#################################################################################
# Uses the ProductVersion property in the file                                  #
# Works with any program that stores the product version in the executable      #
#################################################################################

$version = "104.0.1"

Try {
    $getVersion = (Get-Item "C:\Program Files\Mozilla Firefox\firefox.exe").VersionInfo.ProductVersion
    If ($getVersion -like "*$version*"){
        Write-Output "Detected"
       Exit 0
    } 
    Exit 1
} 
Catch {
    Exit 1
}