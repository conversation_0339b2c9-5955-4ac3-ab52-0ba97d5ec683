﻿<#
.SYNOPSIS
Creates an admin account based on a standard user's details.

.DESCRIPTION
This script automates the process of creating an admin account in Active Directory based on the details of a standard user. It includes validation of user inputs, logging actions, generating passwords, and sending email notifications.

.NOTES
Version:        1.0
Author:         <PERSON>
Creation Date:  2023-04-01
#>

# Import required modules
Import-Module ActiveDirectory

# Retrieves the full name of the current user
function GetCurrentUserName {
    try {
        $currentUserSamAccountName = whoami
        $currentUser = Get-ADUser -Identity $currentUserSamAccountName.Split('\')[1] -Properties GivenName, Surname
        return "$($currentUser.GivenName) $($currentUser.Surname)"
    } catch {
        return "Unknown User"
    }
}

# Writes a log message with a timestamp and the current user's name
function Write-Log {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Message,

        [string]$Path = "C:\Temp\Scripts\Desktop Support\Logs\AdminAccountCreation.log"
    )

    # Get the current date and time
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

    # Retrieve the full name and username of the current user
    $currentUserFullName = GetCurrentUserName
    $currentUserSamAccountName = whoami

    # Construct the log message
    $logMessage = "[$timestamp] [$currentUserFullName - $currentUserSamAccountName] $Message"

    # Write the log message to the log file and display it in the console
    Add-Content -Path $Path -Value $logMessage
    Write-Host $logMessage
}

#Gets upn of standard acocunt to model 
function Get-ValidUPN {
    do {
        $upn = Read-Host "Enter the UPN of the standard account. E.g., user.user"
        if (-not $upn -match "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$") {
            Write-Host "Invalid UPN format. Please enter a valid UPN."
            Write-Log "Invalid UPN format entered: $upn"
            $isValid = $false
        } else {
            $isValid = $true
            if (-not $upn -like "*@jeragm.com") {
                $upn += "@jeragm.com"
                Write-Log "Appended domain to UPN: $upn"
            }
        }
    } while (-not $isValid)
    return $upn
}

#Checks standard acocunt is valid
function Get-ADUserDetails {
    param (
        [string]$UserUPN
    )
    try {
        $user = Get-ADUser -Filter "UserPrincipalName -eq '$UserUPN'" -Properties *
        Write-Log "Retrieved AD user details for UPN: $UserUPN"
        return $user
    } catch {
        Write-Host "Failed to find the user or connect to AD. Error: $_"
        Write-Log "Failed to retrieve AD user details for UPN: $UserUPN. Error: $_"
        return $null
    }
}

#Selects ths OU the Admin account will be created into
function Select-OU {
    do {
        Write-Host "Select the OU for the admin account: 1 for London, 2 for Singapore."
        $selection = Read-Host "Enter 1 or 2"
        switch ($selection) {
            "1" { $ouPath = "OU=London,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com"; $isValid = $true; Write-Log "Selected OU: London" }
            "2" { $ouPath = "OU=Singapore,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com"; $isValid = $true; Write-Log "Selected OU: Singapore" }
            default {
                Write-Host "Invalid selection. Please select either 1 or 2."
                Write-Log "Invalid OU selection: $selection"
                $isValid = $false
            }
        }
    } while (-not $isValid)
    return $ouPath
}

#Password generator
function Generate-Password {
    Add-Type -AssemblyName System.Web
    $password = [System.Web.Security.Membership]::GeneratePassword(12, 2)
    Write-Log "Generated password"
    return $password
}

# Creates an admin account based on the details of a standard user
function Create-AdminAccount {
  Write-Log "Starting admin account creation process"
  $standardUserUPN = Get-ValidUPN
  $standardUser = Get-ADUserDetails -UserUPN $standardUserUPN
  if ($standardUser -eq $null) {
      Write-Host "Unable to proceed without valid standard user details."
      return
  }

  $ouPath = Select-OU
  $displayName = "$($standardUser.GivenName) $($standardUser.Surname) (Admin Account)"
  $logonName = $standardUser.SamAccountName + "-a"
  $adminUserUPN = "$<EMAIL>"

  if (Get-ADUser -Filter "SamAccountName -eq '$logonName'") {
      Write-Host "An admin account with the same SamAccountName already exists."
      return
  }

  $password = Generate-Password
  try {
      New-ADUser -Name $displayName -GivenName $standardUser.GivenName -Surname $standardUser.Surname -DisplayName $displayName -Description $displayName -UserPrincipalName $adminUserUPN -SamAccountName $logonName -AccountPassword (ConvertTo-SecureString $password -AsPlainText -Force) -Path $ouPath -Enabled $false
      Write-Host "The password for the admin account ($adminUserUPN) is: $password. Note: The account is created as disabled."
  } catch {
      Write-Host "Failed to create the admin account. Error: $_"
  }
}

# Main script execution
Create-AdminAccount

# Add this line at the end of your script
Read-Host -Prompt "Press Enter to exit"