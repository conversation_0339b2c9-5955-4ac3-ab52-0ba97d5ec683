//PARAMETERS

param p_environmentShort   string
param p_locationShort      string
param p_storageAccountName string
param p_hostPool           object

// VARIABLES

//RESOURCES

// Referencing an existing network to be used when creating network adapters
resource storageAccount 'Microsoft.Storage/storageAccounts@2023-01-01' existing  = {
    name  : 'jgm${p_locationShort}${p_environmentShort}${p_storageAccountName}st' //jgmuksprdazvdfslogixst
    scope : resourceGroup('${p_locationShort}-${p_environmentShort}-ssv-azvd-rg') //uks-prd-ssv-azvd-rg
}

// Creating the file share
resource fileshare 'Microsoft.Storage/storageAccounts/fileServices/shares@2023-01-01' = {
    name: toLower('${storageAccount.name}/default/azvd-${p_hostPool.hostpoolName}-profiles') // Creates a share called 'profiles'
    properties: {
        accessTier: 'Hot'
    }
}

output o_storageAccountName string = storageAccount.name
output o_fileShareId        string = fileshare.id
output o_containerName      string = 'azvd-${p_hostPool.hostpoolName}-profiles'
