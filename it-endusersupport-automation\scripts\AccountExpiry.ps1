# Set the email parameters
$to = "<EMAIL>"
$from = "IT_Operations<<EMAIL>>"
$subject = "Expiring AD Accounts"
$expirationEndDate= (Get-Date).AddDays(45)
$expirationStartDate = (Get-Date).AddDays(1)
$AttachFile = "C:\temp\export-expiryandmanager.csv"
$body = "FYI on attached file, please check and notify relevant line managers / senior managers on account expiry dates."


get-aduser -filter {Enabled -eq $true -and PasswordNeverExpires -eq $false -and (AccountExpirationDate -ge $expirationStartDate) -and (AccountExpirationDate -le $expirationEndDate)} -Properties displayname, distinguishedName, city, company, department, EmailAddress, Manager, AccountExpirationDate | Select-Object displayname, distinguishedName, city, company, department, EmailAddress, Manager, AccountExpirationDate | export-csv -path c:\temp\export-expiryandmanager.csv

 

# Send the email
Send-MailMessage -To $to -From $from -Subject $subject -Body $body -SmtpServer smtp.jeragm.com -Attachments $AttachFile

