############################################################
# Uses the command line tool to output version
# Works with tools such as Azure CLI, Git and Python
############################################################

$version = "2691" # only include the build number not the entire version
$appxPackage = "Microsoft.DesktopAppInstaller"

# Get all applications matching the appxPackage name
$installedVersions = (Get-AppxPackage -PackageTypeFilter Main, Bundle, Resource | `
Where-Object {$_.PackageFullName -like "*$appxPackage*"}).version

Try {
    If ($installedVersions -match "$version"){
        Write-Output "Detected"
        Exit 0
    } 
    else {
        Exit 1
        Write-Output "Not Detected"
    }
} 
Catch {
    Exit 1
}