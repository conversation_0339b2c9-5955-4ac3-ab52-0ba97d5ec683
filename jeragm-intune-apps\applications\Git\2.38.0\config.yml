name         : "Git"
publisher    : "Git"
appVersion   : "2.38.0"
description  : "Git is free and open source software for distributed version control : tracking changes in any set of files, usually used for coordinating work among programmers collaboratively developing source code during software development."
installExp   : "system"
featured     : false
supersede    : "2.37.3"
assignments  : 
  Intune-Users-Developers:
  - intent   : "available"
dependencies : "none"
commandLine  :
- install    : 'Git.exe /VERYSILENT /NORESTART'
- uninstall  : '"%ProgramFiles%\Git\unins000.exe" /VERYSILENT /NORESTART'