[CmdletBinding()]
param (
    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$appName,

    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$appVersion,

    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$storageAccount,

    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$storageSAS
)

#Set-PSDebug -Trace 2 #Enable to see debug log
$ErrorActionPreference = "Stop"

# Application base path
Write-Host "Set options for $appName"
$basePath     = $pwd.Path + "\applications"
$appFolder    = "$basePath\$appName"
$outputFolder = "$basePath\output"

# Download application from an Azure Storage Account
Write-Host "Downloading application files"
New-Item -ItemType Directory -Path $basePath\output   -Force 

azcopy cp "https://$storageAccount.blob.core.windows.net/apps/$appName/$appVersion$storageSAS" "$appFolder" --recursive=true
Get-ChildItem "$appFolder\$appVersion"

# Get Application file
Write-Host "Get Application File"

$installPsExists = Test-Path -Path "$appFolder\$appVersion\install.ps1" -PathType Leaf
$setupexe        = Test-Path -Path "$appFolder\$appVersion\setup.exe" -PathType Leaf

if ($installPsExists) { 
    $setupFile = "install.ps1"
}
elseif ($setupexe) {
    $setupFile = "setup.exe"
}
else {
    $setupFile = (Get-ChildItem -Path "$appFolder\$appVersion" -Include *.zip,*.ps1,*.msi,*.exe -Recurse -Exclude "detectionRule.ps1","requirementRule.ps1").Name
}

# Output the $setupFile
Write-Host "Setup file is $setupFile"

Write-Host "Installing required modules"
Install-Module -Name "IntuneWin32App" -Repository "PSGallery" -AllowClobber -Confirm:$false -Force
Install-Module -Name "powershell-yaml" -Repository "PSGallery" -AllowClobber -Confirm:$false -Force

# Fix for IntuneWin32App module bug (https://github.com/aaronparker/packagefactory/issues/43)
$url = "https://github.com/microsoft/Microsoft-Win32-Content-Prep-Tool/archive/refs/tags/v1.8.4.zip"

# Download the zip file
Write-Host "Downloading $url"
Invoke-WebRequest -Uri $url -OutFile "$env:TEMP\Microsoft-Win32-Content-Prep-Tool-1.8.4.zip"

# Extract the zip file
Expand-Archive -Path "$env:TEMP\Microsoft-Win32-Content-Prep-Tool-1.8.4.zip" -DestinationPath $env:TEMP -Force

# Move IntuneWinAppUtil.exe from the extracted folder to the module folder
Write-Host "Moving IntuneWinAppUtil.exe to the module folder"
Move-Item -Path "$env:TEMP\Microsoft-Win32-Content-Prep-Tool-1.8.4\IntuneWinAppUtil.exe" -Destination $env:TEMP


# Connect to Microsoft Graph API
Write-Host "Connect to Microsoft Graph API"
Connect-MSIntuneGraph -TenantID $env:tenantId -ClientID $env:servicePrincipalId -ClientSecret $env:servicePrincipalKey

# Import parameters from config.yml
Write-Host "Get application config"
$appConfig = Get-Content "$basePath\$appName\$appVersion\config.yml" | ConvertFrom-Yaml

Write-Host "Get all apps matching $appName and sort them in descending order, this puts the latest version on top"
$listVersions = Get-IntuneWin32App -DisplayName $appName | Sort-Object -Property displayVersion -Descending
$appVersions  = $listVersions.displayVersion -join ', '
Write-Host "The following versions of $appName exist," $appVersions

# Check for duplicate before pushing app to Intune
$appVersion = $appConfig.appVersion
if ($listVersions.displayVersion -contains $appConfig.appVersion) {
    Write-Output "##vso[task.logissue type=warning] Version $appVersion already exists"
    Write-Host   "##vso[task.complete result=Failed;]DONE"
}

if ( $setupFile -like '*.ps1*') {
    Write-Host "Installing PS2EXE Module"
    Install-Module -Name "ps2exe" -Repository "PSGallery" -AllowClobber -Confirm:$false -Force

    Write-Host "Encapsulating $setupFile into an EXE"
    Invoke-PS2EXE -inputFile "$appFolder\$appVersion\$setupFile" -outputFile "$appFolder\$appVersion\$appName.exe" -Verbose
    $setupFile = "$appName.exe"
}
elseif ($setupFile -like '*.zip*') {
    Write-Host "Extracting $setupFile"
    Expand-Archive -LiteralPath "$appFolder\$appVersion\$setupFile" -DestinationPath $appFolder\$appVersion -Force
    Remove-Item -LiteralPath "$appFolder\$appVersion\$setupFile" -Force
    Write-Host "Files in $appFolder\$appVersion"
    Get-ChildItem $appFolder\$appVersion

    $ps1File = "$appName.ps1"
    Write-Host "Installing PS2EXE Module"
    Install-Module -Name "ps2exe" -Repository "PSGallery" -AllowClobber -Confirm:$false -Force
    Write-Host "Encapsulating $setupFile into an EXE"
    Invoke-PS2EXE -inputFile "$appFolder\$appVersion\$ps1File" -outputFile "$appFolder\$appVersion\$appName.exe" -Verbose
    $setupFile = "$appName.exe"
}

# Output updated $setupFile
Write-Host "Setup file is now $setupFile"

# Output the contents of the app folder/appVersion
Write-Host "Files in $appFolder\$appVersion"
Get-ChildItem $appFolder\$appVersion

# Create the application package
Write-Host "Create the application package"
New-IntuneWin32AppPackage `
    -SourceFolder $appFolder\$appVersion `
    -SetupFile    $setupFile `
    -OutputFolder $outputFolder

# Check if any intunewin file was created and kill script if not
if (!(Test-Path -Path "$outputFolder\*.intunewin" -PathType Leaf)) {
    Write-Output "##vso[task.logissue type=error] No intunewin file was created"
    Write-Host   "##vso[task.complete result=Failed;]DONE"
}

# Copy the app files to the pipeline
Write-Host "Files in $basePath\$appName\$appVersion"
Get-ChildItem -Path "$basePath\$appName\$appVersion"

Write-Host "Copy app files to pipeline"
Get-ChildItem -Path "$basePath\$appName\$appVersion" -Include *.jpg,*.jpeg,*.png,config.yml,detectionRule.ps1,requirementRule.ps1 -Recurse `
| ForEach-Object { 
    Copy-Item -Path $_.FullName -Destination "$outputFolder"
}

# Output the contents of the output folder
Write-Host "Files in $outputFolder"
Get-ChildItem $outputFolder