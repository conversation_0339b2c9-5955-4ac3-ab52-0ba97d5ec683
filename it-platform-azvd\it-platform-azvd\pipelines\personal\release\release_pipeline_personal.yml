name: '🚀 Bicep Release ${{ parameters.environment }} personal ~ $(Date:yyyy-MM-dd HH-mm) UTC'


pr: none
trigger: none


resources:
  repositories:
  - repository: pipelines
    type      : git
    name      : it/it-devops-pipelines
    ref       : refs/heads/main


parameters:
- name: userName
  displayName: User Name (<EMAIL>)
  default: ""

- name: hostPool 
  displayName: Host Pool
  default: "consultants-personal"
  values:
  - "consultants-personal"
  - "baltimore-personal"

- name: environment # The configuration files in the folder with the same name under ./params will be used
  displayName: Please select your environment
  default: "prod"
  values:
  - "prod"   

- name: location # Location to deploy the route tables
  displayName: Please select your location
  default: "uksouth"
  values:
  - "uksouth"
  - "ukwest"
  - "eastasia"
  - "southeastasia"


variables:
  - template: /params/config.yml


stages:
- template: /pipelines/01_stage/bicep_build.yml@pipelines
  parameters:
    bicepFolder: "personal"

- ${{ if eq(parameters.environment, 'prod') }}:
  - template: /pipelines/01_stage/bicep_test.yml@pipelines
    parameters:
      bicepFolder: "personal"

- template: /pipelines/01_stage/bicep_release.yml@pipelines
  parameters:
    bicepFolder    : "personal"
    # environment lifecycles
    environmentList: ["${{ parameters.environment }}"]
    inputParams    : "@{p_userName='${{ parameters.userName }}'},@{p_hostPoolName='${{ parameters.hostPool }}'},@{p_primaryRegion='${{ parameters.location }}'}"
    locationLoop   : true
    locationList   : 
      - ${{ if or(eq(parameters.location, 'uksouth'), eq(parameters.location, 'ukwest')) }}:
        - uksouth
        - ukwest
      - ${{ if or(eq(parameters.location, 'southeastasia'), eq(parameters.location, 'eastasia')) }}:
        - southeastasia
        - eastasia

- template: /pipelines/01_stage/post_config.yml
  parameters:
    environment   : ${{ parameters.environment }}
    primaryRegion : ${{ parameters.location }}
    locationList  : 
      - ${{ if or(eq(parameters.location, 'uksouth'), eq(parameters.location, 'ukwest')) }}:
        - uksouth
        - ukwest
      - ${{ if or(eq(parameters.location, 'southeastasia'), eq(parameters.location, 'eastasia')) }}:
        - southeastasia
        - eastasia

- ${{ if ne(parameters.environment, 'prod') }}:
  - template: /pipelines/01_stage/bicep_test.yml@pipelines
    parameters:
      bicepFolder: "personal"


