#Requires -Version 5.1

<#
.SYNOPSIS
JML Configuration Management Module

.DESCRIPTION
This module provides configuration management functionality for the JML (<PERSON><PERSON>, Mover, Leaver) 
admin account management script. It handles loading, merging, saving, and validating 
configuration files with intelligent defaults and auto-generation capabilities.

.NOTES
Version:        2.0
Author:         <PERSON>
Creation Date:  2025-01-09
Security Level: Enhanced with comprehensive data protection

Dependencies:
- PowerShell 5.1 or higher
#>

# Module variables
$script:ModuleConfig = $null

<#
.SYNOPSIS
Creates a default configuration for the script when no config file exists.

.DESCRIPTION
Generates intelligent defaults based on the environment and common organizational
patterns. This ensures the script works out-of-the-box without requiring
manual configuration.

.OUTPUTS
Hashtable containing the default configuration structure.

.EXAMPLE
$defaultConfig = New-DefaultConfiguration

.NOTES
This function enables zero-configuration deployment while maintaining
full customization capabilities.
#>
function New-DefaultConfiguration {
    [CmdletBinding()]
    [OutputType([hashtable])]
    param()

    try {
        Write-Verbose "Generating default configuration based on environment detection"

        # Detect domain from current user context
        $currentDomain = try {
            ([System.DirectoryServices.ActiveDirectory.Domain]::GetCurrentDomain()).Name
        } catch {
            "domain.com"  # Fallback
        }

        # Detect common log directories
        $logDirectory = @(
            "C:\Temp\Scripts\Desktop Support\Logs",
            "C:\Logs\AdminScript",
            "$env:TEMP\AdminScript\Logs"
        ) | Where-Object {
            try {
                Test-Path (Split-Path $_ -Parent) -PathType Container
            } catch {
                $false
            }
        } | Select-Object -First 1

        if (-not $logDirectory) {
            $logDirectory = "$env:TEMP\AdminScript\Logs"
        }

        # Generate intelligent defaults
        $defaultConfig = @{
            # Script metadata
            ConfigVersion = "2.0"
            GeneratedOn = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            GeneratedBy = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name

            # General settings with environment detection
            ScriptSettings = @{
                DefaultDomain = $currentDomain
                MaxRetryAttempts = 3
                BaseRetryDelay = 2
                MaxRetryDelay = 30
                OperationTimeout = 300
                ShowProgress = $true
                EnableAuditLogging = $true
                AutoCreateMissingOUs = $false
            }

            # Logging configuration
            Logging = @{
                LogDirectory = $logDirectory
                LogRetentionDays = 30
                MaxLogFileSizeMB = 10
                EnableLogRotation = $true
                FileLogLevels = @('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')
                ConsoleLogLevels = @('INFO', 'WARNING', 'ERROR', 'CRITICAL')
                EnableDataRedaction = $true
                DataRedaction = @{
                    RedactUPNs = $true
                    RedactEmailAddresses = $true
                    RedactDistinguishedNames = $true
                    RedactServerNames = $true
                    HashAlgorithm = "SHA256"
                    HashSalt = "AdminAccountScript2024_$(Get-Random)"
                }
            }

            # Active Directory configuration with common patterns
            ActiveDirectory = @{
                OUMappings = @{
                    "Singapore" = "OU=Singapore,OU=Admins,OU=Accounts,OU=Users,DC=$($currentDomain.Replace('.', ',DC='))"
                    "United Kingdom" = "OU=London,OU=Admins,OU=Accounts,OU=Users,DC=$($currentDomain.Replace('.', ',DC='))"
                    "London" = "OU=London,OU=Admins,OU=Accounts,OU=Users,DC=$($currentDomain.Replace('.', ',DC='))"
                    "Default" = "OU=Admins,OU=Accounts,OU=Users,DC=$($currentDomain.Replace('.', ',DC='))"
                }
                DefaultOU = "OU=Admins,OU=Accounts,OU=Users,DC=$($currentDomain.Replace('.', ',DC='))"
                QueryOptimization = @{
                    UserProperties = @('GivenName', 'Surname', 'DisplayName', 'SamAccountName', 'UserPrincipalName', 'EmailAddress', 'Enabled', 'DistinguishedName', 'Office')
                    MaxResults = 1000
                    QueryTimeout = 30
                    EnableCaching = $true
                    CacheExpirationMinutes = 15
                }
                PasswordPolicy = @{
                    MinLength = 12
                    MinSpecialChars = 2
                    RequireComplexity = $true
                    PasswordNeverExpires = $false
                    ChangePasswordAtLogon = $true
                }
            }

            # Email configuration with common SMTP patterns
            Email = @{
                SmtpServer = "smtp.$currentDomain"
                SmtpPort = 25
                UseSSL = $false
                DefaultFrom = "noreply@$currentDomain"
                SupportEmail = "itsupport@$currentDomain"
                RetrySettings = @{
                    MaxAttempts = 3
                    BaseDelay = 2
                    MaxDelay = 16
                    EnableExponentialBackoff = $true
                }
                Timeout = 30
                EnableNotifications = $true
            }

            # Jira configuration with modern defaults
            Jira = @{
                ServerUrl = "https://yourorg.atlassian.net"  # User must customize
                ExpectedWorkTypes = @{
                    CreateAdmin = "Service Request with Approvals"
                    DeleteAdmin = "Service Request with Approvals"
                    ResetAdmin = "Service Request with Approvals"
                }
                ExpectedRequestTypes = @{
                    CreateAdmin = "Admin Account Request"
                    DeleteAdmin = "Remove Admin Account"
                    ResetAdmin = "Reset Admin Account"
                }
                CustomFields = @{
                    FirstName = "customfield_10304"
                    LastName = "customfield_10305"
                    OfficeLocation = "customfield_10115"
                    ITAdminAccount = "customfield_10453"
                    Department = "customfield_10120"
                    JobTitle = "customfield_10238"
                    ModelAccount = "customfield_10343"
                }
                ApiSettings = @{
                    Timeout = 60
                    RetrySettings = @{
                        MaxAttempts = 5
                        BaseDelay = 1
                        MaxDelay = 32
                        EnableExponentialBackoff = $true
                        JitterEnabled = $true
                    }
                    RateLimit = @{
                        RequestsPerMinute = 60
                        EnableRateLimit = $true
                    }
                }
                AttachmentSettings = @{
                    MaxFileSizeMB = 10
                    AllowedFileTypes = @('.log', '.txt', '.pdf', '.docx', '.xlsx')
                    EnableChunkedUpload = $true
                    ChunkSizeKB = 1024
                    ValidateBeforeUpload = $true
                }
                CommentFormatting = @{
                    PreferADF = $true
                    FallbackToWikiMarkup = $true
                    EnableRichFormatting = $true
                    UseCodeBlocks = $true
                    EnableEmojis = $false
                }
            }

            # Security configuration with secure defaults
            Security = @{
                CredentialStorage = @{
                    PrimaryMethod = "SecretManagement"
                    FallbackMethods = @("CredentialManager", "EncryptedFile", "SecurePrompt")
                    SecretVaultName = "AdminAccountVault"
                    CredentialNames = @{
                        JiraUsername = "AdminScript-JiraUsername"
                        JiraApiToken = "AdminScript-JiraApiToken"
                        SmtpCredentials = "AdminScript-SmtpCredentials"
                    }
                    EncryptedFileSettings = @{
                        FilePath = ".\SecureCredentials.xml"
                        UseUserScope = $true
                        EnableCompression = $true
                    }
                }
                InputValidation = @{
                    EnableStrictValidation = $true
                    MaxInputLength = 256
                    AllowedPatterns = @{
                        UPN = '^[\w][\w\.-]{1,62}[\w]@[\w][\w-]{1,62}\.[a-zA-Z]{2,}$'
                        SamAccountName = '^[a-zA-Z0-9._-]{1,20}$'
                        DisplayName = '^[a-zA-Z0-9\s._-]{1,64}$'
                    }
                    EnableSanitization = $true
                    RemoveHtmlTags = $true
                    RemoveScriptTags = $true
                }
                AuditTrail = @{
                    EnableAuditTrail = $true
                    LogUserIdentity = $true
                    LogFunctionParameters = $true
                    LogExecutionContext = $true
                    AuditRetentionDays = 90
                }
            }

            # User experience configuration
            UserExperience = @{
                ConsoleOutput = @{
                    Colors = @{
                        Success = "Green"
                        Warning = "Yellow"
                        Error = "Red"
                        Information = "Cyan"
                        Debug = "Gray"
                        Progress = "Blue"
                    }
                    EnableColors = $true
                    EnableTimestamps = $true
                    EnableLogLevels = $true
                    EnableProgressBars = $true
                }
                ProgressIndicators = @{
                    EnableProgressBars = $true
                    ShowTimeRemaining = $true
                    ShowPercentage = $true
                    UpdateInterval = 500
                }
                InputPrompts = @{
                    EnableValidationFeedback = $true
                    ShowHelpText = $true
                    EnableAutoCompletion = $false
                    TimeoutSeconds = 300
                }
            }
        }

        Write-Verbose "Default configuration generated successfully"
        return $defaultConfig
    }
    catch {
        Write-Warning "Failed to generate default configuration: $($_.Exception.Message)"
        throw
    }
}

<#
.SYNOPSIS
Initializes configuration with intelligent defaults and auto-generation capabilities.

.DESCRIPTION
Loads configuration from file if available, or generates intelligent defaults based on
environment detection. Creates missing configuration files automatically and provides
self-healing capabilities for corrupted or incomplete configurations.

.PARAMETER ConfigPath
Optional path to the configuration file (.psd1 format). If not provided or file doesn't exist,
intelligent defaults will be generated and optionally saved.

.PARAMETER CreateConfigFile
Switch to automatically create a configuration file with detected defaults.

.EXAMPLE
Initialize-SmartConfiguration
Loads configuration with intelligent defaults.

.EXAMPLE
Initialize-SmartConfiguration -ConfigPath ".\MyConfig.psd1" -CreateConfigFile
Loads from specific path or creates it with defaults.

.NOTES
This function enables zero-configuration deployment while maintaining full customization capabilities.
Security: Only loads data files, never executable code.
#>
function Initialize-SmartConfiguration {
    [CmdletBinding()]
    [OutputType([bool])]
    param(
        [Parameter(Mandatory = $false)]
        [string]$ConfigPath,

        [Parameter(Mandatory = $false)]
        [switch]$CreateConfigFile
    )

    try {
        Write-Progress -Activity "Initializing Script" -Status "Setting up configuration..." -PercentComplete 10

        # Generate default configuration first
        $script:DefaultConfig = New-DefaultConfiguration

        # Determine config file path
        if (-not $ConfigPath) {
            $ConfigPath = Join-Path $PSScriptRoot "..\AdminAccountConfig.psd1"
        }

        $configLoaded = $false
        $config = $null

        # Try to load existing configuration
        if (Test-Path $ConfigPath -PathType Leaf) {
            try {
                Write-Verbose "Loading configuration from: $ConfigPath"
                $config = Import-PowerShellDataFile -Path $ConfigPath -ErrorAction Stop

                # Validate and merge with defaults
                $config = Merge-ConfigurationWithDefaults -UserConfig $config -DefaultConfig $script:DefaultConfig
                $configLoaded = $true

                Write-Host "Configuration loaded from: $ConfigPath" -ForegroundColor Green
            }
            catch {
                Write-Warning "Failed to load configuration file '$ConfigPath': $($_.Exception.Message)"
                Write-Host "Using intelligent defaults instead." -ForegroundColor Yellow
            }
        }

        # Use defaults if no config loaded
        if (-not $configLoaded) {
            Write-Host "No configuration file found. Using intelligent defaults based on environment detection." -ForegroundColor Cyan
            $config = $script:DefaultConfig

            # Offer to create configuration file
            if ($CreateConfigFile -or (Test-InteractiveSession)) {
                $createFile = if ($CreateConfigFile) {
                    $true
                } else {
                    $response = Read-Host "Would you like to create a configuration file for future customization? (Y/N)"
                    $response -eq 'Y' -or $response -eq 'y'
                }

                if ($createFile) {
                    try {
                        Save-ConfigurationFile -Config $config -Path $ConfigPath
                        Write-Host "Configuration file created: $ConfigPath" -ForegroundColor Green
                        Write-Host "You can customize this file for your environment." -ForegroundColor Cyan
                    }
                    catch {
                        Write-Warning "Failed to create configuration file: $($_.Exception.Message)"
                    }
                }
            }
        }

        # Expand environment variables in paths
        if ($config.Logging.LogDirectory) {
            $config.Logging.LogDirectory = [System.Environment]::ExpandEnvironmentVariables($config.Logging.LogDirectory)
        }

        # Set module configuration
        $script:ModuleConfig = $config

        Write-Progress -Activity "Initializing Script" -Status "Configuration ready" -PercentComplete 20
        Write-Verbose "Configuration initialized successfully"
        return $true
    }
    catch {
        Write-Error "Failed to initialize configuration: $($_.Exception.Message)"
        throw
    }
}

<#
.SYNOPSIS
Merges user configuration with intelligent defaults.

.DESCRIPTION
Combines user-provided configuration with generated defaults, ensuring all required
sections exist while preserving user customizations.

.PARAMETER UserConfig
User-provided configuration hashtable.

.PARAMETER DefaultConfig
Default configuration hashtable.

.OUTPUTS
Hashtable containing merged configuration.

.EXAMPLE
$merged = Merge-ConfigurationWithDefaults -UserConfig $userConfig -DefaultConfig $defaults

.NOTES
Performs deep merge to preserve nested user settings while filling gaps with defaults.
#>
function Merge-ConfigurationWithDefaults {
    [CmdletBinding()]
    [OutputType([hashtable])]
    param(
        [Parameter(Mandatory = $true)]
        [hashtable]$UserConfig,

        [Parameter(Mandatory = $true)]
        [hashtable]$DefaultConfig
    )

    try {
        $merged = $DefaultConfig.Clone()

        foreach ($section in $UserConfig.Keys) {
            if ($merged.ContainsKey($section)) {
                if ($UserConfig[$section] -is [hashtable] -and $merged[$section] -is [hashtable]) {
                    # Deep merge for hashtable sections
                    foreach ($key in $UserConfig[$section].Keys) {
                        $merged[$section][$key] = $UserConfig[$section][$key]
                    }
                } else {
                    # Direct replacement for non-hashtable values
                    $merged[$section] = $UserConfig[$section]
                }
            } else {
                # Add new sections from user config
                $merged[$section] = $UserConfig[$section]
            }
        }

        return $merged
    }
    catch {
        Write-Warning "Failed to merge configurations, using defaults: $($_.Exception.Message)"
        return $DefaultConfig
    }
}

<#
.SYNOPSIS
Saves configuration to a PowerShell data file.

.DESCRIPTION
Exports configuration hashtable to a properly formatted .psd1 file with
comments and structure for easy customization.

.PARAMETER Config
Configuration hashtable to save.

.PARAMETER Path
Path where to save the configuration file.

.EXAMPLE
Save-ConfigurationFile -Config $config -Path ".\MyConfig.psd1"

.NOTES
Creates human-readable configuration files with helpful comments.
#>
function Save-ConfigurationFile {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [hashtable]$Config,

        [Parameter(Mandatory = $true)]
        [string]$Path
    )

    try {
        $configContent = @"
# Admin Account Creation Script Configuration
# Generated on: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
# Version: 2.0 - Enhanced Security Edition
#
# This file contains configuration settings for the Admin Account Creation Script.
# Customize the values below to match your environment.
#
# IMPORTANT: This file contains only data, no executable code.
# It is safe to edit and version control.

@{
$(ConvertTo-ConfigString -Object $Config -IndentLevel 1)
}
"@

        # Ensure directory exists
        $directory = Split-Path $Path -Parent
        if ($directory -and -not (Test-Path $directory)) {
            New-Item -ItemType Directory -Path $directory -Force | Out-Null
        }

        # Save configuration file
        Set-Content -Path $Path -Value $configContent -Encoding UTF8 -Force

        Write-Verbose "Configuration saved to: $Path"
    }
    catch {
        Write-Error "Failed to save configuration file: $($_.Exception.Message)"
        throw
    }
}

<#
.SYNOPSIS
Converts hashtable to formatted PowerShell data string.

.DESCRIPTION
Recursively converts hashtable to properly formatted PowerShell data file content
with appropriate indentation and comments.

.PARAMETER Object
Object to convert (hashtable, array, or primitive).

.PARAMETER IndentLevel
Current indentation level for formatting.

.OUTPUTS
String containing formatted PowerShell data representation.

.EXAMPLE
$formatted = ConvertTo-ConfigString -Object $hashtable -IndentLevel 1

.NOTES
Internal helper function for configuration file generation.
#>
function ConvertTo-ConfigString {
    [CmdletBinding()]
    [OutputType([string])]
    param(
        [Parameter(Mandatory = $true)]
        $Object,

        [Parameter(Mandatory = $false)]
        [int]$IndentLevel = 0
    )

    $indent = "    " * $IndentLevel
    $result = ""

    if ($Object -is [hashtable]) {
        foreach ($key in $Object.Keys | Sort-Object) {
            $value = $Object[$key]
            $result += "$indent$key = "

            if ($value -is [hashtable]) {
                $result += "@{`n"
                $result += ConvertTo-ConfigString -Object $value -IndentLevel ($IndentLevel + 1)
                $result += "$indent}`n"
            }
            elseif ($value -is [array]) {
                $result += "@("
                $arrayItems = $value | ForEach-Object {
                    if ($_ -is [string]) { "'$_'" } else { $_ }
                }
                $result += $arrayItems -join ", "
                $result += ")`n"
            }
            elseif ($value -is [string]) {
                $result += "'$value'`n"
            }
            elseif ($value -is [bool]) {
                $result += "`$$value`n"
            }
            else {
                $result += "$value`n"
            }
        }
    }

    return $result
}

<#
.SYNOPSIS
Tests if the current session is interactive.

.DESCRIPTION
Determines if the PowerShell session is interactive (user can respond to prompts)
or non-interactive (automated/scheduled execution).

.OUTPUTS
Boolean indicating if session is interactive.

.EXAMPLE
if (Test-InteractiveSession) { $response = Read-Host "Continue?" }

.NOTES
Used to determine when to prompt users for configuration options.
#>
function Test-InteractiveSession {
    [CmdletBinding()]
    [OutputType([bool])]
    param()

    try {
        # Check if running in ISE, VS Code, or other interactive environments
        $isInteractive = [Environment]::UserInteractive -and
                        -not [Console]::IsInputRedirected -and
                        -not [Console]::IsOutputRedirected

        return $isInteractive
    }
    catch {
        # Default to non-interactive if detection fails
        return $false
    }
}

<#
.SYNOPSIS
Gets the current configuration from the module.

.DESCRIPTION
Returns the currently loaded configuration, or null if no configuration has been loaded.

.OUTPUTS
Hashtable containing the current configuration, or null.

.EXAMPLE
$config = Get-ModuleConfiguration

.NOTES
Used by other modules to access the current configuration.
#>
function Get-ModuleConfiguration {
    [CmdletBinding()]
    [OutputType([hashtable])]
    param()

    return $script:ModuleConfig
}

# Export functions
Export-ModuleMember -Function New-DefaultConfiguration, Initialize-SmartConfiguration, Merge-ConfigurationWithDefaults, Save-ConfigurationFile, ConvertTo-ConfigString, Test-InteractiveSession, Get-ModuleConfiguration
