name         : "Windows Package Manager"
publisher    : "Microsoft"
appVersion   : "Latest"
description  : "The winget command line tool enables users to discover, install, upgrade, remove and configure applications on Windows 10 and Windows 11 computers. This tool is the client interface to the Windows Package Manager service."
installExp   : "system"
featured     : false
supersede    : "none"
assignments  : 
  JERAGM Internal Users:
  - intent   : "available"
  Privileged Accounts:
  - intent   : "available"
dependencies : "none"
commandLine  :
- install    : 'Windows Package Manager.exe'
- uninstall  : 'PowerShell.exe -Command "Get-AppxPackage -PackageTypeFilter Main, Bundle, Resource | Where-Object {$_.PackageFullName -like "*Microsoft.DesktopAppInstaller*"}  | Remove-AppxPackage -Allusers"'

