//PARAMETERS

param p_tags               object
param p_host               object
param p_location           string
param p_locationShort      string
param p_hostPoolName       string
param p_hostPoolNameShort  string
param p_hostPoolRegKey     string
@secure() // Prevents the key from being shown in the output
param p_password           string = '!kYdbvuIrFCGePyHf2aJf!${newGuid()}' // newGuid ensures the password is unique with each run
param p_commandToExecute   string = loadTextContent('../../../it-devops-pipelines/scripts/installCato.ps1')
param p_environmentShort   string
param p_storageAccountName string
param p_hostNumber         string
param p_availabilityZone   string

//VARIABLES
var v_hostNumber            = padLeft(p_hostNumber, 2, '0')
var v_hostname              = '${p_locationShort}PWV${p_hostPoolNameShort}${v_hostNumber}'
var v_storageAccountName    = toLower('jgm${p_locationShort}${p_environmentShort}${p_storageAccountName}st')

//RESOURCES

resource virtualNetwork 'Microsoft.Network/virtualNetworks@2022-01-01' existing  = {
    name  : p_host.virtualNetworkName
    scope : resourceGroup(p_host.resourceGroup)
}

resource subnet 'Microsoft.Network/virtualNetworks/subnets@2022-01-01' existing = {
    name  : p_host.subnetName
    parent: virtualNetwork
}

resource hostNic 'Microsoft.Network/networkInterfaces@2022-01-01' = {
    name    : '${toUpper(v_hostname)}-nic'// Example: UKSPWVAZVDTT01-nic, UKSPWVAZVD is the vmname and TT the team code.
    location: p_location
    tags    : p_tags
    properties: {
       ipConfigurations: [
        {
            name: 'ipconfig'
            properties: {
               privateIPAllocationMethod: 'Dynamic'
               subnet: {
                  id: subnet.id
               }
            }
        }]
    }
}

resource hosts 'Microsoft.Compute/virtualMachines@2022-03-01' = {
    name    : toUpper(v_hostname) // Example: UKSPWVAZVDTT01, UKSPWVAZVD is the vmname and TT the team code.
    location: p_location
    tags    : p_tags
    identity: {
        type: 'SystemAssigned'
    }
    zones: [
        p_availabilityZone
    ]
    properties: {
        licenseType: 'Windows_Client'
        hardwareProfile: {
            vmSize        : p_host.hostSku
        }
        osProfile: {
            computerName  : toUpper(v_hostname) // Example: UKSPWVAZVDTT01, UKSPWVAZVD is the vmname and TT the team code.
            adminUsername : p_host.adminUserName
            adminPassword : p_password
        }
        storageProfile: {
            osDisk: {
                name       : '${toUpper(v_hostname)}-osdisk' // Example: UKSPWVAZVDTT01-osdisk, UKSPWVAZVD is the vmname and TT the team code.
                managedDisk: {
                    storageAccountType: p_host.diskType
                }
                osType: 'Windows'
                createOption: 'FromImage'
                deleteOption: 'Delete'
            }
            imageReference: {
                publisher: p_host.publisher
                offer: p_host.offer
                sku: p_host.sku
                version: p_host.version
            }
        }
        networkProfile: {
            networkInterfaces: [
                {
                    id: resourceId('Microsoft.Network/networkInterfaces', hostNic.name)
                    properties: {
                        deleteOption: 'Delete'
                    }
                }
            ]
        }
        diagnosticsProfile: {
            bootDiagnostics: {
                enabled: true
                storageUri: uri('https://${v_storageAccountName}.blob.${environment().suffixes.storage}', '/')
            }
        }
    }
}

resource vmCustomScriptExtension 'Microsoft.Compute/virtualMachines/extensions@2022-11-01'= {
    parent: hosts
    name: 'config-app'
     location: p_location
     properties: {
      publisher: 'Microsoft.Compute'
      type: 'CustomScriptExtension'
      typeHandlerVersion: '1.10'
      autoUpgradeMinorVersion: true
      protectedSettings: {
       commandToExecute: 'powershell.exe -ExecutionPolicy Unrestricted -Command ${p_commandToExecute}'
      }
     }
}

resource aadDomainJoin 'Microsoft.Compute/virtualMachines/extensions@2022-11-01'= {
    parent: hosts
    name: 'AADLoginForWindows'
    location: p_location
    properties: {
     publisher: 'Microsoft.Azure.ActiveDirectory'
     type: 'AADLoginForWindows'
     typeHandlerVersion: '1.0'
     autoUpgradeMinorVersion: true
     settings: {
      mdmId: '0000000a-0000-0000-c000-************'
     }
    }
    dependsOn: [
        vmCustomScriptExtension
    ]
}

resource joinHostPool 'Microsoft.Compute/virtualMachines/extensions@2022-03-01' = {
    parent: hosts
    name    : 'joinPool'
    location: p_location
    properties: {
        publisher: 'Microsoft.Powershell'
        type: 'DSC'
        typeHandlerVersion: '2.73'
        autoUpgradeMinorVersion: true
        settings: {
        #disable-next-line no-hardcoded-env-urls
            modulesUrl: 'https://wvdportalstorageblob.blob.core.windows.net/galleryartifacts/Configuration_03-30-2022.zip'
            configurationFunction: 'Configuration.ps1\\AddSessionHost'
            properties: {
                hostPoolName: p_hostPoolName
                registrationInfoToken: p_hostPoolRegKey
                aadJoin: true
            }
        }
    }
    dependsOn:[
        aadDomainJoin
    ]
}

// Outputs the name of the host to main.bicep
output o_hostname string = hosts.name
