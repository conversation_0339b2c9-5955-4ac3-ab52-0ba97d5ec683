{"p_appGroups": {"value": [{"applicationGroupType": "Desktop", "description": "Desktop Session for LBdevSP - Pooled.", "friendlyName": "Desktop Session for LBdevSP - LBdevSP (Pooled)", "appgroupName": "lbdevsp-desktop", "aadUserGroup": "AZVD Users - TestBlue - Pooled", "hostpoolName": "lbdevsp-pooled"}, {"applicationGroupType": "RemoteApp", "description": "RemoteApps for LBdevSP - Pooled.", "friendlyName": "RemoteApps for LBdevSP - LBdevSP (Pooled)", "appgroupName": "lbdevsp-remoteapp", "aadUserGroup": "AZVD Users - TestBlue - Pooled", "hostpoolName": "lbdevsp-pooled"}]}, "p_remoteApps": {"value": [{"applicationName": "remote-desktop", "description": "Remote Desktop Connection", "friendlyName": "Remote Desktop Connection", "filePath": "C:\\Windows\\System32\\mstsc.exe", "hostpoolName": "lbdevsp-pooled"}]}, "p_hostPools": {"value": [{"customRdpProperty": "drivestoredirect:s:;audiomode:i:0;videoplaybackmode:i:1;redirectclipboard:i:0;redirectprinters:i:0;devicestoredirect:s:;redirectcomports:i:0;redirectsmartcards:i:0;usbdevicestoredirect:s:;enablecredsspsupport:i:1;use multimon:i:1;autoreconnection enabled:i:1;bandwidthautodetect:i:1;networkautodetect:i:1;compression:i:1;audiocapturemode:i:1;encode redirected video capture:i:1;redirected video capture encoding quality:i:2;camerastoredirect:s:*;selectedmonitors:s:;maximizetocurrentdisplays:i:1;singlemoninwindowedmode:i:1;screen mode id:i:1;smart sizing:i:1;dynamic resolution:i:1;desktopscalefactor:i:100;targetisaadjoined:i:1;enablerdsaadauth:i:1;redirectwebauthn:i:1;", "description": "Azure Virtual Desktop Host Pool for LBdevSP - Pooled.", "friendlyName": "JERAGM Host Pool - LBdevSP (Pooled)", "hostPoolType": "<PERSON>d", "loadBalancerType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maxSessionLimit": 4, "personalDesktopAssignmentType": "Direct", "preferredAppGroupType": "Desktop", "startVMOnConnect": false, "hostpoolName": "lbdevsp-pooled", "hostpoolNameShort": "azvdlds", "validationEnvironment": false, "aadUserGroup": "AZVD Users - LBdevSP - Pooled"}]}, "p_scalingPlans": {"value": [{"enabled": true, "timeZone": "GMT Standard Time", "hostpoolName": "lbdevsp-pooled", "schedules": [{"name": "Weekdays", "daysOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"], "rampUpStartTime": {"hour": 8, "minute": 0}, "peakStartTime": {"hour": 9, "minute": 0}, "rampDownStartTime": {"hour": 19, "minute": 0}, "offPeakStartTime": {"hour": 20, "minute": 0}, "rampUpLoadBalancingAlgorithm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rampUpMinimumHostsPct": 20, "rampUpCapacityThresholdPct": 60, "rampDownLoadBalancingAlgorithm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rampDownMinimumHostsPct": 10, "rampDownCapacityThresholdPct": 90, "rampDownWaitTimeMinutes": 30, "offPeakLoadBalancingAlgorithm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "peakLoadBalancingAlgorithm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rampDownStopHostsWhen": "ZeroActiveSessions", "rampDownForceLogoffUsers": false}]}]}, "p_hosts": {"value": [{"adminUserName": "jeragmadm", "diskType": "Standard_LRS", "hostpoolName": "lbdevsp-pooled", "hostSku": "Standard_D4s_v3", "offer": "office-365", "publisher": "microsoftwindowsdesktop", "sku": "21h1-evd-o365pp", "version": "latest", "resourceGroup": "uks-npr-ssv-networking-rg", "virtualNetworkName": "uks-npr-ssv-spoke-vnet", "subnetName": "azure-virtual-desktop-snet"}]}}