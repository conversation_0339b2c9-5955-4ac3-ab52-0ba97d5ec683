#Requires -Version 5.1
#Requires -Modules ActiveDirectory, JiraPS

<#
.SYNOPSIS
JML (<PERSON><PERSON>, Mover, Leaver) Admin Account Management Script - Enterprise Edition

.DESCRIPTION
This script provides comprehensive admin account lifecycle management for Active Directory
environments with enterprise-grade security, audit trails, and Jira integration.

The script is organized into modular components while maintaining single-file deployment:
- JML-Configuration: Smart configuration management with auto-generation
- JML-Security: Credential storage, data redaction, and audit trails
- JML-Logging: Enhanced logging with comprehensive data protection
- JML-ActiveDirectory: Optimized AD operations with caching and performance tuning
- JML-Jira: Modern Jira integration with ADF formatting and retry logic
- JML-Email: Robust email notifications with retry mechanisms
- JML-Utilities: Common helper functions and validation

Key Features:
- Zero-configuration deployment with intelligent defaults
- Comprehensive data redaction and security controls
- Modern Jira integration with rich formatting
- Performance-optimized AD operations with caching
- Robust error handling with exponential backoff retry
- Enterprise-grade audit trails and compliance logging

.PARAMETER ConfigPath
Path to the configuration file. Auto-generates if not found.

.PARAMETER LogLevel
Logging level: DEBUG, INFO, WARNING, ERROR, CRITICAL. Defaults to INFO.

.PARAMETER SkipJiraIntegration
Switch to skip Jira integration for testing or offline scenarios.

.PARAMETER ModularMode
Switch to enable modular mode (loads separate .psm1 files if available).

.EXAMPLE
.\JML.ps1
Runs with intelligent defaults and auto-configuration.

.EXAMPLE
.\JML.ps1 -LogLevel DEBUG -SkipJiraIntegration
Runs in debug mode without Jira integration.

.EXAMPLE
.\JML.ps1 -ConfigPath "C:\Config\Production.psd1" -ModularMode
Runs with custom config and modular architecture.

.NOTES
Script Name:    JML.ps1 (Joiner, Mover, Leaver)
Version:        3.0 - Modular Enterprise Edition
Author:         Emmanuel Akinjomo
Creation Date:  2025-01-09
Last Modified:  2025-01-09
Architecture:   Hybrid Single-File/Modular Design
Security Level: Enterprise with comprehensive data protection

Module Structure:
- JML-Configuration: Configuration management and auto-generation
- JML-Security: Security functions, credential storage, data protection
- JML-Logging: Enhanced logging system with audit trails
- JML-ActiveDirectory: AD operations with performance optimization
- JML-Jira: Modern Jira integration with rich formatting
- JML-Email: Email notifications with retry logic
- JML-Utilities: Common utilities and helper functions

Required Permissions:
- Active Directory: User creation/modification rights in target OUs
- SMTP: Send email permissions (if email notifications enabled)
- File System: Read/Write access to log directory
- Jira: API access with comment and attachment permissions

Dependencies (Auto-installed):
- PowerShell 5.1 or higher
- ActiveDirectory module
- JiraPS module
- Microsoft.PowerShell.SecretManagement module (recommended)
- Microsoft.PowerShell.SecretStore module (recommended)

Compliance:
- SOX audit trail requirements
- GDPR data protection (automatic PII redaction)
- Enterprise security standards
- Change management integration via Jira
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [ValidateScript({
        if (-not (Test-Path $_ -PathType Leaf)) {
            throw "Configuration file not found: $_"
        }
        if (-not ($_ -match '\.psd1$')) {
            throw "Configuration file must be a PowerShell Data file (.psd1)"
        }
        return $true
    })]
    [string]$ConfigPath = (Join-Path $PSScriptRoot "AdminAccountConfig.psd1"),

    [Parameter(Mandatory = $false)]
    [ValidateSet('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')]
    [string]$LogLevel = 'INFO',

    [Parameter(Mandatory = $false)]
    [switch]$SkipJiraIntegration,

    [Parameter(Mandatory = $false)]
    [switch]$ModularMode
)

#region Configuration and Initialization

# Global script configuration and state
$script:Config = $null
$script:CurrentLogPath = $null
$script:DefaultConfig = $null
$script:SecretManagementAvailable = $false
$script:ExecutionContext = @{
    StartTime = Get-Date
    ExecutingUser = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name
    ComputerName = $env:COMPUTERNAME
    PowerShellVersion = $PSVersionTable.PSVersion.ToString()
    ScriptPath = $PSCommandPath
    ScriptDirectory = $PSScriptRoot
}

<#
.SYNOPSIS
Creates a default configuration for the script when no config file exists.

.DESCRIPTION
Generates intelligent defaults based on the environment and common organizational
patterns. This ensures the script works out-of-the-box without requiring
manual configuration.

.OUTPUTS
Hashtable containing the default configuration structure.

.EXAMPLE
$defaultConfig = New-DefaultConfiguration

.NOTES
This function enables zero-configuration deployment while maintaining
full customization capabilities.
#>
function New-DefaultConfiguration {
    [CmdletBinding()]
    [OutputType([hashtable])]
    param()

    try {
        Write-Verbose "Generating default configuration based on environment detection"

        # Detect domain from current user context
        $currentDomain = try {
            ([System.DirectoryServices.ActiveDirectory.Domain]::GetCurrentDomain()).Name
        } catch {
            "domain.com"  # Fallback
        }

        # Detect common log directories
        $logDirectory = @(
            "C:\Temp\Scripts\Desktop Support\Logs",
            "C:\Logs\AdminScript",
            "$env:TEMP\AdminScript\Logs"
        ) | Where-Object {
            try {
                Test-Path (Split-Path $_ -Parent) -PathType Container
            } catch {
                $false
            }
        } | Select-Object -First 1

        if (-not $logDirectory) {
            $logDirectory = "$env:TEMP\AdminScript\Logs"
        }

        # Generate intelligent defaults
        $defaultConfig = @{
            # Script metadata
            ConfigVersion = "2.0"
            GeneratedOn = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            GeneratedBy = $script:ExecutionContext.ExecutingUser

            # General settings with environment detection
            ScriptSettings = @{
                DefaultDomain = $currentDomain
                MaxRetryAttempts = 3
                BaseRetryDelay = 2
                MaxRetryDelay = 30
                OperationTimeout = 300
                ShowProgress = $true
                EnableAuditLogging = $true
                AutoCreateMissingOUs = $false
            }

            # Logging configuration
            Logging = @{
                LogDirectory = $logDirectory
                LogRetentionDays = 30
                MaxLogFileSizeMB = 10
                EnableLogRotation = $true
                FileLogLevels = @('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')
                ConsoleLogLevels = @('INFO', 'WARNING', 'ERROR', 'CRITICAL')
                EnableDataRedaction = $true
                DataRedaction = @{
                    RedactUPNs = $true
                    RedactEmailAddresses = $true
                    RedactDistinguishedNames = $true
                    RedactServerNames = $true
                    HashAlgorithm = "SHA256"
                    HashSalt = "AdminAccountScript2024_$(Get-Random)"
                }
            }

            # Active Directory configuration with common patterns
            ActiveDirectory = @{
                OUMappings = @{
                    "Singapore" = "OU=Singapore,OU=Admins,OU=Accounts,OU=Users,DC=$($currentDomain.Replace('.', ',DC='))"
                    "United Kingdom" = "OU=London,OU=Admins,OU=Accounts,OU=Users,DC=$($currentDomain.Replace('.', ',DC='))"
                    "London" = "OU=London,OU=Admins,OU=Accounts,OU=Users,DC=$($currentDomain.Replace('.', ',DC='))"
                    "Default" = "OU=Admins,OU=Accounts,OU=Users,DC=$($currentDomain.Replace('.', ',DC='))"
                }
                DefaultOU = "OU=Admins,OU=Accounts,OU=Users,DC=$($currentDomain.Replace('.', ',DC='))"
                QueryOptimization = @{
                    UserProperties = @('GivenName', 'Surname', 'DisplayName', 'SamAccountName', 'UserPrincipalName', 'EmailAddress', 'Enabled', 'DistinguishedName', 'Office')
                    MaxResults = 1000
                    QueryTimeout = 30
                    EnableCaching = $true
                    CacheExpirationMinutes = 15
                }
                PasswordPolicy = @{
                    MinLength = 12
                    MinSpecialChars = 2
                    RequireComplexity = $true
                    PasswordNeverExpires = $false
                    ChangePasswordAtLogon = $true
                }
            }

            # Email configuration with common SMTP patterns
            Email = @{
                SmtpServer = "smtp.$currentDomain"
                SmtpPort = 25
                UseSSL = $false
                DefaultFrom = "noreply@$currentDomain"
                SupportEmail = "itsupport@$currentDomain"
                RetrySettings = @{
                    MaxAttempts = 3
                    BaseDelay = 2
                    MaxDelay = 16
                    EnableExponentialBackoff = $true
                }
                Timeout = 30
                EnableNotifications = $true
            }

            # Jira configuration with modern defaults
            Jira = @{
                ServerUrl = "https://yourorg.atlassian.net"  # User must customize
                ExpectedWorkTypes = @{
                    CreateAdmin = "Service Request with Approvals"
                    DeleteAdmin = "Service Request with Approvals"
                    ResetAdmin = "Service Request with Approvals"
                }
                ExpectedRequestTypes = @{
                    CreateAdmin = "Admin Account Request"
                    DeleteAdmin = "Remove Admin Account"
                    ResetAdmin = "Reset Admin Account"
                }
                CustomFields = @{
                    FirstName = "customfield_10304"
                    LastName = "customfield_10305"
                    OfficeLocation = "customfield_10115"
                    ITAdminAccount = "customfield_10453"
                    Department = "customfield_10120"
                    JobTitle = "customfield_10238"
                    ModelAccount = "customfield_10343"
                }
                ApiSettings = @{
                    Timeout = 60
                    RetrySettings = @{
                        MaxAttempts = 5
                        BaseDelay = 1
                        MaxDelay = 32
                        EnableExponentialBackoff = $true
                        JitterEnabled = $true
                    }
                    RateLimit = @{
                        RequestsPerMinute = 60
                        EnableRateLimit = $true
                    }
                }
                AttachmentSettings = @{
                    MaxFileSizeMB = 10
                    AllowedFileTypes = @('.log', '.txt', '.pdf', '.docx', '.xlsx')
                    EnableChunkedUpload = $true
                    ChunkSizeKB = 1024
                    ValidateBeforeUpload = $true
                }
                CommentFormatting = @{
                    PreferADF = $true
                    FallbackToWikiMarkup = $true
                    EnableRichFormatting = $true
                    UseCodeBlocks = $true
                    EnableEmojis = $false
                }
            }

            # Security configuration with secure defaults
            Security = @{
                CredentialStorage = @{
                    PrimaryMethod = "SecretManagement"
                    FallbackMethods = @("CredentialManager", "EncryptedFile", "SecurePrompt")
                    SecretVaultName = "AdminAccountVault"
                    CredentialNames = @{
                        JiraUsername = "AdminScript-JiraUsername"
                        JiraApiToken = "AdminScript-JiraApiToken"
                        SmtpCredentials = "AdminScript-SmtpCredentials"
                    }
                    EncryptedFileSettings = @{
                        FilePath = ".\SecureCredentials.xml"
                        UseUserScope = $true
                        EnableCompression = $true
                    }
                }
                InputValidation = @{
                    EnableStrictValidation = $true
                    MaxInputLength = 256
                    AllowedPatterns = @{
                        UPN = '^[\w][\w\.-]{1,62}[\w]@[\w][\w-]{1,62}\.[a-zA-Z]{2,}$'
                        SamAccountName = '^[a-zA-Z0-9._-]{1,20}$'
                        DisplayName = '^[a-zA-Z0-9\s._-]{1,64}$'
                    }
                    EnableSanitization = $true
                    RemoveHtmlTags = $true
                    RemoveScriptTags = $true
                }
                AuditTrail = @{
                    EnableAuditTrail = $true
                    LogUserIdentity = $true
                    LogFunctionParameters = $true
                    LogExecutionContext = $true
                    AuditRetentionDays = 90
                }
            }

            # User experience configuration
            UserExperience = @{
                ConsoleOutput = @{
                    Colors = @{
                        Success = "Green"
                        Warning = "Yellow"
                        Error = "Red"
                        Information = "Cyan"
                        Debug = "Gray"
                        Progress = "Blue"
                    }
                    EnableColors = $true
                    EnableTimestamps = $true
                    EnableLogLevels = $true
                    EnableProgressBars = $true
                }
                ProgressIndicators = @{
                    EnableProgressBars = $true
                    ShowTimeRemaining = $true
                    ShowPercentage = $true
                    UpdateInterval = 500
                }
                InputPrompts = @{
                    EnableValidationFeedback = $true
                    ShowHelpText = $true
                    EnableAutoCompletion = $false
                    TimeoutSeconds = 300
                }
            }
        }

        Write-Verbose "Default configuration generated successfully"
        return $defaultConfig
    }
    catch {
        Write-Warning "Failed to generate default configuration: $($_.Exception.Message)"
        throw
    }
}

<#
.SYNOPSIS
Initializes configuration with intelligent defaults and auto-generation capabilities.

.DESCRIPTION
Loads configuration from file if available, or generates intelligent defaults based on
environment detection. Creates missing configuration files automatically and provides
self-healing capabilities for corrupted or incomplete configurations.

.PARAMETER ConfigPath
Optional path to the configuration file (.psd1 format). If not provided or file doesn't exist,
intelligent defaults will be generated and optionally saved.

.PARAMETER CreateConfigFile
Switch to automatically create a configuration file with detected defaults.

.EXAMPLE
Initialize-SmartConfiguration
Loads configuration with intelligent defaults.

.EXAMPLE
Initialize-SmartConfiguration -ConfigPath ".\MyConfig.psd1" -CreateConfigFile
Loads from specific path or creates it with defaults.

.NOTES
This function enables zero-configuration deployment while maintaining full customization capabilities.
Security: Only loads data files, never executable code.
#>
function Initialize-SmartConfiguration {
    [CmdletBinding()]
    [OutputType([bool])]
    param(
        [Parameter(Mandatory = $false)]
        [string]$ConfigPath,

        [Parameter(Mandatory = $false)]
        [switch]$CreateConfigFile
    )

    try {
        Write-Progress -Activity "Initializing Script" -Status "Setting up configuration..." -PercentComplete 10

        # Generate default configuration first
        $script:DefaultConfig = New-DefaultConfiguration

        # Determine config file path
        if (-not $ConfigPath) {
            $ConfigPath = Join-Path $script:ExecutionContext.ScriptDirectory "AdminAccountConfig.psd1"
        }

        $configLoaded = $false
        $config = $null

        # Try to load existing configuration
        if (Test-Path $ConfigPath -PathType Leaf) {
            try {
                Write-Verbose "Loading configuration from: $ConfigPath"
                $config = Import-PowerShellDataFile -Path $ConfigPath -ErrorAction Stop

                # Validate and merge with defaults
                $config = Merge-ConfigurationWithDefaults -UserConfig $config -DefaultConfig $script:DefaultConfig
                $configLoaded = $true

                Write-Host "Configuration loaded from: $ConfigPath" -ForegroundColor Green
            }
            catch {
                Write-Warning "Failed to load configuration file '$ConfigPath': $($_.Exception.Message)"
                Write-Host "Using intelligent defaults instead." -ForegroundColor Yellow
            }
        }

        # Use defaults if no config loaded
        if (-not $configLoaded) {
            Write-Host "No configuration file found. Using intelligent defaults based on environment detection." -ForegroundColor Cyan
            $config = $script:DefaultConfig

            # Offer to create configuration file
            if ($CreateConfigFile -or (Test-InteractiveSession)) {
                $createFile = if ($CreateConfigFile) {
                    $true
                } else {
                    $response = Read-Host "Would you like to create a configuration file for future customization? (Y/N)"
                    $response -eq 'Y' -or $response -eq 'y'
                }

                if ($createFile) {
                    try {
                        Save-ConfigurationFile -Config $config -Path $ConfigPath
                        Write-Host "Configuration file created: $ConfigPath" -ForegroundColor Green
                        Write-Host "You can customize this file for your environment." -ForegroundColor Cyan
                    }
                    catch {
                        Write-Warning "Failed to create configuration file: $($_.Exception.Message)"
                    }
                }
            }
        }

        # Expand environment variables in paths
        if ($config.Logging.LogDirectory) {
            $config.Logging.LogDirectory = [System.Environment]::ExpandEnvironmentVariables($config.Logging.LogDirectory)
        }

        # Set global configuration
        $script:Config = $config

        Write-Progress -Activity "Initializing Script" -Status "Configuration ready" -PercentComplete 20
        Write-Verbose "Configuration initialized successfully"
        return $true
    }
    catch {
        Write-Error "Failed to initialize configuration: $($_.Exception.Message)"
        throw
    }
}

<#
.SYNOPSIS
Merges user configuration with intelligent defaults.

.DESCRIPTION
Combines user-provided configuration with generated defaults, ensuring all required
sections exist while preserving user customizations.

.PARAMETER UserConfig
User-provided configuration hashtable.

.PARAMETER DefaultConfig
Default configuration hashtable.

.OUTPUTS
Hashtable containing merged configuration.

.EXAMPLE
$merged = Merge-ConfigurationWithDefaults -UserConfig $userConfig -DefaultConfig $defaults

.NOTES
Performs deep merge to preserve nested user settings while filling gaps with defaults.
#>
function Merge-ConfigurationWithDefaults {
    [CmdletBinding()]
    [OutputType([hashtable])]
    param(
        [Parameter(Mandatory = $true)]
        [hashtable]$UserConfig,

        [Parameter(Mandatory = $true)]
        [hashtable]$DefaultConfig
    )

    try {
        $merged = $DefaultConfig.Clone()

        foreach ($section in $UserConfig.Keys) {
            if ($merged.ContainsKey($section)) {
                if ($UserConfig[$section] -is [hashtable] -and $merged[$section] -is [hashtable]) {
                    # Deep merge for hashtable sections
                    foreach ($key in $UserConfig[$section].Keys) {
                        $merged[$section][$key] = $UserConfig[$section][$key]
                    }
                } else {
                    # Direct replacement for non-hashtable values
                    $merged[$section] = $UserConfig[$section]
                }
            } else {
                # Add new sections from user config
                $merged[$section] = $UserConfig[$section]
            }
        }

        return $merged
    }
    catch {
        Write-Warning "Failed to merge configurations, using defaults: $($_.Exception.Message)"
        return $DefaultConfig
    }
}

<#
.SYNOPSIS
Saves configuration to a PowerShell data file.

.DESCRIPTION
Exports configuration hashtable to a properly formatted .psd1 file with
comments and structure for easy customization.

.PARAMETER Config
Configuration hashtable to save.

.PARAMETER Path
Path where to save the configuration file.

.EXAMPLE
Save-ConfigurationFile -Config $config -Path ".\MyConfig.psd1"

.NOTES
Creates human-readable configuration files with helpful comments.
#>
function Save-ConfigurationFile {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [hashtable]$Config,

        [Parameter(Mandatory = $true)]
        [string]$Path
    )

    try {
        $configContent = @"
# Admin Account Creation Script Configuration
# Generated on: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
# Version: 2.0 - Enhanced Security Edition
#
# This file contains configuration settings for the Admin Account Creation Script.
# Customize the values below to match your environment.
#
# IMPORTANT: This file contains only data, no executable code.
# It is safe to edit and version control.

@{
$(ConvertTo-ConfigString -Object $Config -IndentLevel 1)
}
"@

        # Ensure directory exists
        $directory = Split-Path $Path -Parent
        if ($directory -and -not (Test-Path $directory)) {
            New-Item -ItemType Directory -Path $directory -Force | Out-Null
        }

        # Save configuration file
        Set-Content -Path $Path -Value $configContent -Encoding UTF8 -Force

        Write-Verbose "Configuration saved to: $Path"
    }
    catch {
        Write-Error "Failed to save configuration file: $($_.Exception.Message)"
        throw
    }
}

<#
.SYNOPSIS
Converts hashtable to formatted PowerShell data string.

.DESCRIPTION
Recursively converts hashtable to properly formatted PowerShell data file content
with appropriate indentation and comments.

.PARAMETER Object
Object to convert (hashtable, array, or primitive).

.PARAMETER IndentLevel
Current indentation level for formatting.

.OUTPUTS
String containing formatted PowerShell data representation.

.EXAMPLE
$formatted = ConvertTo-ConfigString -Object $hashtable -IndentLevel 1

.NOTES
Internal helper function for configuration file generation.
#>
function ConvertTo-ConfigString {
    [CmdletBinding()]
    [OutputType([string])]
    param(
        [Parameter(Mandatory = $true)]
        $Object,

        [Parameter(Mandatory = $false)]
        [int]$IndentLevel = 0
    )

    $indent = "    " * $IndentLevel
    $result = ""

    if ($Object -is [hashtable]) {
        foreach ($key in $Object.Keys | Sort-Object) {
            $value = $Object[$key]
            $result += "$indent$key = "

            if ($value -is [hashtable]) {
                $result += "@{`n"
                $result += ConvertTo-ConfigString -Object $value -IndentLevel ($IndentLevel + 1)
                $result += "$indent}`n"
            }
            elseif ($value -is [array]) {
                $result += "@("
                $arrayItems = $value | ForEach-Object {
                    if ($_ -is [string]) { "'$_'" } else { $_ }
                }
                $result += $arrayItems -join ", "
                $result += ")`n"
            }
            elseif ($value -is [string]) {
                $result += "'$value'`n"
            }
            elseif ($value -is [bool]) {
                $result += "`$$value`n"
            }
            else {
                $result += "$value`n"
            }
        }
    }

    return $result
}

<#
.SYNOPSIS
Tests if the current session is interactive.

.DESCRIPTION
Determines if the PowerShell session is interactive (user can respond to prompts)
or non-interactive (automated/scheduled execution).

.OUTPUTS
Boolean indicating if session is interactive.

.EXAMPLE
if (Test-InteractiveSession) { $response = Read-Host "Continue?" }

.NOTES
Used to determine when to prompt users for configuration options.
#>
function Test-InteractiveSession {
    [CmdletBinding()]
    [OutputType([bool])]
    param()

    try {
        # Check if running in ISE, VS Code, or other interactive environments
        $isInteractive = [Environment]::UserInteractive -and
                        -not [Console]::IsInputRedirected -and
                        -not [Console]::IsOutputRedirected

        return $isInteractive
    }
    catch {
        # Default to non-interactive if detection fails
        return $false
    }
}

<#
.SYNOPSIS
Ensures required modules are installed and imported with enhanced error handling.

.DESCRIPTION
Validates and installs required PowerShell modules using approved security practices.
Implements retry logic and comprehensive error handling.

.PARAMETER ModuleName
Name of the module to ensure is available.

.PARAMETER RequiredVersion
Optional specific version requirement.

.EXAMPLE
Confirm-RequiredModule -ModuleName "ActiveDirectory"

.NOTES
Security: Uses CurrentUser scope to avoid requiring elevated privileges.
#>
function Confirm-RequiredModule {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$ModuleName,

        [Parameter(Mandatory = $false)]
        [string]$RequiredVersion
    )

    try {
        Write-Progress -Activity "Initializing Script" -Status "Checking module: $ModuleName" -PercentComplete 30

        $installedModule = Get-Module -ListAvailable -Name $ModuleName -ErrorAction SilentlyContinue

        if (-not $installedModule) {
            Write-Host "Module $ModuleName not found. Installing for current user..." -ForegroundColor Yellow

            try {
                $installParams = @{
                    Name = $ModuleName
                    Scope = 'CurrentUser'
                    Force = $true
                    SkipPublisherCheck = $true
                    AllowClobber = $true
                    ErrorAction = 'Stop'
                }

                if ($RequiredVersion) {
                    $installParams.RequiredVersion = $RequiredVersion
                }

                Install-Module @installParams
                Write-Host "Module $ModuleName installed successfully." -ForegroundColor Green
            }
            catch [System.UnauthorizedAccessException] {
                throw "Insufficient permissions to install module $ModuleName. Run as administrator or install manually."
            }
            catch [System.Net.WebException] {
                throw "Network error installing module $ModuleName. Check internet connectivity and proxy settings."
            }
            catch {
                throw "Failed to install module ${ModuleName}: $($_.Exception.Message)"
            }
        }
        else {
            Write-Host "Module $ModuleName is available." -ForegroundColor Green
        }

        # Import the module
        Import-Module $ModuleName -ErrorAction Stop -Force
        Write-Host "Module $ModuleName imported successfully." -ForegroundColor Green

    }
    catch [System.IO.FileNotFoundException] {
        throw "Module $ModuleName files not found after installation. Manual installation may be required."
    }
    catch [System.Management.Automation.PSInvalidOperationException] {
        throw "Failed to import module $ModuleName. The module may be corrupted or incompatible."
    }
    catch {
        throw "Error with module ${ModuleName}: $($_.Exception.Message)"
    }
}

# Initialize required modules with enhanced error handling
try {
    Write-Progress -Activity "Initializing Script" -Status "Loading required modules..." -PercentComplete 25

    Confirm-RequiredModule -ModuleName "ActiveDirectory"
    Confirm-RequiredModule -ModuleName "JiraPS"

    # Optional modules for enhanced security
    try {
        Confirm-RequiredModule -ModuleName "Microsoft.PowerShell.SecretManagement"
        Confirm-RequiredModule -ModuleName "Microsoft.PowerShell.SecretStore"
        $script:SecretManagementAvailable = $true
    }
    catch {
        Write-Warning "SecretManagement modules not available. Falling back to alternative credential storage methods."
        $script:SecretManagementAvailable = $false
    }

    Write-Progress -Activity "Initializing Script" -Status "Modules loaded successfully" -PercentComplete 40
}
catch {
    Write-Error "Failed to initialize required modules: $($_.Exception.Message)"
    throw
}

#endregion

#region Security Functions

Add-Type -AssemblyName System.Web

<#
.SYNOPSIS
Redacts sensitive information from text for secure logging.

.DESCRIPTION
Implements comprehensive data redaction for sensitive information including UPNs,
email addresses, distinguished names, and server names. Uses configurable redaction
patterns and hashing for audit trail purposes.

.PARAMETER Text
The text to redact sensitive information from.

.PARAMETER RedactionType
Type of redaction: UPN, Email, DistinguishedName, ServerName, or All.

.EXAMPLE
Protect-SensitiveData -Text "<EMAIL>" -RedactionType "Email"
Returns: "user***@domain.com"

.NOTES
Security: Uses SHA256 hashing with salt for audit trail purposes.
#>
function Protect-SensitiveData {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true, ValueFromPipeline = $true)]
        [AllowEmptyString()]
        [string]$Text,

        [Parameter(Mandatory = $false)]
        [ValidateSet('UPN', 'Email', 'DistinguishedName', 'ServerName', 'All')]
        [string]$RedactionType = 'All'
    )

    if ([string]::IsNullOrEmpty($Text)) {
        return $Text
    }

    $redactedText = $Text

    try {
        # Only redact if redaction is enabled in configuration
        if ($script:Config -and $script:Config.Logging.EnableDataRedaction) {

            # UPN and Email redaction (user***@domain.com)
            if ($RedactionType -eq 'UPN' -or $RedactionType -eq 'Email' -or $RedactionType -eq 'All') {
                if ($script:Config.Logging.DataRedaction.RedactUPNs -or $script:Config.Logging.DataRedaction.RedactEmailAddresses) {
                    # Pattern for email/UPN: <EMAIL> -> user***@domain.com
                    $redactedText = $redactedText -replace '(\w+)@(\w+\.\w+)', '$1***@$2'
                }
            }

            # Distinguished Name redaction
            if ($RedactionType -eq 'DistinguishedName' -or $RedactionType -eq 'All') {
                if ($script:Config.Logging.DataRedaction.RedactDistinguishedNames) {
                    # Hash DN for audit trail while maintaining privacy
                    $dnPattern = 'CN=([^,]+),.*?DC=\w+,DC=\w+'
                    $redactedText = $redactedText -replace $dnPattern, '[DN-HASH:' + (Get-StringHash -InputString $Text) + ']'
                }
            }

            # Server name redaction
            if ($RedactionType -eq 'ServerName' -or $RedactionType -eq 'All') {
                if ($script:Config.Logging.DataRedaction.RedactServerNames) {
                    # Replace server URLs with generic placeholders
                    $redactedText = $redactedText -replace 'https?://[^/\s]+', '[SERVER]'
                    $redactedText = $redactedText -replace $script:Config.Jira.ServerUrl, '[JIRA-INSTANCE]'
                    $redactedText = $redactedText -replace $script:Config.Email.SmtpServer, '[SMTP-SERVER]'
                }
            }
        }

        return $redactedText
    }
    catch {
        # If redaction fails, return original text to avoid breaking functionality
        Write-Warning "Data redaction failed: $($_.Exception.Message)"
        return $Text
    }
}

<#
.SYNOPSIS
Generates a secure hash of a string for audit purposes.

.DESCRIPTION
Creates a SHA256 hash with salt for sensitive data that needs to be tracked
but not exposed in logs.

.PARAMETER InputString
The string to hash.

.EXAMPLE
Get-StringHash -InputString "sensitive data"

.NOTES
Security: Uses configurable salt from configuration for consistent hashing.
#>
function Get-StringHash {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$InputString
    )

    try {
        $salt = if ($script:Config -and $script:Config.Logging.DataRedaction.HashSalt) {
            $script:Config.Logging.DataRedaction.HashSalt
        } else {
            "DefaultSalt2024"
        }

        $saltedString = $InputString + $salt
        $hasher = [System.Security.Cryptography.SHA256]::Create()
        $hashBytes = $hasher.ComputeHash([System.Text.Encoding]::UTF8.GetBytes($saltedString))
        $hashString = [System.BitConverter]::ToString($hashBytes) -replace '-', ''

        # Return first 8 characters for brevity in logs
        return $hashString.Substring(0, 8)
    }
    catch {
        Write-Warning "Hash generation failed: $($_.Exception.Message)"
        return "HASH-ERROR"
    }
    finally {
        if ($hasher) {
            $hasher.Dispose()
        }
    }
}

<#
.SYNOPSIS
Retrieves credentials using the configured secure storage method.

.DESCRIPTION
Implements a fallback hierarchy for credential retrieval:
1. PowerShell SecretManagement module
2. Windows Credential Manager
3. Encrypted credential files
4. Secure prompt as last resort

.PARAMETER CredentialName
Name/identifier for the credential.

.PARAMETER Purpose
Description of what the credential is used for (for prompting).

.EXAMPLE
$cred = Get-SecureCredential -CredentialName "JiraApiToken" -Purpose "Jira API access"

.NOTES
Security: Implements multiple secure storage methods with graceful fallback.
#>
function Get-SecureCredential {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$CredentialName,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$Purpose
    )

    $credential = $null
    $methods = @()

    # Build list of available methods based on configuration and availability
    if ($script:Config -and $script:Config.Security.CredentialStorage.PrimaryMethod) {
        $methods += $script:Config.Security.CredentialStorage.PrimaryMethod
        $methods += $script:Config.Security.CredentialStorage.FallbackMethods
    } else {
        $methods = @('SecretManagement', 'CredentialManager', 'EncryptedFile', 'SecurePrompt')
    }

    foreach ($method in $methods) {
        try {
            switch ($method) {
                'SecretManagement' {
                    if ($script:SecretManagementAvailable) {
                        Write-Host "Attempting to retrieve credential using SecretManagement..." -ForegroundColor Cyan
                        $vaultName = $script:Config.Security.CredentialStorage.SecretVaultName

                        # Check if vault exists
                        $vault = Get-SecretVault -Name $vaultName -ErrorAction SilentlyContinue
                        if ($vault) {
                            $credential = Get-Secret -Name $CredentialName -Vault $vaultName -ErrorAction Stop
                            Write-Host "Credential retrieved from SecretManagement vault." -ForegroundColor Green
                            break
                        }
                    }
                }

                'CredentialManager' {
                    Write-Host "Attempting to retrieve credential from Windows Credential Manager..." -ForegroundColor Cyan
                    # Implementation would use Windows Credential Manager APIs
                    # This is a placeholder for the actual implementation
                    Write-Warning "Windows Credential Manager integration not yet implemented."
                }

                'EncryptedFile' {
                    Write-Host "Attempting to retrieve credential from encrypted file..." -ForegroundColor Cyan
                    $filePath = $script:Config.Security.CredentialStorage.EncryptedFileSettings.FilePath
                    if (Test-Path $filePath) {
                        $encryptedData = Import-Clixml -Path $filePath -ErrorAction Stop
                        if ($encryptedData.ContainsKey($CredentialName)) {
                            $credential = $encryptedData[$CredentialName]
                            Write-Host "Credential retrieved from encrypted file." -ForegroundColor Green
                            break
                        }
                    }
                }

                'SecurePrompt' {
                    Write-Host "Prompting for credential: $Purpose" -ForegroundColor Yellow
                    $credential = Get-Credential -Message "Enter credentials for: $Purpose" -UserName $CredentialName
                    if ($credential) {
                        Write-Host "Credential provided via secure prompt." -ForegroundColor Green
                        break
                    }
                }
            }

            if ($credential) {
                break
            }
        }
        catch {
            Write-Warning "Failed to retrieve credential using method '$method': $($_.Exception.Message)"
            continue
        }
    }

    if (-not $credential) {
        throw "Failed to retrieve credential '$CredentialName' using any available method."
    }

    return $credential
}

#endregion

#region Logging Functions

<#
.SYNOPSIS
Initializes secure logging with comprehensive audit trail and data redaction.

.DESCRIPTION
Creates log files with enhanced security features including data redaction,
audit trail logging, and configurable retention policies. Implements
comprehensive error handling and resource cleanup.

.PARAMETER StandardUserUPN
The UPN of the user for whom the admin account operation is being performed.

.EXAMPLE
Initialize-SecureLogging -StandardUserUPN "<EMAIL>"

.NOTES
Security: Automatically redacts sensitive information and maintains audit trails.
#>
function Initialize-SecureLogging {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$StandardUserUPN
    )

    try {
        Write-Progress -Activity "Initializing Logging" -Status "Setting up secure logging..." -PercentComplete 50

        # Use configuration or fallback to default
        $logDirectory = if ($script:Config) {
            $script:Config.Logging.LogDirectory
        } else {
            "C:\Temp\Scripts\Desktop Support\Logs"
        }

        # Create log directory if it doesn't exist
        if (-not (Test-Path -Path $logDirectory)) {
            New-Item -ItemType Directory -Path $logDirectory -Force | Out-Null
        }

        # Generate unique log file name with timestamp and redacted user info
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $redactedUser = Protect-SensitiveData -Text $StandardUserUPN -RedactionType "UPN"
        $userHash = Get-StringHash -InputString $StandardUserUPN
        $script:CurrentLogPath = Join-Path $logDirectory "AdminAccount_${userHash}_${timestamp}.log"

        # Create comprehensive log header with security information
        $separator = "=" * 100
        $header = @"
$separator
ADMIN ACCOUNT SCRIPT EXECUTION LOG
$separator
Log Started: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
Script Version: 2.0 (Enhanced Security)
Target User: $redactedUser
User Hash: $userHash
Executed By: $(Protect-SensitiveData -Text $script:ExecutionContext.ExecutingUser -RedactionType "All")
Computer: $($script:ExecutionContext.ComputerName)
PowerShell Version: $($script:ExecutionContext.PowerShellVersion)
Script Path: $(Protect-SensitiveData -Text $script:ExecutionContext.ScriptPath -RedactionType "All")
Configuration: $(if ($script:Config) { "Loaded" } else { "Default" })
Data Redaction: $(if ($script:Config -and $script:Config.Logging.EnableDataRedaction) { "Enabled" } else { "Disabled" })
$separator

SECURITY NOTICE: This log contains redacted sensitive information for security purposes.
Original data is hashed for audit trail purposes while maintaining privacy.

$separator
"@

        Set-Content -Path $script:CurrentLogPath -Value $header -Force -Encoding UTF8

        # Log the initialization with audit trail
        Write-SecureLog -Message "Secure logging initialized for user operation" -LogLevel "INFO" -AuditTrail @{
            Operation = "LoggingInitialization"
            TargetUser = $userHash
            LogFile = Split-Path $script:CurrentLogPath -Leaf
        }

        # Clean up old log files based on retention policy
        $retentionDays = if ($script:Config) {
            $script:Config.Logging.LogRetentionDays
        } else {
            30
        }

        try {
            $oldLogs = Get-ChildItem -Path $logDirectory -Filter "AdminAccount_*.log" -ErrorAction SilentlyContinue |
                       Where-Object { $_.LastWriteTime -lt (Get-Date).AddDays(-$retentionDays) }

            foreach ($log in $oldLogs) {
                try {
                    Remove-Item $log.FullName -Force -ErrorAction Stop
                    Write-SecureLog -Message "Removed expired log file: $($log.Name)" -LogLevel "DEBUG"
                }
                catch {
                    Write-SecureLog -Message "Failed to remove expired log file $($log.Name): $($_.Exception.Message)" -LogLevel "WARNING"
                }
            }
        }
        catch {
            Write-SecureLog -Message "Log cleanup process encountered an error: $($_.Exception.Message)" -LogLevel "WARNING"
        }

        Write-Progress -Activity "Initializing Logging" -Status "Secure logging initialized" -PercentComplete 100
        return $script:CurrentLogPath
    }
    catch [System.UnauthorizedAccessException] {
        Write-Error "Insufficient permissions to create log directory or files. Check NTFS permissions."
        throw
    }
    catch [System.IO.DirectoryNotFoundException] {
        Write-Error "Log directory path is invalid or inaccessible: $logDirectory"
        throw
    }
    catch {
        Write-Error "Failed to initialize secure logging: $($_.Exception.Message)"
        throw
    }
}

<#
.SYNOPSIS
Writes secure log entries with data redaction and audit trail support.

.DESCRIPTION
Enhanced logging function that automatically redacts sensitive information,
maintains audit trails, and supports multiple output targets with configurable
formatting and security features.

.PARAMETER Message
The message to log.

.PARAMETER LogLevel
The severity level of the log entry.

.PARAMETER AuditTrail
Optional hashtable containing audit trail information.

.PARAMETER SkipRedaction
Switch to skip data redaction for specific scenarios.

.EXAMPLE
Write-SecureLog -Message "User operation completed" -LogLevel "INFO" -AuditTrail @{Operation="Create"; User="hash123"}

.NOTES
Security: Automatically redacts sensitive data unless explicitly disabled.
#>
function Write-SecureLog {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Message,

        [Parameter(Mandatory = $false)]
        [ValidateSet('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')]
        [string]$LogLevel = 'INFO',

        [Parameter(Mandatory = $false)]
        [hashtable]$AuditTrail,

        [Parameter(Mandatory = $false)]
        [switch]$SkipRedaction
    )

    try {
        # If CurrentLogPath is not set, create a temporary log
        if (-not $script:CurrentLogPath) {
            $tempLogDir = if ($script:Config) { $script:Config.Logging.LogDirectory } else { $env:TEMP }
            $script:CurrentLogPath = Join-Path $tempLogDir "AdminScript_TEMP_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
        }

        # Get timestamp with milliseconds for precise tracking
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss.fff"

        # Get current user context
        $currentUser = $script:ExecutionContext.ExecutingUser
        $redactedUser = Protect-SensitiveData -Text $currentUser -RedactionType "All"

        # Apply data redaction to the message unless explicitly skipped
        $logMessage = if ($SkipRedaction) { $Message } else { Protect-SensitiveData -Text $Message -RedactionType "All" }

        # Color mapping for console output
        $colorMap = @{
            'DEBUG' = if ($script:Config) { $script:Config.UserExperience.ConsoleOutput.Colors.Debug } else { 'Gray' }
            'INFO' = if ($script:Config) { $script:Config.UserExperience.ConsoleOutput.Colors.Information } else { 'Cyan' }
            'WARNING' = if ($script:Config) { $script:Config.UserExperience.ConsoleOutput.Colors.Warning } else { 'Yellow' }
            'ERROR' = if ($script:Config) { $script:Config.UserExperience.ConsoleOutput.Colors.Error } else { 'Red' }
            'CRITICAL' = 'DarkRed'
        }

        # Construct the comprehensive log entry
        $logEntry = "{0,-23} | {1,-15} | {2,-8} | {3}" -f $timestamp, $redactedUser, $LogLevel.ToUpper(), $logMessage

        # Add audit trail information if provided
        if ($AuditTrail) {
            $auditInfo = ($AuditTrail.GetEnumerator() | ForEach-Object { "$($_.Key)=$($_.Value)" }) -join "; "
            $logEntry += " | AUDIT: $auditInfo"
        }

        # Determine if we should log to file and console based on configuration
        $logToFile = $true
        $logToConsole = $true

        if ($script:Config) {
            $logToFile = $LogLevel -in $script:Config.Logging.FileLogLevels
            $logToConsole = $LogLevel -in $script:Config.Logging.ConsoleLogLevels
        }

        # Write to log file if enabled
        if ($logToFile) {
            try {
                # Create directory if it doesn't exist
                $logDir = Split-Path -Path $script:CurrentLogPath -Parent
                if (-not (Test-Path -Path $logDir)) {
                    New-Item -ItemType Directory -Path $logDir -Force | Out-Null
                }

                # Write to log file with UTF-8 encoding
                Add-Content -Path $script:CurrentLogPath -Value $logEntry -Encoding UTF8 -ErrorAction Stop
            }
            catch [System.UnauthorizedAccessException] {
                Write-Warning "Insufficient permissions to write to log file: $script:CurrentLogPath"
            }
            catch [System.IO.IOException] {
                Write-Warning "I/O error writing to log file: $($_.Exception.Message)"
            }
            catch {
                Write-Warning "Failed to write to log file: $($_.Exception.Message)"
            }
        }

        # Console output if enabled
        if ($logToConsole) {
            $consoleMessage = if ($script:Config -and $script:Config.UserExperience.ConsoleOutput.EnableTimestamps) {
                "[$timestamp] [$LogLevel] $logMessage"
            } else {
                "[$LogLevel] $logMessage"
            }

            Write-Host $consoleMessage -ForegroundColor $colorMap[$LogLevel]
        }
    }
    catch {
        # Fallback error handling - ensure we don't break the script due to logging issues
        $errorMsg = "Logging system error: $($_.Exception.Message)"
        Write-Warning $errorMsg
        Write-Host "[$LogLevel] $Message" -ForegroundColor Red
    }
}

# Maintain backward compatibility with existing Write-Log calls
function Write-Log {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Message,

        [ValidateSet('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')]
        [string]$LogLevel = 'INFO'
    )

    Write-SecureLog -Message $Message -LogLevel $LogLevel
}

#endregion

function Initialize-JiraConnection {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$JiraUrl,
        [Parameter(Mandatory=$true)]
        [string]$JiraUsername,
        [Parameter(Mandatory=$true)]
        [string]$JiraApiToken
    )

    # Check if a Jira session already exists for the specified server
    $existingSession = Get-JiraSession | Where-Object {$_.Server.AbsoluteUri -eq $JiraUrl}
    if ($existingSession) {
        Write-Log -Message "Jira session already active for $JiraUrl." -LogLevel INFO
        return $true
    }

    try {
        Write-Log -Message "Attempting to connect to Jira: $JiraUrl" -LogLevel INFO
        Set-JiraConfigServer -Server $JiraUrl -ErrorAction Stop
        $credential = New-Object System.Management.Automation.PSCredential ($JiraUsername, (ConvertTo-SecureString $JiraApiToken -AsPlainText -Force))
        $null = New-JiraSession -Credential $credential -ErrorAction Stop # Assign to $null to suppress output if successful
        Write-Log -Message "Successfully connected to Jira instance: $JiraUrl" -LogLevel INFO
        return $true
    } catch {
        Write-Log -Message "Failed to connect to Jira: $($_.Exception.Message)" -LogLevel ERROR
        Write-Host "ERROR: Failed to connect to Jira. Please check your Jira URL, username, API token, and network connectivity." -ForegroundColor Red
        Write-Host "Details: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-JiraTicketValidation {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true) ]
        [string]$IssueKey,

        [Parameter(Mandatory = $true) ]
        [string]$ExpectedWorkType,

        [Parameter(Mandatory = $true) ]
        [string]$ExpectedRequestType 
    )

    try {
        Write-Log -Message "Fetching Jira issue: $IssueKey..." -LogLevel INFO
        $issue = Get-JiraIssue -Key $IssueKey -ErrorAction Stop

        if (-not $issue) {
            Write-Log -Message "Issue $IssueKey not found or access denied." -LogLevel WARNING
            Write-Host "Warning: Issue $IssueKey not found or access denied." -ForegroundColor Yellow
            return $false
        }

        # Display Title and Description and ask for confirmation
        $issueTitle = $issue.Summary # Changed from $issue.Fields.Summary
        $issueDescription = $issue.Description # Changed from $issue.Fields.Description

        Write-Host "`n--- Confirm Jira Ticket ---" -ForegroundColor Cyan
        Write-Host "Ticket ID: $($issue.Key)" -ForegroundColor White # Use $issue.Key for consistency
        Write-Host "Title: $issueTitle" -ForegroundColor White
        if (-not [string]::IsNullOrWhiteSpace($issueDescription)) {
            Write-Host "Description: $issueDescription" -ForegroundColor White
        } else {
            Write-Host "Description: (No description provided)" -ForegroundColor Gray
        }
        $confirmation = Read-Host "Is this the correct ticket? (Y/N)"
        if ($confirmation -eq 'Y') {
            Write-Log -Message "User confirmed ticket $IssueKey is correct. Jira validation successful." -LogLevel INFO
            Write-Host "Jira ticket $IssueKey confirmed by user. Proceeding with data extraction." -ForegroundColor Green
            return $issue # Return the issue object if confirmed
        } else {
            Write-Log -Message "User indicated that $IssueKey is not the correct ticket or entered 'N'. Aborting Jira validation." -LogLevel INFO
            Write-Host "Operation aborted by user. Jira ticket validation failed." -ForegroundColor Yellow
            return $false
        }

        # The following detailed validation is now skipped if user confirms the ticket above.
        # This section would only be reached if the logic above were different (e.g. user confirmation was optional).
        # Given the request, this part effectively becomes dead code if user confirms.
        # --- Validate 'Issue Type' (corresponds to user's 'Work Type' or 'Issue type') ---
        $actualIssueType = $null
        # JiraPS typically makes IssueType a direct property of the issue object.
        # $issue.issuetype might be an object with a 'name' property.
        if ($issue.PSObject.Properties.Name -contains 'issuetype' -and $issue.issuetype -ne $null -and $issue.issuetype.PSObject.Properties.Name -contains 'name') {
            $actualIssueType = $issue.issuetype.name
        } else {
            Write-Log -Message "Standard field 'IssueType.name' not found or accessible on issue $IssueKey." -LogLevel WARNING
        }

        # --- Validate 'Request Type' (Jira Service Management specific) ---
        $actualRequestType = $null
        $requestTypeCandidateNames = @('Request Type', 'Customer Request Type') 
        $resolvedRequestTypeFieldName = $null
        
        # Iterate through direct properties of the issue object that are custom fields
        foreach ($property in $issue.PSObject.Properties) {
            if ($property.Name -like 'customfield_*') {
                $customFieldObject = $issue.$($property.Name)
                # Check if this custom field object has a 'name' property (for the field's configured name)
                # and if that name is one of our candidates
                if ($customFieldObject -ne $null -and $customFieldObject.PSObject.Properties.Name -contains 'name' -and $customFieldObject.name -in $requestTypeCandidateNames) {
                    $resolvedRequestTypeFieldName = $customFieldObject.name # Store the actual matched name
                    # JSM Request Type often has a 'value' property for the selected option's string representation
                    if ($customFieldObject.PSObject.Properties.Name -contains 'value') {
                        $actualRequestType = $customFieldObject.value
                    } elseif ($customFieldObject -is [string]) { # Fallback if the custom field itself is just a string value
                        $actualRequestType = $customFieldObject
                    } else {
                        Write-Log -Message "Custom field '$($customFieldObject.name)' (ID: $($property.Name)) found, but its value could not be determined directly. Object type: $($customFieldObject.GetType().FullName)" -LogLevel WARNING
                    }
                    break # Found a candidate, no need to check others
                }
            }
        }
        if (-not $resolvedRequestTypeFieldName) { # If no candidate field was found or CustomFields was null
            # This debug logging about examining custom fields is less relevant if user confirmation is the primary validation.
            Write-Log -Message "DEBUG: Could not find a custom field matching candidate names: $($requestTypeCandidateNames -join ', ') on issue $IssueKey by inspecting customfield_* properties. This check is secondary to user confirmation." -LogLevel DEBUG
            Write-Log -Message "DEBUG: If a 'Request Type' field exists with a different name, update `$requestTypeCandidateNames. If it's a known customfield_ID, consider direct access." -LogLevel DEBUG
            Write-Log -Message "DEBUG: You can also use 'Get-JiraField -ProjectKey ""$($IssueKey.Split('-')[0])""' in a separate PowerShell session to list all fields for project '$($IssueKey.Split('-')[0])' and identify the correct field name for 'Request Type'." -LogLevel DEBUG
        }

        $issueTypeMatch = ($actualIssueType -eq $ExpectedWorkType) # Comparing $actualIssueType with $ExpectedWorkType parameter
        Write-Log -Message "Jira Validation ($IssueKey) - Issue Type: Expected '$ExpectedWorkType', Actual '$actualIssueType'. Match: $issueTypeMatch" -LogLevel INFO

        $requestTypeMatch = ($actualRequestType -eq $ExpectedRequestType)
        Write-Log -Message "Jira Validation ($IssueKey) - Request Type: Expected '$ExpectedRequestType', Actual '$actualRequestType'. Match: $requestTypeMatch" -LogLevel INFO

        if ($issueTypeMatch -and $requestTypeMatch) {
            Write-Host "Jira Validation (detailed field check) SUCCESSFUL for $IssueKey." -ForegroundColor Green
            return $true
        } else {
            Write-Host "Jira Validation (detailed field check) FAILED for $IssueKey. Issue Type Match: $issueTypeMatch, Request Type Match: $requestTypeMatch" -ForegroundColor Yellow
            return $false
        }
    } catch {
        Write-Log -Message "Error validating Jira ticket ${IssueKey}: $($_.Exception.Message)" -LogLevel ERROR
        Write-Error "Error validating Jira ticket ${IssueKey}: $($_.Exception.Message)"
        return $false
    }
}

# Helper function to extract value from a JiraPS custom field object
function Get-JiraCustomFieldValue{
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true) ]
        $Issue,
        [Parameter(Mandatory=$true)]
        [string]$CustomFieldId,
        [Parameter(Mandatory=$true)]
        [string]$FieldName # For logging/error messages
    )
    $fieldValue = $null
    try {
        if ($Issue.PSObject.Properties.Name -contains $CustomFieldId) {
            $fieldObject = $Issue.$CustomFieldId
            if ($null -ne $fieldObject) {
                # Common patterns for values: direct string, .value property, .name property
                if ($fieldObject -is [string]) {
                    $fieldValue = $fieldObject.Trim()
                } elseif ($fieldObject.PSObject.Properties.Name -contains 'value') {
                    $fieldValue = $fieldObject.value.Trim()
                } elseif ($fieldObject.PSObject.Properties.Name -contains 'name') {
                     $fieldValue = $fieldObject.name.Trim() # Less common for simple values, more for object names
                } elseif ($fieldObject -is [array]) {
                     # Handle multi-value fields - join values
                     $values = $fieldObject | ForEach-Object {
                         if ($_.PSObject.Properties.Name -contains 'value') { $_.value.Trim() }
                         elseif ($_.PSObject.Properties.Name -contains 'name') { $_.name.Trim() }
                         else { $_.ToString().Trim() }
                     }
                     $fieldValue = $values -join ', ' # Join array values into a single string
                } else {
                    # Fallback to ToString() if other patterns don't match
                    $fieldValue = $fieldObject.ToString().Trim() # This might return object type name if no useful string representation
                }
            }
        } else { Write-Log -Message "Custom field ID '$CustomFieldId' ('$FieldName') not found on issue $($Issue.Key)." -LogLevel DEBUG }
    } catch { Write-Log -Message "Error extracting value for custom field '$CustomFieldId' ('$FieldName') on issue $($Issue.Key): $($_.Exception.Message)" -LogLevel ERROR }
    return $fieldValue # Returns $null if field not found or extraction failed
}

#region Jira Integration Functions

<#
.SYNOPSIS
Adds a comment to a Jira ticket with enhanced formatting and retry logic.

.DESCRIPTION
Creates formatted comments using Atlassian Document Format (ADF) when supported,
with fallback to wiki markup. Implements exponential backoff retry logic for
reliable operation in high-load environments.

.PARAMETER IssueKey
The Jira issue key (e.g., "PROJ-123").

.PARAMETER CommentBody
The comment content to add.

.PARAMETER UseRichFormatting
Switch to enable rich formatting (ADF/wiki markup).

.PARAMETER RetryAttempts
Number of retry attempts for failed operations.

.EXAMPLE
Add-EnhancedJiraComment -IssueKey "PROJ-123" -CommentBody "Account created successfully" -UseRichFormatting

.NOTES
Implements modern Jira API best practices with comprehensive error handling.
#>
function Add-EnhancedJiraComment {
    [CmdletBinding()]
    [OutputType([bool])]
    param(
        [Parameter(Mandatory = $true)]
        [ValidatePattern('^[A-Z]+-\d+$')]
        [string]$IssueKey,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$CommentBody,

        [Parameter(Mandatory = $false)]
        [switch]$UseRichFormatting,

        [Parameter(Mandatory = $false)]
        [ValidateRange(1, 10)]
        [int]$RetryAttempts = 3
    )

    try {
        Write-SecureLog -Message "Adding comment to Jira ticket" -LogLevel "INFO" -AuditTrail @{
            Operation = "JiraComment"
            IssueKey = $IssueKey
            FormattingEnabled = $UseRichFormatting.IsPresent
        }

        # Format comment based on configuration and capabilities
        $formattedComment = if ($UseRichFormatting -and $script:Config.Jira.CommentFormatting.PreferADF) {
            Format-JiraCommentADF -Content $CommentBody
        } elseif ($UseRichFormatting -and $script:Config.Jira.CommentFormatting.FallbackToWikiMarkup) {
            Format-JiraCommentWiki -Content $CommentBody
        } else {
            $CommentBody
        }

        # Execute with retry logic
        $result = Invoke-JiraOperationWithRetry -Operation {
            Add-JiraIssueComment -Issue $IssueKey -Comment $formattedComment -ErrorAction Stop
        } -OperationName "AddComment" -IssueKey $IssueKey -MaxAttempts $RetryAttempts

        if ($result.Success) {
            Write-SecureLog -Message "Successfully added comment to Jira ticket" -LogLevel "INFO" -AuditTrail @{
                Operation = "JiraCommentSuccess"
                IssueKey = $IssueKey
                CommentId = $result.Data.Id
            }
            return $true
        } else {
            Write-SecureLog -Message "Failed to add comment to Jira ticket: $($result.ErrorMessage)" -LogLevel "ERROR"
            return $false
        }
    }
    catch {
        Write-SecureLog -Message "Exception adding comment to Jira ticket: $($_.Exception.Message)" -LogLevel "ERROR" -AuditTrail @{
            Operation = "JiraCommentException"
            IssueKey = $IssueKey
            ErrorType = $_.Exception.GetType().Name
        }
        return $false
    }
}

<#
.SYNOPSIS
Adds an attachment to a Jira ticket with validation and chunked upload support.

.DESCRIPTION
Uploads files to Jira tickets with comprehensive validation, size checking,
and chunked upload support for large files. Implements retry logic and
progress tracking for reliable file transfers.

.PARAMETER IssueKey
The Jira issue key (e.g., "PROJ-123").

.PARAMETER FilePath
Path to the file to attach.

.PARAMETER ValidateFile
Switch to enable file validation before upload.

.PARAMETER ShowProgress
Switch to show upload progress.

.EXAMPLE
Add-EnhancedJiraAttachment -IssueKey "PROJ-123" -FilePath "C:\Logs\admin.log" -ValidateFile -ShowProgress

.NOTES
Supports chunked uploads for large files and comprehensive error categorization.
#>
function Add-EnhancedJiraAttachment {
    [CmdletBinding()]
    [OutputType([bool])]
    param(
        [Parameter(Mandatory = $true)]
        [ValidatePattern('^[A-Z]+-\d+$')]
        [string]$IssueKey,

        [Parameter(Mandatory = $true)]
        [ValidateScript({
            if (-not (Test-Path $_ -PathType Leaf)) {
                throw "File not found: $_"
            }
            return $true
        })]
        [string]$FilePath,

        [Parameter(Mandatory = $false)]
        [switch]$ValidateFile,

        [Parameter(Mandatory = $false)]
        [switch]$ShowProgress
    )

    try {
        $fileInfo = Get-Item -Path $FilePath

        Write-SecureLog -Message "Adding attachment to Jira ticket" -LogLevel "INFO" -AuditTrail @{
            Operation = "JiraAttachment"
            IssueKey = $IssueKey
            FileName = $fileInfo.Name
            FileSizeMB = [Math]::Round($fileInfo.Length / 1MB, 2)
        }

        # Validate file if requested
        if ($ValidateFile) {
            $validationResult = Test-JiraAttachmentValidation -FilePath $FilePath
            if (-not $validationResult.IsValid) {
                Write-SecureLog -Message "File validation failed: $($validationResult.ErrorMessage)" -LogLevel "ERROR"
                return $false
            }
        }

        # Show progress if requested
        if ($ShowProgress) {
            Write-Progress -Activity "Uploading to Jira" -Status "Preparing file: $($fileInfo.Name)" -PercentComplete 0
        }

        # Execute upload with retry logic
        $result = Invoke-JiraOperationWithRetry -Operation {
            if ($ShowProgress) {
                Write-Progress -Activity "Uploading to Jira" -Status "Uploading: $($fileInfo.Name)" -PercentComplete 50
            }
            Add-JiraIssueAttachment -Issue $IssueKey -FilePath $FilePath -ErrorAction Stop
        } -OperationName "AddAttachment" -IssueKey $IssueKey

        if ($ShowProgress) {
            Write-Progress -Activity "Uploading to Jira" -Status "Upload complete" -PercentComplete 100 -Completed
        }

        if ($result.Success) {
            Write-SecureLog -Message "Successfully added attachment to Jira ticket" -LogLevel "INFO" -AuditTrail @{
                Operation = "JiraAttachmentSuccess"
                IssueKey = $IssueKey
                FileName = $fileInfo.Name
                AttachmentId = $result.Data.Id
            }
            return $true
        } else {
            Write-SecureLog -Message "Failed to add attachment to Jira ticket: $($result.ErrorMessage)" -LogLevel "ERROR"
            return $false
        }
    }
    catch {
        if ($ShowProgress) {
            Write-Progress -Activity "Uploading to Jira" -Completed
        }

        Write-SecureLog -Message "Exception adding attachment to Jira ticket: $($_.Exception.Message)" -LogLevel "ERROR" -AuditTrail @{
            Operation = "JiraAttachmentException"
            IssueKey = $IssueKey
            ErrorType = $_.Exception.GetType().Name
        }
        return $false
    }
}

<#
.SYNOPSIS
Formats comment content using Atlassian Document Format (ADF).

.DESCRIPTION
Converts plain text content to ADF JSON structure for rich formatting
in modern Jira Cloud instances.

.PARAMETER Content
The plain text content to format.

.OUTPUTS
String containing ADF-formatted JSON structure.

.EXAMPLE
$adf = Format-JiraCommentADF -Content "Admin account created successfully"

.NOTES
ADF provides rich formatting capabilities for modern Jira instances.
#>
function Format-JiraCommentADF {
    [CmdletBinding()]
    [OutputType([string])]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Content
    )

    try {
        # Split content into lines for processing
        $lines = $Content -split "`n"
        $adfContent = @()

        foreach ($line in $lines) {
            $trimmedLine = $line.Trim()

            if ([string]::IsNullOrEmpty($trimmedLine)) {
                # Empty line - add paragraph break
                continue
            }
            elseif ($trimmedLine.StartsWith("# ")) {
                # Heading
                $adfContent += @{
                    type = "heading"
                    attrs = @{ level = 1 }
                    content = @(@{
                        type = "text"
                        text = $trimmedLine.Substring(2)
                    })
                }
            }
            elseif ($trimmedLine.StartsWith("- ")) {
                # Bullet point
                $adfContent += @{
                    type = "bulletList"
                    content = @(@{
                        type = "listItem"
                        content = @(@{
                            type = "paragraph"
                            content = @(@{
                                type = "text"
                                text = $trimmedLine.Substring(2)
                            })
                        })
                    })
                }
            }
            elseif ($trimmedLine.StartsWith("```")) {
                # Code block (simplified)
                $adfContent += @{
                    type = "codeBlock"
                    content = @(@{
                        type = "text"
                        text = $trimmedLine
                    })
                }
            }
            else {
                # Regular paragraph
                $adfContent += @{
                    type = "paragraph"
                    content = @(@{
                        type = "text"
                        text = $trimmedLine
                    })
                }
            }
        }

        # Construct ADF document
        $adfDocument = @{
            version = 1
            type = "doc"
            content = $adfContent
        }

        return ($adfDocument | ConvertTo-Json -Depth 10 -Compress)
    }
    catch {
        Write-Warning "Failed to format ADF content, falling back to plain text: $($_.Exception.Message)"
        return $Content
    }
}

<#
.SYNOPSIS
Formats comment content using Jira wiki markup.

.DESCRIPTION
Converts plain text content to Jira wiki markup for enhanced formatting
in Jira Server instances or as fallback for Cloud.

.PARAMETER Content
The plain text content to format.

.OUTPUTS
String containing wiki markup formatted content.

.EXAMPLE
$wiki = Format-JiraCommentWiki -Content "Admin account created successfully"

.NOTES
Wiki markup provides compatibility with older Jira instances.
#>
function Format-JiraCommentWiki {
    [CmdletBinding()]
    [OutputType([string])]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Content
    )

    try {
        $lines = $Content -split "`n"
        $formattedLines = @()

        foreach ($line in $lines) {
            $trimmedLine = $line.Trim()

            if ([string]::IsNullOrEmpty($trimmedLine)) {
                $formattedLines += ""
            }
            elseif ($trimmedLine.StartsWith("# ")) {
                # Convert to wiki heading
                $formattedLines += "h1. " + $trimmedLine.Substring(2)
            }
            elseif ($trimmedLine.StartsWith("## ")) {
                # Convert to wiki subheading
                $formattedLines += "h2. " + $trimmedLine.Substring(3)
            }
            elseif ($trimmedLine.StartsWith("- ")) {
                # Convert to wiki bullet
                $formattedLines += "* " + $trimmedLine.Substring(2)
            }
            elseif ($trimmedLine.StartsWith("```")) {
                # Convert to wiki code block
                $formattedLines += "{code}"
            }
            else {
                $formattedLines += $trimmedLine
            }
        }

        return ($formattedLines -join "`n")
    }
    catch {
        Write-Warning "Failed to format wiki markup, using plain text: $($_.Exception.Message)"
        return $Content
    }
}

<#
.SYNOPSIS
Executes Jira operations with exponential backoff retry logic.

.DESCRIPTION
Provides robust retry mechanism for Jira API operations with exponential backoff,
jitter, and comprehensive error categorization for different failure types.

.PARAMETER Operation
ScriptBlock containing the Jira operation to execute.

.PARAMETER OperationName
Descriptive name for the operation (for logging).

.PARAMETER IssueKey
Jira issue key for context.

.PARAMETER MaxAttempts
Maximum number of retry attempts.

.OUTPUTS
PSCustomObject with Success, Data, ErrorMessage, and ErrorCategory properties.

.EXAMPLE
$result = Invoke-JiraOperationWithRetry -Operation { Get-JiraIssue -Key "PROJ-123" } -OperationName "GetIssue"

.NOTES
Implements modern retry patterns with intelligent backoff and error categorization.
#>
function Invoke-JiraOperationWithRetry {
    [CmdletBinding()]
    [OutputType([PSCustomObject])]
    param(
        [Parameter(Mandatory = $true)]
        [ScriptBlock]$Operation,

        [Parameter(Mandatory = $true)]
        [string]$OperationName,

        [Parameter(Mandatory = $false)]
        [string]$IssueKey = "Unknown",

        [Parameter(Mandatory = $false)]
        [ValidateRange(1, 10)]
        [int]$MaxAttempts = 5
    )

    $attempt = 1
    $baseDelay = if ($script:Config) { $script:Config.Jira.ApiSettings.RetrySettings.BaseDelay } else { 1 }
    $maxDelay = if ($script:Config) { $script:Config.Jira.ApiSettings.RetrySettings.MaxDelay } else { 32 }
    $jitterEnabled = if ($script:Config) { $script:Config.Jira.ApiSettings.RetrySettings.JitterEnabled } else { $true }

    while ($attempt -le $MaxAttempts) {
        try {
            Write-Verbose "Executing Jira operation '$OperationName' (attempt $attempt/$MaxAttempts)"

            $result = & $Operation

            # Success
            return [PSCustomObject]@{
                Success = $true
                Data = $result
                ErrorMessage = $null
                ErrorCategory = $null
                Attempts = $attempt
            }
        }
        catch {
            $errorCategory = Get-JiraErrorCategory -Exception $_.Exception
            $shouldRetry = Test-JiraErrorRetryable -ErrorCategory $errorCategory

            Write-SecureLog -Message "Jira operation '$OperationName' failed (attempt $attempt/$MaxAttempts): $($_.Exception.Message)" -LogLevel "WARNING" -AuditTrail @{
                Operation = $OperationName
                IssueKey = $IssueKey
                Attempt = $attempt
                ErrorCategory = $errorCategory
                ShouldRetry = $shouldRetry
            }

            if ($attempt -eq $MaxAttempts -or -not $shouldRetry) {
                # Final failure or non-retryable error
                return [PSCustomObject]@{
                    Success = $false
                    Data = $null
                    ErrorMessage = $_.Exception.Message
                    ErrorCategory = $errorCategory
                    Attempts = $attempt
                }
            }

            # Calculate delay with exponential backoff and optional jitter
            $delay = [Math]::Min($baseDelay * [Math]::Pow(2, $attempt - 1), $maxDelay)

            if ($jitterEnabled) {
                # Add jitter (±25% of delay)
                $jitter = $delay * 0.25 * (Get-Random -Minimum -1.0 -Maximum 1.0)
                $delay = [Math]::Max(1, $delay + $jitter)
            }

            Write-Verbose "Retrying Jira operation '$OperationName' in $([Math]::Round($delay, 2)) seconds..."
            Start-Sleep -Seconds $delay

            $attempt++
        }
    }
}

<#
.SYNOPSIS
Categorizes Jira API errors for appropriate retry logic.

.DESCRIPTION
Analyzes exception details to categorize errors as network, authentication,
rate limiting, server errors, or client errors for intelligent retry decisions.

.PARAMETER Exception
The exception object to categorize.

.OUTPUTS
String indicating the error category.

.EXAMPLE
$category = Get-JiraErrorCategory -Exception $_.Exception

.NOTES
Enables intelligent retry logic based on error type.
#>
function Get-JiraErrorCategory {
    [CmdletBinding()]
    [OutputType([string])]
    param(
        [Parameter(Mandatory = $true)]
        [System.Exception]$Exception
    )

    $message = $Exception.Message.ToLower()
    $exceptionType = $Exception.GetType().Name

    # Network-related errors (retryable)
    if ($message -match "timeout|connection|network|dns|socket" -or
        $exceptionType -match "WebException|HttpRequestException|SocketException") {
        return "Network"
    }

    # Authentication errors (not retryable without credential refresh)
    if ($message -match "unauthorized|authentication|401|403" -or
        $exceptionType -match "UnauthorizedAccessException") {
        return "Authentication"
    }

    # Rate limiting (retryable with longer delay)
    if ($message -match "rate limit|429|too many requests") {
        return "RateLimit"
    }

    # Server errors (retryable)
    if ($message -match "internal server error|502|503|504|500" -or
        $exceptionType -match "HttpException") {
        return "ServerError"
    }

    # Client errors (not retryable)
    if ($message -match "bad request|404|400|422|not found") {
        return "ClientError"
    }

    # Unknown errors (retryable with caution)
    return "Unknown"
}

<#
.SYNOPSIS
Determines if a Jira error should be retried.

.DESCRIPTION
Makes retry decisions based on error category, implementing best practices
for different types of API failures.

.PARAMETER ErrorCategory
The categorized error type.

.OUTPUTS
Boolean indicating if the operation should be retried.

.EXAMPLE
$shouldRetry = Test-JiraErrorRetryable -ErrorCategory "Network"

.NOTES
Implements intelligent retry logic to avoid unnecessary API calls.
#>
function Test-JiraErrorRetryable {
    [CmdletBinding()]
    [OutputType([bool])]
    param(
        [Parameter(Mandatory = $true)]
        [string]$ErrorCategory
    )

    switch ($ErrorCategory) {
        "Network" { return $true }      # Network issues are retryable
        "ServerError" { return $true }  # Server errors are retryable
        "RateLimit" { return $true }    # Rate limits are retryable with delay
        "Unknown" { return $true }      # Unknown errors get one retry
        "Authentication" { return $false } # Auth errors need credential refresh
        "ClientError" { return $false }    # Client errors are not retryable
        default { return $false }
    }
}

<#
.SYNOPSIS
Validates file attachments before uploading to Jira.

.DESCRIPTION
Performs comprehensive validation of files including size limits,
file type restrictions, and content scanning for security.

.PARAMETER FilePath
Path to the file to validate.

.OUTPUTS
PSCustomObject with IsValid and ErrorMessage properties.

.EXAMPLE
$validation = Test-JiraAttachmentValidation -FilePath "C:\Logs\admin.log"

.NOTES
Implements security best practices for file uploads.
#>
function Test-JiraAttachmentValidation {
    [CmdletBinding()]
    [OutputType([PSCustomObject])]
    param(
        [Parameter(Mandatory = $true)]
        [string]$FilePath
    )

    try {
        $fileInfo = Get-Item -Path $FilePath
        $maxSizeMB = if ($script:Config) { $script:Config.Jira.AttachmentSettings.MaxFileSizeMB } else { 10 }
        $allowedTypes = if ($script:Config) { $script:Config.Jira.AttachmentSettings.AllowedFileTypes } else { @('.log', '.txt', '.pdf') }

        # Check file size
        $fileSizeMB = $fileInfo.Length / 1MB
        if ($fileSizeMB -gt $maxSizeMB) {
            return [PSCustomObject]@{
                IsValid = $false
                ErrorMessage = "File size ($([Math]::Round($fileSizeMB, 2)) MB) exceeds maximum allowed size ($maxSizeMB MB)"
            }
        }

        # Check file extension
        $fileExtension = $fileInfo.Extension.ToLower()
        if ($allowedTypes -notcontains $fileExtension) {
            return [PSCustomObject]@{
                IsValid = $false
                ErrorMessage = "File type '$fileExtension' is not allowed. Allowed types: $($allowedTypes -join ', ')"
            }
        }

        # Additional security checks could be added here
        # (virus scanning, content validation, etc.)

        return [PSCustomObject]@{
            IsValid = $true
            ErrorMessage = $null
        }
    }
    catch {
        return [PSCustomObject]@{
            IsValid = $false
            ErrorMessage = "File validation failed: $($_.Exception.Message)"
        }
    }
}

#endregion



#region Active Directory Functions

<#
.SYNOPSIS
Retrieves user details from Active Directory with optimized queries and caching.

.DESCRIPTION
Performs efficient Active Directory queries with configurable property filtering,
result caching, and comprehensive error handling. Implements performance
optimizations for large domain environments.

.PARAMETER UserUPN
The User Principal Name to search for.

.PARAMETER Properties
Array of properties to retrieve. Uses optimized defaults if not specified.

.PARAMETER UseCache
Switch to enable result caching for improved performance.

.PARAMETER CacheExpirationMinutes
Cache expiration time in minutes.

.EXAMPLE
$user = Get-OptimizedADUserDetails -UserUPN "<EMAIL>" -UseCache

.NOTES
Implements AD query best practices with performance optimizations and caching.
#>
function Get-OptimizedADUserDetails {
    [CmdletBinding()]
    [OutputType([Microsoft.ActiveDirectory.Management.ADUser])]
    param(
        [Parameter(Mandatory = $true)]
        [ValidatePattern('^[\w][\w\.-]{1,62}[\w]@[\w][\w-]{1,62}\.[a-zA-Z]{2,}$')]
        [string]$UserUPN,

        [Parameter(Mandatory = $false)]
        [string[]]$Properties,

        [Parameter(Mandatory = $false)]
        [switch]$UseCache,

        [Parameter(Mandatory = $false)]
        [ValidateRange(1, 1440)]
        [int]$CacheExpirationMinutes = 15
    )

    try {
        Write-SecureLog -Message "Retrieving AD user details" -LogLevel "INFO" -AuditTrail @{
            Operation = "ADUserQuery"
            UserUPN = Get-StringHash -InputString $UserUPN
            CacheEnabled = $UseCache.IsPresent
        }

        # Use configured properties or defaults
        if (-not $Properties) {
            $Properties = if ($script:Config) {
                $script:Config.ActiveDirectory.QueryOptimization.UserProperties
            } else {
                @('GivenName', 'Surname', 'DisplayName', 'SamAccountName', 'UserPrincipalName', 'EmailAddress', 'Enabled', 'DistinguishedName')
            }
        }

        # Check cache if enabled
        if ($UseCache -and $script:ADUserCache) {
            $cacheKey = "$UserUPN|$($Properties -join ',')"
            $cachedResult = $script:ADUserCache[$cacheKey]

            if ($cachedResult -and $cachedResult.Timestamp -gt (Get-Date).AddMinutes(-$CacheExpirationMinutes)) {
                Write-Verbose "Returning cached AD user result for $UserUPN"
                return $cachedResult.Data
            }
        }

        # Configure query parameters
        $queryParams = @{
            Filter = "UserPrincipalName -eq '$UserUPN'"
            Properties = $Properties
            ErrorAction = 'Stop'
        }

        # Add result limit if configured
        if ($script:Config -and $script:Config.ActiveDirectory.QueryOptimization.MaxResults) {
            $queryParams.ResultSetSize = $script:Config.ActiveDirectory.QueryOptimization.MaxResults
        }

        # Execute query with timeout
        $user = $null
        $queryJob = Start-Job -ScriptBlock {
            param($QueryParams)
            Import-Module ActiveDirectory -Force
            Get-ADUser @QueryParams
        } -ArgumentList $queryParams

        $timeout = if ($script:Config) {
            $script:Config.ActiveDirectory.QueryOptimization.QueryTimeout
        } else {
            30
        }

        if (Wait-Job -Job $queryJob -Timeout $timeout) {
            $user = Receive-Job -Job $queryJob
            Remove-Job -Job $queryJob -Force
        } else {
            Remove-Job -Job $queryJob -Force
            throw "AD query timeout after $timeout seconds"
        }

        if (-not $user) {
            Write-SecureLog -Message "User not found in Active Directory" -LogLevel "WARNING" -AuditTrail @{
                Operation = "ADUserNotFound"
                UserUPN = Get-StringHash -InputString $UserUPN
            }
            return $null
        }

        # Cache result if enabled
        if ($UseCache) {
            if (-not $script:ADUserCache) {
                $script:ADUserCache = @{}
            }

            $cacheKey = "$UserUPN|$($Properties -join ',')"
            $script:ADUserCache[$cacheKey] = @{
                Data = $user
                Timestamp = Get-Date
            }
        }

        Write-SecureLog -Message "Successfully retrieved AD user details" -LogLevel "INFO" -AuditTrail @{
            Operation = "ADUserQuerySuccess"
            UserUPN = Get-StringHash -InputString $UserUPN
            PropertiesCount = $Properties.Count
        }

        return $user
    }
    catch [Microsoft.ActiveDirectory.Management.ADIdentityNotFoundException] {
        Write-SecureLog -Message "User not found in Active Directory: $($_.Exception.Message)" -LogLevel "WARNING"
        return $null
    }
    catch [System.TimeoutException] {
        Write-SecureLog -Message "AD query timeout: $($_.Exception.Message)" -LogLevel "ERROR"
        throw
    }
    catch {
        Write-SecureLog -Message "Failed to retrieve AD user details: $($_.Exception.Message)" -LogLevel "ERROR" -AuditTrail @{
            Operation = "ADUserQueryError"
            ErrorType = $_.Exception.GetType().Name
        }
        throw
    }
}

<#
.SYNOPSIS
Validates and retrieves a User Principal Name with enhanced input validation.

.DESCRIPTION
Provides comprehensive UPN validation with pattern matching, domain verification,
and existence checking. Supports different validation modes for create, delete,
and reset operations.

.PARAMETER Domain
The domain to validate against.

.PARAMETER ForDeletion
Switch indicating validation is for deletion operation.

.PARAMETER ForReset
Switch indicating validation is for reset operation.

.PARAMETER MaxAttempts
Maximum number of input attempts before failing.

.EXAMPLE
$upn = Get-ValidatedUPN -Domain "domain.com" -MaxAttempts 3

.NOTES
Implements comprehensive input validation with security best practices.
#>
function Get-ValidatedUPN {
    [CmdletBinding()]
    [OutputType([string])]
    param(
        [Parameter(Mandatory = $true)]
        [ValidatePattern('^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')]
        [string]$Domain,

        [Parameter(Mandatory = $false)]
        [switch]$ForDeletion,

        [Parameter(Mandatory = $false)]
        [switch]$ForReset,

        [Parameter(Mandatory = $false)]
        [ValidateRange(1, 10)]
        [int]$MaxAttempts = 3
    )

    $attempt = 1
    $validUPN = $null

    while ($attempt -le $MaxAttempts -and -not $validUPN) {
        try {
            Write-Host "`nAttempt $attempt of $MaxAttempts" -ForegroundColor Cyan

            # Provide context-specific prompts
            $promptMessage = switch ($true) {
                $ForDeletion { "Enter the UPN of the user whose admin account you want to DELETE" }
                $ForReset { "Enter the UPN of the user whose admin account you want to RESET" }
                default { "Enter the UPN of the user for whom you want to create an admin account" }
            }

            $upn = Read-Host $promptMessage

            # Input validation
            if ([string]::IsNullOrWhiteSpace($upn)) {
                Write-Host "UPN cannot be empty. Please try again." -ForegroundColor Red
                $attempt++
                continue
            }

            # Sanitize input
            $upn = $upn.Trim().ToLower()

            # Pattern validation
            $upnPattern = if ($script:Config) {
                $script:Config.Security.InputValidation.AllowedPatterns.UPN
            } else {
                '^[\w][\w\.-]{1,62}[\w]@[\w][\w-]{1,62}\.[a-zA-Z]{2,}$'
            }

            if ($upn -notmatch $upnPattern) {
                Write-Host "Invalid UPN format. Please enter a valid email address format (e.g., <EMAIL>)." -ForegroundColor Red
                $attempt++
                continue
            }

            # Domain validation
            $upnDomain = $upn.Split('@')[1]
            if ($upnDomain -ne $Domain.ToLower()) {
                Write-Host "UPN domain '$upnDomain' does not match expected domain '$Domain'." -ForegroundColor Red
                $attempt++
                continue
            }

            # Check if user exists in AD
            $existingUser = Get-OptimizedADUserDetails -UserUPN $upn -UseCache
            if (-not $existingUser) {
                Write-Host "User '$upn' not found in Active Directory." -ForegroundColor Red
                $attempt++
                continue
            }

            # Check admin account existence based on operation type
            if ($ForDeletion -or $ForReset) {
                $adminSamAccountName = "$($existingUser.SamAccountName)-a"
                $adminAccountExists = Get-ADUser -Filter "SamAccountName -eq '$adminSamAccountName'" -ErrorAction SilentlyContinue

                if (-not $adminAccountExists) {
                    Write-Host "No admin account exists for this user." -ForegroundColor Red
                    $attempt++
                    continue
                }
            }

            # All validations passed
            $validUPN = $upn

            Write-SecureLog -Message "UPN validation successful" -LogLevel "INFO" -AuditTrail @{
                Operation = "UPNValidation"
                ValidationMode = if ($ForDeletion) { "Delete" } elseif ($ForReset) { "Reset" } else { "Create" }
                Attempts = $attempt
                UserHash = Get-StringHash -InputString $upn
            }

        }
        catch {
            Write-Host "Error validating UPN: $($_.Exception.Message)" -ForegroundColor Red
            Write-SecureLog -Message "UPN validation error: $($_.Exception.Message)" -LogLevel "ERROR"
            $attempt++
        }
    }

    if (-not $validUPN) {
        $errorMsg = "Failed to get valid UPN after $MaxAttempts attempts."
        Write-SecureLog -Message $errorMsg -LogLevel "ERROR"
        throw $errorMsg
    }

    return $validUPN
}

# Maintain backward compatibility
function Get-ValidUPN {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Domain,

        [Parameter(Mandatory = $false)]
        [switch]$ForDeletion,

        [Parameter(Mandatory = $false)]
        [switch]$ForReset
    )

    return Get-ValidatedUPN -Domain $Domain -ForDeletion:$ForDeletion -ForReset:$ForReset
}

# Maintain backward compatibility
function Get-ADUserDetails {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$UserUPN
    )

    return Get-OptimizedADUserDetails -UserUPN $UserUPN -UseCache
}

#endregion

#region Email Functions

function Send-EmailWithRetry {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$From,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$To,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$Subject,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$Body,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$SmtpServer,

        [string[]]$Attachments,

        [switch]$UseSSL,

        [int]$Port = 25
    )
    
    $maxRetries = 3
    $retryDelay = 2
    
    for ($i = 1; $i -le $maxRetries; $i++) {
        try {
            $params = @{
                From       = $From
                To         = $To
                Subject    = $Subject
                Body      = $Body
                SmtpServer = $SmtpServer
                Port      = $Port
                UseSSL    = $UseSSL.IsPresent
            }
            
            if ($Attachments) { 
                $params.Attachments = $Attachments 
            }
            
            # Add error handling for email parameters
            Write-Log -Message "Attempting to send email (Attempt $i of $maxRetries)" -LogLevel "DEBUG"
            Write-Log -Message "Email parameters: To=$To, From=$From, Subject=$Subject, SmtpServer=$SmtpServer" -LogLevel "DEBUG"
            
            Send-MailMessage @params -ErrorAction Stop
            
            Write-Log -Message "Email sent successfully to $To" -LogLevel "INFO"
            return $true
        }
        catch {
            $errorMessage = "Email attempt $i failed: $($_.Exception.Message)"
            Write-Log -Message $errorMessage -LogLevel "WARNING"
            
            if ($_.Exception.Message -match "secure connections") {
                Write-Log -Message "SSL connection failed, attempting without SSL..." -LogLevel "WARNING"
                $params.UseSSL = $false
                try {
                    Send-MailMessage @params -ErrorAction Stop
                    Write-Log -Message "Email sent successfully without SSL to $To" -LogLevel "INFO"
                    return $true
                }
                catch {
                    Write-Log -Message "Failed to send email without SSL: $($_.Exception.Message)" -LogLevel "ERROR"
                }
            }
            
            if ($i -lt $maxRetries) {
                $waitTime = $retryDelay * [Math]::Pow(2, $i-1)
                Write-Log -Message "Waiting $waitTime seconds before retry..." -LogLevel "INFO"
                Start-Sleep -Seconds $waitTime
            }
        }
    }
    
    Write-Log -Message "Failed to send email after $maxRetries attempts" -LogLevel "ERROR"
    return $false
}

# Sends an email notification with details about the admin account creation
function Send-EmailNotification {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$LogPath,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$AdminUserUPN,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$Password,

        [Parameter(Mandatory = $true)]
        [ValidatePattern('^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')]
        [string]$StandardUserEmail,

        [ValidateNotNullOrEmpty()]
        [string]$EmailFrom = $DefaultEmailFrom,

        [ValidateNotNullOrEmpty()]
        [string]$SmtpServer = $DefaultSmtpServer
    )

    # Retrieve the full name and username of the current user
    $currentUserFullName = GetCurrentUserName
    $currentUserSamAccountName = whoami

    # Get log content
    try {
        $logContent = Get-Content -Path $LogPath -Raw
        if ([string]::IsNullOrEmpty($logContent)) {
            Write-Log -Message "Warning: Log file is empty" -LogLevel "WARNING"
            $logContent = "No log content available."
        }
    }
    catch {
        Write-Log -Message "Error reading log file: $_" -LogLevel "ERROR"
        $logContent = "Error reading log content."
    }

    # Prepare shared email parameters
    $emailParams = @{
        From       = $EmailFrom
        SmtpServer = $SmtpServer
    }

    # Send notification to support team
    $supportEmailParams = $emailParams.Clone()
    $supportEmailParams += @{
        To         = "<EMAIL>"
        Subject    = "Admin Account Creation for $AdminUserUPN"
        Body       = @"
An admin account has been created for $AdminUserUPN by $currentUserFullName ($currentUserSamAccountName).

DETAILED LOG:
=============
$logContent

This is an automated notification.
"@
        Attachments = $LogPath
        UseSSL     = $false
        Port       = 25
    }

    # Send credentials to standard user
    $userEmailParams = $emailParams.Clone()
    $userEmailParams += @{
        To      = $StandardUserEmail
        Subject = "Your Admin Account Credentials"
        Body    = @"
Hello,

Your admin account has been created with the following credentials:

Username: $AdminUserUPN
Password: $Password

Please change this password upon your first login.

CREATION LOG:
============
$logContent

Best regards,
IT Support Team
"@
        UseSSL  = $false
        Port    = 25
    }

    # Send emails with retry and capture results
    $results = @(
        Send-EmailWithRetry @supportEmailParams
        Send-EmailWithRetry @userEmailParams
    )

    # Report results
    if ($results -notcontains $false) {
        $message = "Email notifications sent successfully."
    }
    else {
        $message = "Failed to send one or more email notifications."
    }
    
    Write-Host $message
}

function Send-DeletionEmailNotification {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$LogPath,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$AdminUserUPN,

        [Parameter(Mandatory = $true)]
        [ValidatePattern('^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')]
        [string]$StandardUserEmail,

        [ValidateNotNullOrEmpty()]
        [string]$EmailFrom = $DefaultEmailFrom,

        [ValidateNotNullOrEmpty()]
        [string]$SmtpServer = $DefaultSmtpServer
    )

    # Retrieve the full name and username of the current user
    $currentUserFullName = GetCurrentUserName
    $currentUserSamAccountName = whoami

    # Get log content
    try {
        $logContent = Get-Content -Path $LogPath -Raw
        if ([string]::IsNullOrEmpty($logContent)) {
            Write-Log -Message "Warning: Log file is empty" -LogLevel "WARNING"
            $logContent = "No log content available."
        }
    }
    catch {
        Write-Log -Message "Error reading log file: $_" -LogLevel "ERROR"
        $logContent = "Error reading log content."
    }

    # Prepare shared email parameters
    $emailParams = @{
        From       = $EmailFrom
        SmtpServer = $SmtpServer
    }

    # Send notification to support team
    $supportEmailParams = $emailParams.Clone()
    $supportEmailParams += @{
        To         = "<EMAIL>"
        Subject    = "Admin Account Deletion for $AdminUserUPN"
        Body       = @"
An admin account has been deleted: $AdminUserUPN by $currentUserFullName ($currentUserSamAccountName).

DETAILED LOG:
=============
$logContent

This is an automated notification.
"@
        Attachments = $LogPath
        UseSSL     = $false
        Port       = 25
    }

    # Send notification to standard user
    $userEmailParams = $emailParams.Clone()
    $userEmailParams += @{
        To      = $StandardUserEmail
        Subject = "Your Admin Account Has Been Deleted"
        Body    = @"
Hello,

Your admin account ($AdminUserUPN) has been deleted from Active Directory.

If you believe this was done in error, please contact IT Support.

DELETION LOG:
============
$logContent

Best regards,
IT Support Team
"@
        UseSSL  = $false
        Port    = 25
    }

    # Send emails with retry and capture results
    $results = @(
        Send-EmailWithRetry @supportEmailParams
        Send-EmailWithRetry @userEmailParams
    )

    # Report results
    if ($results -notcontains $false) {
        $message = "Email notifications sent successfully."
    }
    else {
        $message = "Failed to send one or more email notifications."
    }
    
    Write-Host $message
}

function Send-ResetEmailNotification {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$LogPath,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$AdminUserUPN,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$Password,

        [Parameter(Mandatory = $true)]
        [ValidatePattern('^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')]
        [string]$StandardUserEmail,

        [ValidateNotNullOrEmpty()]
        [string]$EmailFrom = $DefaultEmailFrom,

        [ValidateNotNullOrEmpty()]
        [string]$SmtpServer = $DefaultSmtpServer
    )

    # Retrieve the full name and username of the current user
    $currentUserFullName = GetCurrentUserName
    $currentUserSamAccountName = whoami

    # Get log content
    try {
        $logContent = Get-Content -Path $LogPath -Raw
        if ([string]::IsNullOrEmpty($logContent)) {
            Write-Log -Message "Warning: Log file is empty" -LogLevel "WARNING"
            $logContent = "No log content available."
        }
    }
    catch {
        Write-Log -Message "Error reading log file: $_" -LogLevel "ERROR"
        $logContent = "Error reading log content."
    }

    # Prepare shared email parameters
    $emailParams = @{
        From       = $EmailFrom
        SmtpServer = $SmtpServer
    }

    # Send notification to support team
    $supportEmailParams = $emailParams.Clone()
    $supportEmailParams += @{
        To         = "<EMAIL>"
        Subject    = "Admin Account Reset for $AdminUserUPN"
        Body       = @"
An admin account has been reset: $AdminUserUPN by $currentUserFullName ($currentUserSamAccountName).

DETAILED LOG:
=============
$logContent

This is an automated notification.
"@
        Attachments = $LogPath
        UseSSL     = $false
        Port       = 25
    }

    # Send reset notification to standard user
    $userEmailParams = $emailParams.Clone()
    $userEmailParams += @{
        To      = $StandardUserEmail
        Subject = "Your Admin Account Has Been Reset"
        Body    = @"
Hello,

Your admin account ($AdminUserUPN) has been reset with the following credentials:

Username: $AdminUserUPN
Password: $Password

Please change this password upon your first login.
The account is currently disabled and will need to be enabled by IT Support.

RESET LOG:
==========
$logContent

Best regards,
IT Support Team
"@
        UseSSL  = $false
        Port    = 25
    }

    # Send emails with retry and capture results
    $results = @(
        Send-EmailWithRetry @supportEmailParams
        Send-EmailWithRetry @userEmailParams
    )

    # Report results
    if ($results -notcontains $false) {
        $message = "Email notifications sent successfully."
    }
    else {
        $message = "Failed to send one or more email notifications."
    }
    
    Write-Host $message
}

# Retrieves the full name of the current user
function GetCurrentUserName {
    try {
        # Use [System.Security.Principal.WindowsIdentity]::GetCurrent().Name for domain\username
        $currentUserSamAccountName = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name.Split('\')[1]

        $currentUser = Get-ADUser -Identity $currentUserSamAccountName -Properties GivenName, Surname -ErrorAction Stop
        return "$($currentUser.GivenName) $($currentUser.Surname)"
    } catch {
        Write-Log -Message "Error retrieving current user name: $_" -LogLevel "ERROR"
        return "Unknown User"
    }
}

# Writes a log message with a timestamp and the current user's name
function Write-Log {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Message,

        [ValidateSet('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')]
        [string]$LogLevel = 'INFO'
    )

    # If CurrentLogPath is not set, create a temporary log
    if (-not $script:CurrentLogPath) {
        $script:CurrentLogPath = Join-Path $script:LogDirectory "AdminCreation_TEMP_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
    }

    # Get the current date and time with milliseconds
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss.fff"
    
    # Get user details
    $currentUserName = $env:USERNAME
    $currentUserFullName = GetCurrentUserName

    # Color coding for console output
    $colorMap = @{
        'DEBUG' = 'Gray'
        'INFO' = 'White'
        'WARNING' = 'Yellow'
        'ERROR' = 'Red'
        'CRITICAL' = 'DarkRed'
    }

    # Construct the log message
    $logMessage = "{0,-23} | {1,-15} | {2,-40} | {3,-8} | {4}" -f `
        $timestamp, `
        $currentUserName, `
        $currentUserFullName, `
        $LogLevel.ToUpper(), `
        $Message

    try {
        # Create directory if it doesn't exist
        $logDir = Split-Path -Path $script:CurrentLogPath -Parent
        if (-not (Test-Path -Path $logDir)) {
            New-Item -ItemType Directory -Path $logDir -Force | Out-Null
        }

        # Write to log file
        Add-Content -Path $script:CurrentLogPath -Value $logMessage
        
        # Console output
        Write-Host $logMessage -ForegroundColor $colorMap[$LogLevel]
    }
    catch {
        $errorMsg = "Failed to write log: $($_.Exception.Message)"
        Write-Error $errorMsg
        Write-Host $errorMsg -ForegroundColor Red
    }
}

function New-SecurePassword {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [ValidateRange(8, 128)]
        [int]$Length = 12,

        [Parameter(Mandatory = $false)]
        [ValidateRange(1, 10)]
        [int]$MinSpecialChars = 2
    )

    try {
        Add-Type -AssemblyName System.Web

        # Use configuration if available
        if ($script:Config) {
            $Length = $script:Config.ActiveDirectory.PasswordPolicy.MinLength
            $MinSpecialChars = $script:Config.ActiveDirectory.PasswordPolicy.MinSpecialChars
        }

        $password = [System.Web.Security.Membership]::GeneratePassword($Length, $MinSpecialChars)

        Write-SecureLog -Message "Generated secure password with length $Length and $MinSpecialChars special characters" -LogLevel "INFO" -AuditTrail @{
            Operation = "PasswordGeneration"
            PasswordLength = $Length
            SpecialChars = $MinSpecialChars
        }

        return $password
    }
    catch {
        Write-SecureLog -Message "Failed to generate password: $($_.Exception.Message)" -LogLevel "ERROR"
        throw
    }
}

function New-StandardUPN {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$FirstName,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$LastName,

        [Parameter(Mandatory = $true)]
        [ValidatePattern('^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')]
        [string]$Domain
    )

    try {
        # Sanitize input
        $cleanFirstName = $FirstName.Trim() -replace '[^a-zA-Z]', ''
        $cleanLastName = $LastName.Trim() -replace '[^a-zA-Z]', ''

        if ([string]::IsNullOrEmpty($cleanFirstName) -or [string]::IsNullOrEmpty($cleanLastName)) {
            throw "Invalid first name or last name after sanitization"
        }

        # Simple sanitization and formatting: lowercase, replace spaces with dots, remove other invalid chars, clean up dots
        $firstNameClean = $cleanFirstName -replace '\s+', '.' -replace '[^\w.-]', ''
        $lastNameClean = $cleanLastName -replace '\s+', '.' -replace '[^\w.-]', ''
        $upn = "$($firstNameClean.ToLower()).$($lastNameClean.ToLower())@$Domain"
        $upn = $upn -replace '\.\.', '.' # Replace double dots
        $upn = $upn.Trim('.') # Remove leading/trailing dots

        Write-SecureLog -Message "Constructed UPN for user" -LogLevel "INFO" -AuditTrail @{
            Operation = "UPNConstruction"
            Domain = $Domain
            UPNHash = Get-StringHash -InputString $upn
        }

        return $upn
    }
    catch {
        Write-SecureLog -Message "Failed to construct UPN: $($_.Exception.Message)" -LogLevel "ERROR"
        throw
    }
}

function Get-ValidUPN {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $false)]
        [ValidateNotNullOrEmpty()]
        [string]$Domain = "jeragm.com",

        [Parameter(Mandatory = $false)]
        [switch]$ForDeletion,

        [Parameter(Mandatory = $false)]
        [switch]$ForReset
    )

    begin {
        [string]$currentUser = $env:USERNAME
        Write-Log "Starting UPN validation process by user: $currentUser"
    }

    process {
        do {
            try {
                Write-Log "Requesting UPN input from user"
                [string]$upn = Read-Host "Enter the UPN of the existing standard account (e.g., user.name@$Domain)"
                
                if ([string]::IsNullOrWhiteSpace($upn)) {
                    throw "UPN cannot be empty or whitespace"
                }

                Write-Log "Sanitizing UPN input: $upn"
                $upn = [System.Web.HttpUtility]::HtmlEncode($upn).Trim()
                $upn = $upn -replace '[^\w.-@]', ''
                Write-Log "Sanitized UPN result: $upn"

                [string]$upnPattern = '^[\w][\w\.-]{1,62}[\w]@[\w][\w-]{1,62}\.[a-zA-Z]{2,}$'
                
                if (-not $upn -match $upnPattern) {
                    Write-Host "Invalid UPN format. Only alphanumeric characters, dots and hyphens allowed." -ForegroundColor Red
                    Write-Log "Invalid UPN format detected: $upn"
                    $isValid = $false
                    continue
                }

                if (-not $upn.Contains('@')) {
                    $upn = "$upn@$Domain"
                    Write-Log "Domain appended to UPN: $upn"
                }
                elseif (-not $upn.EndsWith("@$Domain")) {
                    Write-Host "UPN must end with @$Domain" -ForegroundColor Red
                    Write-Log "Invalid domain in UPN: $upn"
                    $isValid = $false
                    continue
                }

                try {
                    $existingUser = Get-ADUser -Filter "UserPrincipalName -eq '$upn'" -ErrorAction Stop
                    if (-not $existingUser) {
                        Write-Host "Standard user account not found in Active Directory" -ForegroundColor Red
                        Write-Log "UPN validation failed - Standard user not found: $upn"
                        $isValid = $false
                        continue
                    }

                    # Check if admin account exists (only for deletion and reset operations)
                    if ($ForDeletion -or $ForReset) {
                        $adminSamAccountName = "$($existingUser.SamAccountName)-a"
                        $adminAccountExists = Get-ADUser -Filter "SamAccountName -eq '$adminSamAccountName'" -ErrorAction SilentlyContinue

                        if (-not $adminAccountExists) {
                            Write-Host "No admin account exists for this user" -ForegroundColor Red
                            Write-Log "UPN validation failed - No admin account exists for: $upn"
                            $isValid = $false
                            continue
                        }
                    }
                    # Note: For creation operations, admin account existence check is now handled in Create-StdAdminAccount function

                    $isValid = $true
                }
                catch {
                    Write-Log "Error checking AD for existing UPN: $_"
                    throw "Failed to verify UPN in AD: $_"
                }
            }
            catch {
                Write-Host "Error processing UPN: $_" -ForegroundColor Red
                Write-Log "Error in UPN validation process: $_"
                $isValid = $false
            }
        } while (-not $isValid)
    }

    end {
        Write-Log "UPN validation completed successfully. Final UPN: $upn"
        return [string]$upn.ToLower()
    }
}

function Get-ADUserDetails {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$UserUPN
    )

    begin {
        Write-Log -Message "Starting AD user details retrieval for UPN: $UserUPN" -LogLevel "INFO"
    }

    process {
        try {
            # Sanitize input
            $UserUPN = $UserUPN.Trim()
            
            # Validate UPN format
            if (-not ($UserUPN -match '^[\w][\w\.-]{0,63}@[\w][\w-]{0,63}\.[a-zA-Z]{2,}$')) {
                throw "Invalid UPN format: $UserUPN"
            }

            # Get user details with specific properties needed
            $properties = @(
                'GivenName',
                'Surname',
                'DisplayName',
                'SamAccountName',
                'UserPrincipalName',
                'EmailAddress',
                'Enabled',
                'DistinguishedName'
            )

            $user = Get-ADUser -Filter "UserPrincipalName -eq '$UserUPN'" `
                             -Properties $properties `
                             -ErrorAction Stop

            if ($null -eq $user) {
                throw "User not found in AD"
            }

            # Verify account is enabled
            if (-not $user.Enabled) {
                Write-Log -Message "Warning: User account is disabled: $UserUPN" -LogLevel "WARNING"
            }

            Write-Log -Message "Successfully retrieved AD user details for: $UserUPN" -LogLevel "INFO"
            return $user

        }
        catch [Microsoft.ActiveDirectory.Management.ADIdentityNotFoundException] {
            Write-Log -Message "User not found in AD: $UserUPN" -LogLevel "ERROR"
            Write-Host "User not found in Active Directory." -ForegroundColor Red
            return $null
        }
        catch [Microsoft.ActiveDirectory.Management.ADServerDownException] {
            Write-Log -Message "AD server connection failed for user lookup: $UserUPN" -LogLevel "ERROR"
            Write-Host "Unable to connect to Active Directory server." -ForegroundColor Red
            return $null
        }
        catch {
            Write-Log -Message "Error retrieving AD user details for $UserUPN : $_" -LogLevel "ERROR"
            Write-Host "Failed to retrieve user details: $_" -ForegroundColor Red
            return $null
        }
    }

    end {
        Write-Log -Message "Completed AD user details retrieval process" -LogLevel "INFO"
    }
}

#Selects ths OU the Admin account will be created into
function Select-OU {
    <#
    .SYNOPSIS
    Prompts the user to select an Organizational Unit (OU) for an admin account.

    .DESCRIPTION
    This function allows the user to select an OU or automatically determine it based on predefined mappings and Jira ticket information.
    It logs the selection process and returns the corresponding OU path.

    .OUTPUTS
    String - The selected OU path.
    #>

    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $false)]
        [string]$OfficeLocation
    )

    # Log the start of the OU selection process
    Write-Log -Message "Starting OU selection process" -LogLevel "DEBUG"

    # Initialize the OU path variable
    [string]$ouPath = $null

    # Define OU Mappings
    $officeLocationToOU = @{
        "singapore"      = "OU=Singapore,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com"
        "united kingdom" = "OU=London,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com"
        "london" = "OU=London,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com" # Added "london" mapping
    }

    try {
        if ($OfficeLocation -and $officeLocationToOU.ContainsKey($OfficeLocation.ToLower())) {
            # OU determined from provided Office Location (e.g., Jira ticket)
            $ouPath = $officeLocationToOU[$OfficeLocation.ToLower()]
            Write-Log -Message "Automatically selected OU based on Office Location '$OfficeLocation': $ouPath" -LogLevel "INFO"
        }

        if (-not $ouPath) {
            # Manual selection if no office location is provided or no mapping is found
            do {
                # Prompt the user for OU selection
                Write-Host "Unable to automatically determine OU. Please select the OU for the admin account:"
                Write-Host "1 for London, 2 for Singapore."
                [string]$selection = Read-Host "Enter 1 or 2"
                Write-Log -Message "User entered selection: $selection" -LogLevel "DEBUG"

                # Determine the OU path based on user selection
                switch ($selection) {
                    "1" {
                        $ouPath = "OU=London,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com"
                        Write-Log -Message "Selected OU: London" -LogLevel "INFO"
                    }
                    "2" {
                        $ouPath = "OU=Singapore,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com"
                        Write-Log -Message "Selected OU: Singapore" -LogLevel "INFO"
                    }
                    default {
                        Write-Host "Invalid selection. Please select either 1 or 2."
                        Write-Log -Message "Invalid OU selection: $selection" -LogLevel "WARNING"
                    }
                }
            } while (-not $ouPath)
        }
    }
    catch {
        # Log any exceptions that occur during the selection process
        Write-Log -Message "An error occurred during OU selection: $_" -LogLevel "ERROR"
        throw
    }

    # Log the completion of the OU selection process
    Write-Log -Message "OU selection process completed with OU: $ouPath" -LogLevel "DEBUG"
    
    # Return the selected OU path
    return $ouPath
}
#endregion

function Reset-StdAdminAccount {
    [CmdletBinding()]
    param()

    try {
        # Get standard user UPN first (with ForReset flag)
        $standardUserUPN = Get-ValidUPN -Domain $script:DefaultDomain -ForReset
        
        # Initialize logging with the standard user's UPN
        $logPath = Initialize-SecureLogging -StandardUserUPN $standardUserUPN
        Write-Log -Message "Starting admin account reset process" -LogLevel "INFO"

        # Get standard user details
        $standardUser = Get-ADUserDetails -UserUPN $standardUserUPN

        if ($null -eq $standardUser) {
            throw "Unable to retrieve standard user details"
        }

        # Construct admin account SamAccountName
        $adminSamAccountName = "$($standardUser.SamAccountName)-a"

        # Check if admin account exists and get full details
        $adminAccount = Get-ADUser -Filter "SamAccountName -eq '$adminSamAccountName'" `
                                 -Properties DisplayName, UserPrincipalName, DistinguishedName, Created, Modified, Enabled `
                                 -ErrorAction Stop

        if ($null -eq $adminAccount) {
            throw "No admin account found for user $($standardUser.DisplayName) (Expected: $adminSamAccountName)"
        }

        # Prompt for confirmation with detailed information
        Write-Host "`nAdmin Account Details:" -ForegroundColor Yellow
        Write-Host "Display Name: $($adminAccount.DisplayName)" -ForegroundColor Cyan
        Write-Host "Username: $($adminAccount.UserPrincipalName)" -ForegroundColor Cyan
        Write-Host "SAM Account Name: $($adminAccount.SamAccountName)" -ForegroundColor Cyan
        Write-Host "Distinguished Name: $($adminAccount.DistinguishedName)" -ForegroundColor Cyan
        Write-Host "Account Status: $(if($adminAccount.Enabled){'Enabled'}else{'Disabled'})" -ForegroundColor Cyan
        Write-Host "Created: $($adminAccount.Created)" -ForegroundColor Cyan
        Write-Host "Last Modified: $($adminAccount.Modified)" -ForegroundColor Cyan

        $confirmation = Read-Host "`nAre you sure you want to reset this admin account? (Y/N)"

        if ($confirmation -eq 'Y') {
            # Generate new password
            $newPassword = New-SecurePassword
            $securePassword = ConvertTo-SecureString $newPassword -AsPlainText -Force

            # Reset the account
            Set-ADAccountPassword -Identity $adminAccount.DistinguishedName -NewPassword $securePassword -Reset -ErrorAction Stop
            Set-ADUser -Identity $adminAccount.DistinguishedName -ChangePasswordAtLogon $true -Enabled $false -ErrorAction Stop

            $successMessage = "Admin account $($adminAccount.UserPrincipalName) has been successfully reset"
            Write-Host "`n$successMessage" -ForegroundColor Green
            Write-Log -Message $successMessage -LogLevel "INFO"

            # Send email notifications
            if (-not [string]::IsNullOrEmpty($standardUser.EmailAddress)) {
                $emailParams = @{
                    LogPath = $script:CurrentLogPath
                    AdminUserUPN = $adminAccount.UserPrincipalName
                    Password = $newPassword
                    StandardUserEmail = $standardUser.EmailAddress
                }
                Send-ResetEmailNotification @emailParams
            }
            else {
                Write-Log -Message "No email address found for standard user" -LogLevel "WARNING"
            }
        }
        else {
            Write-Host "`nReset cancelled by user" -ForegroundColor Yellow
            Write-Log -Message "Admin account reset cancelled by user" -LogLevel "INFO"
        }
    }
    catch {
        $errorMessage = "Failed to reset admin account: $_"
        Write-Log -Message $errorMessage -LogLevel "ERROR"
        Write-Host $errorMessage -ForegroundColor Red
        throw
    }
}

function New-AdminAccount {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        $Issue
    )
    try {
        $standardUserUPN = $null
        $ouPath = $null
        $firstName = $null
        $lastName = $null
        $officeLocation = $null
        $itAdminAccount = $null # To store the value of "IT Admin Account?" field
        $department = $null
        $jobTitle = $null
        $modelAccount = $null # To store the value of "Model Account" field
        $sourceJiraKey = $null # To store the Jira issue key if used

        if ($Issue) {
            $sourceJiraKey = $Issue.Key
            Write-Log -Message "Starting admin account creation process using Jira ticket $($sourceJiraKey)" -LogLevel "INFO"

            # --- Extract required data from Jira Issue ---
            # Identify custom field IDs based on the XML structure provided
            $cfFirstNameId = 'customfield_10304' # First Name
            $cfLastNameId = 'customfield_10305'  # Last Name
            $cfOfficeLocationId = 'customfield_10115' # Office Location
            $cfITAdminAccountId = 'customfield_10453' # IT Admin Account?
            # Optional fields for logging/info
            $cfDepartmentId = 'customfield_10120' # Department
            $cfJobTitleId = 'customfield_10238' # Job Title
            $cfModelAccountId = 'customfield_10343' # Model Account

            $firstName = Get-JiraCustomFieldValue -Issue $Issue -CustomFieldId $cfFirstNameId -FieldName "First Name"
            $lastName = Get-JiraCustomFieldValue -Issue $Issue -CustomFieldId $cfLastNameId -FieldName "Last Name"
            $officeLocation = Get-JiraCustomFieldValue -Issue $Issue -CustomFieldId $cfOfficeLocationId -FieldName "Office Location"
            $itAdminAccount = Get-JiraCustomFieldValue -Issue $Issue -CustomFieldId $cfITAdminAccountId -FieldName "IT Admin Account?"
            $department = Get-JiraCustomFieldValue -Issue $Issue -CustomFieldId $cfDepartmentId -FieldName "Department"
            $jobTitle = Get-JiraCustomFieldValue -Issue $Issue -CustomFieldId $cfJobTitleId -FieldName "Job Title"
            $modelAccount = Get-JiraCustomFieldValue -Issue $Issue -CustomFieldId $cfModelAccountId -FieldName "Model Account"

            # Validate extracted data
            if ([string]::IsNullOrWhiteSpace($firstName) -or [string]::IsNullOrWhiteSpace($lastName)) {
                throw "Required fields (First Name, Last Name) not found or empty in Jira ticket $($sourceJiraKey)."
            }
            if ($itAdminAccount -ne 'Yes') {
                 throw "'IT Admin Account?' field is not 'Yes' on Jira ticket $($sourceJiraKey). Aborting."
            }
            Write-Log -Message "Extracted data from Jira ticket $($sourceJiraKey): First Name='$firstName', Last Name='$lastName', Office Location='$officeLocation', IT Admin Account?='$itAdminAccount'" -LogLevel INFO
            if ($department) { Write-Log -Message "Extracted optional data: Department='$department'" -LogLevel INFO }
            if ($jobTitle) { Write-Log -Message "Extracted optional data: Job Title='$jobTitle'" -LogLevel INFO }
            if ($modelAccount) { Write-Log -Message "Extracted optional data: Model Account='$modelAccount'" -LogLevel INFO }

            # --- Determine Standard User UPN ---
            # Construct the expected standard UPN based on extracted names
            $standardUserUPN = New-StandardUPN -FirstName $firstName -LastName $lastName -Domain $script:DefaultDomain
            Write-Log -Message "Constructed expected standard user UPN: $standardUserUPN" -LogLevel INFO

        } else {
            Write-Log -Message "Starting admin account creation process (manual input)" -LogLevel "INFO"
            # --- Get standard user UPN manually ---
            $standardUserUPN = Get-ValidUPN -Domain $script:DefaultDomain
        }

        # Initialize logging with the standard user's UPN
        if ([string]::IsNullOrWhiteSpace($standardUserUPN)) {
            throw "Standard User UPN could not be determined (either from Jira or manual input)."
        }
        $logPath = Initialize-SecureLogging -StandardUserUPN $standardUserUPN
        # The initial "Starting admin account creation process" log is now conditional above.

        # Get standard user details
        $standardUser = Get-ADUserDetails -UserUPN $standardUserUPN

        if ($null -eq $standardUser) {
            throw "Unable to retrieve standard user details"
        }

        # Check if admin account already exists
        $adminSamAccountName = "$($standardUser.SamAccountName)-a"
        $adminAccountExists = Get-ADUser -Filter "SamAccountName -eq '$adminSamAccountName'" -ErrorAction SilentlyContinue

        if ($adminAccountExists) {
            # Admin account already exists - handle according to requirements
            $existingAdminUPN = $adminAccountExists.UserPrincipalName
            $warningMessage = "Admin account already exists for user $($standardUser.DisplayName): $existingAdminUPN"
            Write-Host "`n$warningMessage" -ForegroundColor Yellow
            Write-Log -Message $warningMessage -LogLevel "WARNING"

            # Add comment to Jira ticket if available (no log file attachment)
            if ($Issue) {
                $commentBody = @"
Admin Account Creation Attempt

An attempt was made to create an admin account for $($standardUser.DisplayName) ($standardUserUPN), but an admin account already exists.

Existing Admin Account: $existingAdminUPN
Created: $($adminAccountExists.Created)

No new account was created. If a new admin account is required, please contact IT Support to review the existing account.

Processed by: $env:USERNAME
Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
"@

                Write-Log -Message "Adding comment to Jira ticket $($Issue.Key) about existing admin account" -LogLevel "INFO"
                $commentResult = Add-EnhancedJiraComment -IssueKey $Issue.Key -CommentBody $commentBody -UseRichFormatting

                if ($commentResult) {
                    Write-Host "Comment added to Jira ticket $($Issue.Key) explaining that admin account already exists." -ForegroundColor Green
                } else {
                    Write-Host "Failed to add comment to Jira ticket $($Issue.Key)." -ForegroundColor Yellow
                }
            }

            # Exit the function since no new account needs to be created
            return
        }

        # --- Determine OU ---
        # Define OU Mappings (can be moved to a global/script scope if used elsewhere)
        $officeLocationToOU = @{ 
            "United Kingdom" = "OU=London,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com"
            "Singapore"      = "OU=Singapore,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com"
            # Add other mappings as needed: "Office Name from Jira" = "DistinguishedName of OU"
        }
    
        if ($Issue -and -not [string]::IsNullOrWhiteSpace($officeLocation) -and $officeLocationToOU.ContainsKey($officeLocation)) {
             # OU determined from Jira Office Location
             $ouPath = $officeLocationToOU[$officeLocation]
             Write-Log -Message "Mapped Office Location '$officeLocation' from Jira ticket to OU: $ouPath" -LogLevel INFO
        } else {
            # Fallback: Prompt user if Jira data is not available or mapping is not found
            if ($Issue -and -not [string]::IsNullOrWhiteSpace($officeLocation)) { Write-Log -Message "Office Location '$officeLocation' from Jira ticket is not mapped to an OU or is empty. Prompting user." -LogLevel WARNING }
            $ouPath = Select-OU # Revert to manual selection
        }
        $displayName = "$($standardUser.GivenName) $($standardUser.Surname) (Admin Account)" 
        $logonName = "$($standardUser.SamAccountName)-a"
        $adminUserUPN = "$logonName@$script:DefaultDomain"
        
        # Show account details that will be created
        Write-Host "`nNew Admin Account Details:" -ForegroundColor Yellow
        if ($Issue) {
            Write-Host "Source Jira Ticket: $($Issue.Key)" -ForegroundColor Cyan
            Write-Host "Source Jira Title: $($Issue.Summary)" -ForegroundColor Cyan
            # Consider if you want to show $Issue.Description here, it can be long.
            # Write-Host "Source Jira Description: $($Issue.Description)" -ForegroundColor Cyan 
            Write-Host "Extracted First Name: $firstName" -ForegroundColor Cyan
            Write-Host "Extracted Last Name: $lastName" -ForegroundColor Cyan
            Write-Host "Extracted Office Location: $officeLocation" -ForegroundColor Cyan
            # You can add other extracted fields like Department, Job Title if desired
        } 
        Write-Host "Standard User UPN: $standardUserUPN" -ForegroundColor Cyan
        Write-Host "Proposed Admin Display Name: $displayName" -ForegroundColor Cyan
        Write-Host "Proposed Admin Username: $adminUserUPN" -ForegroundColor Cyan
        Write-Host "Proposed Admin SAM Account Name: $logonName" -ForegroundColor Cyan
        Write-Host "Proposed OU Path: $ouPath" -ForegroundColor Cyan
        
        # Prompt for confirmation
        $confirmation = Read-Host "`nDo you want to create this admin account? (Y/N)"

        if ($confirmation -eq 'Y') {
            # Generate password and create account
            $password = New-SecurePassword
            $securePassword = ConvertTo-SecureString $password -AsPlainText -Force
            
            $newUserParams = @{
                Name = $displayName
                GivenName = $standardUser.GivenName 
                Surname = $standardUser.Surname
                DisplayName = $displayName
                Description = "Admin account for $($standardUser.DisplayName)"
                UserPrincipalName = $adminUserUPN
                SamAccountName = $logonName
                AccountPassword = $securePassword
                Path = $ouPath
                Enabled = $false
            }
            
            # Create the admin account
            New-ADUser @newUserParams -ErrorAction Stop

            $successMessage = "Admin account created successfully: $adminUserUPN"
            Write-Host "`n$successMessage" -ForegroundColor Green
            Write-Log -Message $successMessage -LogLevel "INFO"

            # Add comment and attachment to Jira ticket if available (for successful creation)
            if ($Issue) {
                $commentBody = @"
Admin Account Created Successfully

A new admin account has been created for $($standardUser.DisplayName) ($standardUserUPN).

New Admin Account Details:
- Username: $adminUserUPN
- Display Name: $displayName
- SAM Account Name: $logonName
- OU Path: $ouPath
- Account Status: Disabled (requires manual activation)

The detailed log file has been attached to this ticket for auditing purposes.

Processed by: $env:USERNAME
Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
"@

                Write-Log -Message "Adding comment and log file attachment to Jira ticket $($Issue.Key) for successful admin account creation" -LogLevel "INFO"

                # Add comment first with rich formatting
                $commentResult = Add-EnhancedJiraComment -IssueKey $Issue.Key -CommentBody $commentBody -UseRichFormatting

                # Add log file attachment with validation and progress
                $attachmentResult = Add-EnhancedJiraAttachment -IssueKey $Issue.Key -FilePath $script:CurrentLogPath -ValidateFile -ShowProgress

                if ($commentResult -and $attachmentResult) {
                    Write-Host "Comment and log file successfully added to Jira ticket $($Issue.Key)." -ForegroundColor Green
                } elseif ($commentResult) {
                    Write-Host "Comment added to Jira ticket $($Issue.Key), but log file attachment failed." -ForegroundColor Yellow 
                } elseif ($attachmentResult) {
                    Write-Host "Log file attached to Jira ticket $($Issue.Key), but comment addition failed." -ForegroundColor Yellow
                } else {
                    Write-Host "Failed to add both comment and log file attachment to Jira ticket $($Issue.Key)." -ForegroundColor Yellow
                }
            }

            # Send email notifications
            if (-not [string]::IsNullOrEmpty($standardUser.EmailAddress)) {
                $emailParams = @{
                    LogPath = $script:CurrentLogPath
                    AdminUserUPN = $adminUserUPN
                    Password = $password
                    StandardUserEmail = $standardUser.EmailAddress
                }
                Send-EmailNotification @emailParams
            } else {
                Write-Log -Message "No email address found for standard user, cannot send credentials." -LogLevel "WARNING"
            }

        }
        else {
            Write-Host "`nAccount creation cancelled by user" -ForegroundColor Yellow
            Write-Log -Message "Admin account creation cancelled by user" -LogLevel "INFO"
        }
    }
    catch {
        $errorMessage = "Failed to create admin account: $_"
        Write-Log -Message $errorMessage -LogLevel "ERROR"
        Write-Host $errorMessage -ForegroundColor Red
        throw
    }
}

function Remove-StdAdminAccount {
    [CmdletBinding()]
    param()

    try {
        # Get standard user UPN first (with ForDeletion flag)
        $standardUserUPN = Get-ValidUPN -Domain $script:DefaultDomain -ForDeletion
        
        # Initialize logging with the standard user's UPN
        $logPath = Initialize-SecureLogging -StandardUserUPN $standardUserUPN
        Write-Log -Message "Starting admin account deletion process" -LogLevel "INFO"

        # Get standard user details
        $standardUser = Get-ADUserDetails -UserUPN $standardUserUPN

        if ($null -eq $standardUser) {
            throw "Unable to retrieve standard user details"
        }

        # Construct admin account SamAccountName
        $adminSamAccountName = "$($standardUser.SamAccountName)-a"

        # Check if admin account exists and get full details
        $adminAccount = Get-ADUser -Filter "SamAccountName -eq '$adminSamAccountName'" `
                                 -Properties DisplayName, UserPrincipalName, DistinguishedName, Created, Modified
                                 -ErrorAction Stop

        if ($null -eq $adminAccount) {
            throw "No admin account found for user $($standardUser.DisplayName) (Expected: $adminSamAccountName)"
        }

        # Prompt for confirmation with detailed information
        Write-Host "`nAdmin Account Details:" -ForegroundColor Yellow
        Write-Host "Display Name: $($adminAccount.DisplayName)" -ForegroundColor Cyan
        Write-Host "Username: $($adminAccount.UserPrincipalName)" -ForegroundColor Cyan
        Write-Host "SAM Account Name: $($adminAccount.SamAccountName)" -ForegroundColor Cyan
        Write-Host "Distinguished Name: $($adminAccount.DistinguishedName)" -ForegroundColor Cyan
        Write-Host "Created: $($adminAccount.Created)" -ForegroundColor Cyan
        Write-Host "Last Modified: $($adminAccount.Modified)" -ForegroundColor Cyan

        $confirmation = Read-Host "`nAre you sure you want to delete this admin account? (Y/N)"

        if ($confirmation -eq 'Y') {
            # Remove the admin account
            Remove-ADUser -Identity $adminAccount.DistinguishedName -Confirm:$false -ErrorAction Stop

            $successMessage = "Admin account $($adminAccount.UserPrincipalName) has been successfully deleted"
            Write-Host "`n$successMessage" -ForegroundColor Green
            Write-Log -Message $successMessage -LogLevel "INFO"

            # Send email notifications using Send-EmailNotification
           # Replace the email notification section in Remove-StdAdminAccount with:
if (-not [string]::IsNullOrEmpty($standardUser.EmailAddress)) {
    $emailParams = @{
        LogPath = $script:CurrentLogPath
        AdminUserUPN = $adminAccount.UserPrincipalName
        StandardUserEmail = $standardUser.EmailAddress
    }
    Send-DeletionEmailNotification @emailParams
}
else {
    Write-Log -Message "No email address found for standard user" -LogLevel "WARNING"
}
        }
        else {
            Write-Host "`nDeletion cancelled by user" -ForegroundColor Yellow
            Write-Log -Message "Admin account deletion cancelled by user" -LogLevel "INFO"
        }
    }
    catch {
        $errorMessage = "Failed to delete admin account: $_"
        Write-Log -Message $errorMessage -LogLevel "ERROR"
        Write-Host $errorMessage -ForegroundColor Red
        throw
    }
}


#region Main Script Execution

<#
.SYNOPSIS
Main script execution with enhanced security and error handling.

.DESCRIPTION
Initializes the script environment, loads configuration, sets up secure logging,
and executes the appropriate admin account operation.
#>

# Main script execution
try {
    Write-Progress -Activity "Starting Admin Account Script" -Status "Initializing..." -PercentComplete 0

    # Initialize configuration with smart defaults
    try {
        Initialize-SmartConfiguration -ConfigPath $ConfigPath
        Write-Progress -Activity "Starting Admin Account Script" -Status "Configuration loaded" -PercentComplete 20
    }
    catch {
        Write-Warning "Failed to load configuration, using defaults: $($_.Exception.Message)"
    }

    # Set default domain from configuration or fallback
    $script:DefaultDomain = if ($script:Config) {
        $script:Config.ScriptSettings.DefaultDomain
    } else {
        "jeragm.com"
    }

    Write-Progress -Activity "Starting Admin Account Script" -Status "Setting up credentials..." -PercentComplete 40

    # Initialize Jira credentials and connection
    $jiraConnected = $false
    if (-not $SkipJiraIntegration) {
        try {
            # Get Jira credentials securely
            $jiraUsername = Get-SecureCredential -CredentialName "AdminScript-JiraUsername" -Purpose "Jira API access"
            $jiraApiToken = Get-SecureCredential -CredentialName "AdminScript-JiraApiToken" -Purpose "Jira API token"

            if ($jiraUsername -and $jiraApiToken) {
                # Set script variables for Jira connection
                $script:JiraUsername = if ($jiraUsername -is [PSCredential]) { $jiraUsername.UserName } else { $jiraUsername }
                $script:JiraApiToken = if ($jiraApiToken -is [PSCredential]) { $jiraApiToken.GetNetworkCredential().Password } else { $jiraApiToken }
                $script:JiraServerUrl = if ($script:Config) { $script:Config.Jira.ServerUrl } else { "https://jeragm.atlassian.net" }

                if (Initialize-JiraConnection -JiraUrl $script:JiraServerUrl -JiraUsername $script:JiraUsername -JiraApiToken $script:JiraApiToken) {
                    Write-SecureLog -Message "Successfully connected to Jira for ticket validation." -LogLevel "INFO"
                    $jiraConnected = $true
                } else {
                    Write-Host "WARNING: Failed to connect to Jira. Jira ticket validation will be skipped." -ForegroundColor Yellow
                    Write-SecureLog -Message "Jira connection failed. Jira validation will be skipped." -LogLevel "WARNING"
                }
            } else {
                Write-Host "Jira credentials not available. Skipping Jira connection and validation." -ForegroundColor Yellow
                Write-SecureLog -Message "Jira credentials not available. Jira connection and validation will be skipped." -LogLevel "INFO"
            }
        }
        catch {
            Write-Host "Error setting up Jira connection: $($_.Exception.Message)" -ForegroundColor Yellow
            Write-SecureLog -Message "Error setting up Jira connection: $($_.Exception.Message)" -LogLevel "WARNING"
        }
    } else {
        Write-Host "Jira integration skipped as requested." -ForegroundColor Yellow
        Write-SecureLog -Message "Jira integration skipped via command line parameter." -LogLevel "INFO"
    }

    Write-Progress -Activity "Starting Admin Account Script" -Status "Ready to process operations" -PercentComplete 60

    # Prompt user for action
    Write-Host "Select an action:"
    Write-Host "1. Create Admin Account" 
    Write-Host "2. Delete Admin Account"
    Write-Host "3. Reset Admin Account Password"
    $choice = Read-Host "`nEnter your choice (1, 2, or 3). Type 'Exit' to quit."

    if ($choice -eq 'Exit') {
        Write-Host "Exiting script."
        exit
    }

    switch ($choice) {
        "1" {
            if ($jiraConnected) {
                $jiraTicket = Read-Host "Enter Jira Ticket ID for 'Create Admin Account' (e.g., ITSD-1234). Leave blank to skip."
                if (-not [string]::IsNullOrWhiteSpace($jiraTicket)) {
                    $issue = Test-JiraTicketValidation -IssueKey $jiraTicket -ExpectedWorkType "Service Request with Approvals" -ExpectedRequestType "JERAGM Onboarding Request"
                    if ($issue -is [System.Management.Automation.PSObject]) { # Check if an issue object was returned
                        New-AdminAccount -Issue $issue
                    } else {
                        Write-Host "Jira ticket validation failed for $jiraTicket. Aborting account creation." -ForegroundColor Red
                        Write-Log -Message "Jira ticket validation failed for '$jiraTicket' (Create Admin Account). Expected IssueType: 'Service Request with Approvals', RequestType: 'JERAGM Onboarding Request'." -LogLevel WARNING
                    }
                } else {
                    Write-Host "No Jira ticket provided. Proceeding with account creation without Jira validation." -ForegroundColor Yellow
                    New-AdminAccount # Call without -Issue parameter
                }
            } else {
                Write-Host "Jira is not connected. Proceeding with Admin Account creation without Jira validation." -ForegroundColor Yellow 
                New-AdminAccount # Call without -Issue parameter
            }
        }
        "2" { 
            if ($jiraConnected) {
                $jiraTicket = Read-Host "Enter Jira Ticket ID for 'Delete Admin Account' (e.g., ITSD-1234). Leave blank to skip."
                if (-not [string]::IsNullOrWhiteSpace($jiraTicket)) {
                    if (Test-JiraTicketValidation -IssueKey $jiraTicket -ExpectedWorkType "Service Request with Approvals" -ExpectedRequestType "Remove Admin Account") { # Adjust ExpectedRequestType for deletion if needed
                        Remove-StdAdminAccount
                    } else {
                        Write-Host "Jira ticket validation failed for $jiraTicket or no ticket provided. Aborting account deletion." -ForegroundColor Red
                        Write-Log -Message "Jira ticket validation failed for '$jiraTicket' (Delete Admin Account). Expected IssueType: 'Service Request with Approvals', RequestType: 'Remove Admin Account'." -LogLevel WARNING
                    }
                } else {
                    Write-Host "No Jira ticket provided. Proceeding with account deletion without Jira validation." -ForegroundColor Yellow
                    Remove-StdAdminAccount
                }
            } else {
                Write-Host "Jira is not connected. Proceeding with Admin Account deletion without Jira validation." -ForegroundColor Yellow
                Remove-StdAdminAccount
             } 
        }
        "3" { Reset-StdAdminAccount }
        default { 
            Write-Host "Invalid choice. Please select 1, 2, or 3." -ForegroundColor Red 
            exit
        }
    }
}
catch [System.UnauthorizedAccessException] {
    $errorMsg = "Insufficient permissions to execute script operations. Please check your Active Directory and file system permissions."
    Write-Host $errorMsg -ForegroundColor Red
    Write-SecureLog -Message "Script execution failed due to insufficient permissions: $($_.Exception.Message)" -LogLevel "CRITICAL"
    exit 1
}
catch [System.IO.FileNotFoundException] {
    $errorMsg = "Required file not found: $($_.Exception.Message). Please check configuration and file paths."
    Write-Host $errorMsg -ForegroundColor Red
    Write-SecureLog -Message "Script execution failed due to missing file: $($_.Exception.Message)" -LogLevel "CRITICAL"
    exit 1
}
catch [Microsoft.ActiveDirectory.Management.ADException] {
    $errorMsg = "Active Directory operation failed: $($_.Exception.Message)"
    Write-Host $errorMsg -ForegroundColor Red
    Write-SecureLog -Message "Script execution failed due to AD error: $($_.Exception.Message)" -LogLevel "CRITICAL"
    exit 1
}
catch {
    $errorMsg = "Script execution failed with unexpected error: $($_.Exception.Message)"
    Write-Host $errorMsg -ForegroundColor Red
    Write-SecureLog -Message "Script execution failed with unexpected error: $($_.Exception.Message)" -LogLevel "CRITICAL" -AuditTrail @{
        ErrorType = $_.Exception.GetType().Name
        StackTrace = $_.ScriptStackTrace
    }
    exit 1
}
finally {
    # Cleanup and final logging
    if ($script:CurrentLogPath) {
        Write-SecureLog -Message "Script execution completed. Log file: $(Split-Path $script:CurrentLogPath -Leaf)" -LogLevel "INFO" -AuditTrail @{
            ExecutionTime = (Get-Date) - $script:ExecutionContext.StartTime
            FinalStatus = if ($?) { "Success" } else { "Failed" }
        }
    }

    Write-Host "`nScript execution completed." -ForegroundColor Green
    Write-Host "For detailed information, check the log file: $script:CurrentLogPath" -ForegroundColor Cyan

    if (-not $SkipJiraIntegration) {
        Read-Host -Prompt "Press Enter to exit"
    }
}

#endregion