﻿<#
.SYNOPSIS
Creates an admin account based on a standard user's details.

.DESCRIPTION
This script automates the process of creating an admin account in Active Directory based on the details of a standard user. It includes validation of user inputs, logging actions, generating passwords, and sending email notifications.

.NOTES
Version:        1.0
Author:         <PERSON>
Creation Date:  2023-04-01
#>

# Import required modules
Import-Module ActiveDirectory

# Sends an email notification with details about the admin account creation
function Send-EmailNotification {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$LogPath,

        [Parameter(Mandatory = $true)]
        [string]$AdminUserUPN,

        [string]$EmailFrom = "<EMAIL>",
        [string]$EmailTo = "<EMAIL>",
        [string]$SmtpServer = "smtp.jeragm.com"
    )

    try {
        Send-MailMessage -From $EmailFrom -To $EmailTo -Subject "Admin Account Creation for $AdminUserUPN" -Body "An admin account has been created for $AdminUserUPN by $env:USERNAME. Please find the attached log for more details." -SmtpServer $SmtpServer -Attachments $LogPath -BodyAsHtml
        Write-Host "Email notification sent by $env:USERNAME."
    } catch {
    Write-Log -Message "Error sending email: $_" -Level "ERROR"
}
}

# Writes a log message with a timestamp and the current user's name
function Write-Log {
    param (
        [Parameter(Mandatory = $true)]
        [string]$Message,

        [Parameter(Mandatory = $false)]
        [ValidateSet("DEBUG", "INFO", "WARNING", "ERROR", "VERBOSE")]
        [string]$Level = "INFO",

        [string]$Path = "C:\Temp\Scripts\Desktop Support\Logs\AdminAccountCreation.log"
    )

    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] [$env:USERNAME] $Message"

    # Only log DEBUG or VERBOSE messages if a certain condition is met (e.g., a debug flag is set)
    $shouldLog = $true
    if ($Level -eq "DEBUG" -or $Level -eq "VERBOSE") {
        # Example condition: check for a global debug flag
        # This part is for illustration; implement the condition based on your needs
        $shouldLog = $global:DebugEnabled
    }

    if ($shouldLog) {
        Add-Content -Path $Path -Value $logMessage
    }

    Write-Host $logMessage
}

# Validates the UPN format and appends domain if necessary
function Get-ValidUPN {
    do {
        $upn = Read-Host "Enter the UPN of the standard account. E.g., user.user"
        if (-not $upn -match "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$") {
            Write-Host "Invalid UPN format. Please enter a valid UPN."
            Write-Log -Message "Invalid UPN format entered: $upn"
            $isValid = $false
        } else {
            $isValid = $true
            if (-not $upn -like "*@jeragm.com") {
                $upn += "@jeragm.com"
                Write-Log -Message "Appended domain to UPN: $upn"
            }
        }
    } while (-not $isValid)
    return $upn
}

# Retrieves user details from Active Directory
function Get-ADUserDetails {
    param (
        [Parameter(Mandatory = $true)]
        [string]$UserUPN
    )

    # Validate UPN format
    if (-not $UserUPN -match "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$") {
        Write-Log -Message "Invalid UPN format: $UserUPN" -Level "ERROR"
        return $null
    }

    # Connectivity check
    try {
        Get-ADDomainController -ErrorAction Stop
    } catch {
        Write-Log -Message "Failed to connect to any domain controller: $_" -Level "ERROR"
        return $null
    }

    try {
        $user = Get-ADUser -Filter "UserPrincipalName -eq '$UserUPN'" -Properties *
        
        # Check for null or empty user details
        if ($null -eq $user -or $user.SamAccountName -eq $null) {
            Write-Log -Message "Critical user details missing for UPN: $UserUPN" -Level "ERROR"
            return $null
        }

        Write-Log -Message "Retrieved AD user details for UPN: $UserUPN"
        return $user
    } catch {
        if ($_.Exception -match "Active Directory operation failed") {
            Write-Log -Message "Connectivity or permission issue while retrieving AD user details for UPN: $UserUPN. Error: $_" -Level "ERROR"
        } elseif ($_.Exception -match "cannot find an object") {
            Write-Log -Message "User not found in AD for UPN: $UserUPN. Error: $_" -Level "WARNING"
        } else {
            Write-Log -Message "Unexpected error while retrieving AD user details for UPN: $UserUPN. Error: $_" -Level "ERROR"
        }
        return $null
    }
}

# Allows user to select an OU for the admin account
function Select-OU {
    do {
        Write-Host "Select the OU for the admin account: 1 for London, 2 for Singapore."
        $selection = Read-Host "Enter 1 or 2"
        switch ($selection) {
            "1" { $ouPath = "OU=London,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com"; Write-Log -Message "Selected OU: London" }
            "2" { $ouPath = "OU=Singapore,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com"; Write-Log -Message "Selected OU: Singapore" }
            
            default {
                Write-Host "Invalid selection. Please select either 1 or 2."
                Write-Log -Message "Invalid OU selection: $selection"
            }
        }
    } while (-not $ouPath)
    return $ouPath
}

# Generates a secure password
function Generate-Password {
    Add-Type -AssemblyName System.Web
    $password = [System.Web.Security.Membership]::GeneratePassword(12, 2)
    Write-Log -Message "Generated password"
    return $password
}

# Orchestrates the admin account creation process
function Create-AdminAccount {
    <#
    .SYNOPSIS
    Automates the process of creating an admin account in Active Directory based on the details of a standard user.

    .DESCRIPTION
    The Create-AdminAccount function performs various validation checks, generates a secure password, selects an OU for the admin account, and sends an email notification with the account details.

    .EXAMPLE
    Create-AdminAccount

    This code will execute the Create-AdminAccount function, which will prompt the user to enter the UPN of the standard account. It will then retrieve the user details from Active Directory, select an OU for the admin account, generate a secure password, and create the admin account. Finally, it will display the password and send an email notification with the account details.

    .NOTES
    Author: PowerShell Team
    Date:   2022-01-01
    #>

    Write-Log -Message "Starting admin account creation process"
    $accountCreated = $false
try {
    Get-ADDomainController -ErrorAction Stop
} catch {
    Write-Log -Message "Failed to connect to any domain controller: $_" -Level "ERROR"
    Write-Host "Failed to connect to Active Directory. Please check your network connection and try again."
    return
}

    try {

        $standardUserUPN = Get-ValidUPN
        $standardUser = Get-ADUserDetails -UserUPN $standardUserUPN

        if ($standardUser -eq $null) {
            Write-Host "Unable to proceed without valid standard user details."
            return
        }

        $ouPath = Select-OU
        if (-not $ouPath) {
            Write-Host "Unable to proceed without selecting an OU."
            return
        }

        $displayName = "$($standardUser.GivenName) $($standardUser.Surname) (Admin Account)"
        $logonName = $standardUser.SamAccountName + "-a"
        $adminUserUPN = "$<EMAIL>"

        if (Get-ADUser -Filter "SamAccountName -eq '$logonName'") {
            Write-Host "An admin account with the same SamAccountName already exists."
            return
        }

        $password = Generate-Password

        $newUserParams = @{
            Name = $displayName
            GivenName = $standardUser.GivenName
            Surname = $standardUser.Surname
            DisplayName = $displayName
            Description = $displayName
            UserPrincipalName = $adminUserUPN
            SamAccountName = $logonName
            AccountPassword = (ConvertTo-SecureString $password -AsPlainText -Force)
            Path = $ouPath
            Enabled = $false
        }

        $newUser = New-ADUser @newUserParams
        $accountCreated = $true

        if ($newUser) {
            Write-Host "The password for the admin account ($adminUserUPN) is: $password. Note: The account is created as disabled."
            Send-EmailNotification -LogPath "C:\Temp\Scripts\Desktop Support\Logs\AdminAccountCreation.log" -AdminUserUPN $adminUserUPN
        } else {
            Write-Host "Failed to create the admin account."
        }
    } catch {
        Write-Log -Message "Failed to create the admin account. Error: $_" -Level "ERROR"
        if ($accountCreated) {
            Write-Log -Message "Attempting to rollback the admin account creation due to errors." -Level "WARNING"
            # Implement the rollback or cleanup logic here
            # For example, if the account was created but enabling it failed, you might want to disable or delete the account
            # Disable-ADAccount -Identity $logonName
            # or
            # Remove-ADUser -Identity $logonName
        }
    } finally {
        # Any final cleanup actions that should always be performed, success or failure
        Write-Log -Message "Admin account creation process completed." -Level "INFO"
    }
}


# Main script execution
Create-AdminAccount

# Prompt to exit
Read-Host -Prompt "Press Enter to exit"
